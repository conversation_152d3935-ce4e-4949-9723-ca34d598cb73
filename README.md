# MAS: Modular Multi-Agent System with LangGraph

A flexible, scalable multi-agent system built on LangGraph for orchestrating complex workflows through specialized AI agents. The system integrates with Jira, Slack, Bitbucket, and Kubernetes.

## Features

- **Modular Agent Architecture**: Specialized agents for different tasks
- **LangGraph Orchestration**: Sophisticated agent coordination and workflow management
- **Integration Layer**: Connect with external systems via APIs
- **Structured Output**: Reliable structured output generation using [TrustCall](https://github.com/hinthornw/trustcall)
- **Langfuse Observability**: Monitoring and debugging of LLM interactions
- **Asynchronous Execution**: All operations are asynchronous for better performance
- **Error Handling**: Robust error recovery mechanisms
- **Dynamic Agent Selection**: Agents are loaded based on available credentials

## System Architecture

![Architecture Diagram](docs/architecture.png)

### Components

1. **Core Layer**: GraphManager and AgentState for workflow management
2. **Agent Layer**: Specialized agents for different tasks and domains
3. **Integration Layer**: Clients for external service APIs
4. **Orchestration Layer**: WorkflowBuilder for creating and running workflows
5. **Model Layer**: Pydantic models for structured LLM outputs
6. **Workflow Layer**: Domain-specific pre-configured workflows
7. **Utilities Layer**: Logging, LLM providers, and observability tools

## Agents

The system includes several specialized agents:

- **Supervisor Agent**: Coordinates workflows and delegates tasks
- **Jira Agent**: Interacts with Jira (create/update/query tickets)
- **Slack Agent**: Handles Slack notifications and commands
- **Bitbucket Agent**: Manages repositories and pull requests
- **Kubernetes Agent**: Deploys and monitors resources
- **Error Handler Agent**: Provides error analysis and recovery strategies
- **Processing Agent**: Handles data processing and transformation

## Installation

### Prerequisites

- Python 3.10+
- Redis (optional, for state persistence)
- OpenAI API key or Azure OpenAI service 

### Local Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/mas.git
   cd mas
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -e .
   ```

4. Set up environment variables:
   ```bash
   export OPENAI_API_KEY=your_openai_api_key
   # Add other environment variables as needed
   ```

## Usage

### CLI

Run the system via command line:

```bash
python -m main --message "Create a Jira ticket for the new feature"
```

Optional arguments:
```
--jira-url            # Jira URL
--jira-username       # Jira username
--jira-api-token      # Jira API token
--slack-token         # Slack token
--bitbucket-username  # Bitbucket username
--bitbucket-app-password  # Bitbucket app password
--kubeconfig-path     # Kubernetes config path
```

### API Server

Start the API server:

```bash
python -m server
```

Make requests to the API:

```bash
curl -X POST http://localhost:8000/agent \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{"message": "Create a Jira ticket for the new feature"}'
```

## Docker Deployment

Build and run using Docker:

```bash
docker build -t mas:latest .
docker run -p 8000:8000 \
  -e OPENAI_API_KEY=your_openai_api_key \
  -e API_KEY=your_api_key \
  mas:latest
```

## Configuration

Configuration is done through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | *Required* |
| `MODEL_NAME` | LLM model to use | azure_model_2 |
| `LLM_TEMPERATURE` | Temperature for LLM generation | 0 |
| `REDIS_URL` | Redis URL for state persistence | *Optional* |
| `JIRA_URL` | Jira instance URL | *Optional* |
| `JIRA_USERNAME` | Jira username | *Optional* |
| `JIRA_API_TOKEN` | Jira API token | *Optional* |
| `SLACK_TOKEN` | Slack API token | *Optional* |
| `SLACK_DEFAULT_CHANNEL` | Default Slack channel | general |
| `BITBUCKET_USERNAME` | Bitbucket username | *Optional* |
| `BITBUCKET_PASSWORD` | Bitbucket app password | *Optional* |
| `BITBUCKET_WORKSPACE` | Default Bitbucket workspace | *Optional* |
| `KUBECONFIG_PATH` | Path to Kubernetes config | ~/.kube/config |
| `K8S_CONTEXT` | Kubernetes context | *Optional* |
| `K8S_NAMESPACE` | Default Kubernetes namespace | default |

## Development

### Project Structure

```
mas/
├── agents/              # Specialized agents
│   ├── global_agent/    # Supervisor and error handling agents
│   ├── jira/            # Jira agents
│   ├── messaging/       # Slack and messaging agents
│   ├── bitbucket/       # Bitbucket agents
│   └── k8s/             # Kubernetes agents
├── api/                 # API server components
├── core/                # Core LangGraph components
├── integrations/        # External service clients
├── models/              # Pydantic data models
├── orchestration/       # Workflow orchestration
├── workflow/            # Pre-configured workflows
└── utils/               # Utility functions
```

### Adding New Agents

1. Create a new agent directory in `mas/agents/`
2. Create a new agent class inheriting from `BaseAgent` or `BaseAgentLLM`
3. Implement the `execute` method
4. Register the agent in the WorkflowBuilder

### Testing

Run the test suite:

```bash
pytest
```

## Troubleshooting

- **API Connection Issues**: Check API keys and network connectivity
- **Agent Errors**: Enable DEBUG logging for detailed error information
- **Redis Connection**: Verify Redis is running and URL is correct
- **LLM Rate Limits**: Check for rate limit errors in the logs

## License

Apache License 2.0

## Credits

- [LangGraph](https://github.com/langchain-ai/langgraph)
- [LangChain](https://github.com/langchain-ai/langchain)
- [OpenAI](https://openai.com/)
- [TrustCall](https://github.com/hinthornw/trustcall)
- [Langfuse](https://github.com/langfuse/langfuse)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.