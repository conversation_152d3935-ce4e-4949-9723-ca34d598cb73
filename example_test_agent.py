"""
Test script for the search agent
"""

import asyncio
import os
from dotenv import load_dotenv
from langgraph.checkpoint.memory import MemoryCheckpointSaver

# Load environment variables
load_dotenv()

# Import your workflow
from search_agent.workflow.search_agent.search_agent import base_workflow


async def test_search_agent():
    """Test the search agent with a sample query"""
    
    # Create a memory checkpointer for testing
    checkpointer = MemoryCheckpointSaver()
    
    # Create the workflow
    workflow = base_workflow(checkpointer)
    
    # Test configuration
    config = {
        "configurable": {
            "thread_id": "test-thread-1"
        }
    }
    
    # Test search query
    test_query = "What are the latest developments in artificial intelligence?"
    
    print(f"🔍 Testing search agent with query: {test_query}")
    
    # Create initial state
    initial_state = {
        "messages": [
            {
                "role": "user",
                "content": test_query
            }
        ]
    }
    
    try:
        # Run the workflow
        result = await workflow.ainvoke(initial_state, config)
        
        print("\n✅ Search completed!")
        print("\n📋 Results:")
        for message in result["messages"]:
            print(f"\n{message['role'].upper()}: {message['content']}")
            
    except Exception as e:
        print(f"❌ Error running search agent: {e}")


async def test_news_search():
    """Test the search agent with a news query"""
    
    checkpointer = MemoryCheckpointSaver()
    workflow = base_workflow(checkpointer)
    
    config = {
        "configurable": {
            "thread_id": "test-thread-2"
        }
    }
    
    news_query = "Latest news about climate change"
    
    print(f"\n📰 Testing news search with query: {news_query}")
    
    initial_state = {
        "messages": [
            {
                "role": "user", 
                "content": f"Search for recent news about: {news_query}"
            }
        ]
    }
    
    try:
        result = await workflow.ainvoke(initial_state, config)
        
        print("\n✅ News search completed!")
        print("\n📋 Results:")
        for message in result["messages"]:
            print(f"\n{message['role'].upper()}: {message['content']}")
            
    except Exception as e:
        print(f"❌ Error running news search: {e}")


if __name__ == "__main__":
    print("🚀 Starting search agent tests...")
    
    # Run the tests
    asyncio.run(test_search_agent())
    asyncio.run(test_news_search())
    
    print("\n🎉 Tests completed!")
