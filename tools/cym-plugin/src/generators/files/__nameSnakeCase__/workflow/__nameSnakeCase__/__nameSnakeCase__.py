
import os
from langgraph.checkpoint.base import BaseCheckpointSaver


from <%= nameSnakeCase %>.agents.supervisor_agent import SupervisorAgent
from logger import logger

from langgraph.graph import START, END

from corelanggraph import EnhancedStateGraph
from langgraph.graph.state import CompiledStateGraph

from <%= nameSnakeCase %>.models.agent_state import AgentState




def base_workflow(checkpointer: BaseCheckpointSaver) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(AgentState)
        
    supervisor_agent = SupervisorAgent(
        model_name=os.getenv('MODEL_NAME','azure_model_2'),
        temperature=0.1,
        available_agents=[
            
        ]
    )
    



    workflow_builder.add_node("supervisor_agent", supervisor_agent)


    
    workflow_builder.add_edge(START , "supervisor_agent")


    return workflow_builder.compile(checkpointer=checkpointer)
