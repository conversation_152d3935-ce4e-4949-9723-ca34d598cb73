import os
from typing import Any, Dict
import uuid

from <%= nameSnakeCase %>.handlers.<%= nameSnakeCase %>_handler import <%= namePascalCase %>Handler,<%= namePascalCase %>Input
from ..infra import init
from kafka.manager import KafkaManager
from confluent_kafka.cimpl import Message
from logger import logger

env = os.environ.get("ENV", "local")
group_id = uuid.uuid4() if env == "local" else f"siem_rules"

kafka_manager = KafkaManager(config=init.secret.kafka, group_id=group_id)



@kafka_manager.kafka_event(topics="<%= nameSnakeCase %>.topic_name")
async def <%= nameSnakeCase %>_handler(data: Dict[str, Any], message: Message):
    # Add UUID to the message data
    handler = <%= namePascalCase %>Handler()
    await handler.run(<%= namePascalCase %>Input(**data))




if __name__ == "__main__":
    kafka_manager.start_consuming()
