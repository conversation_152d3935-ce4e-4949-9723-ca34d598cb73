# RestInsightReportData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**title** | **str** |  | [optional] 
**type** | **str** |  | [optional] 
**value** | **object** |  | [optional] 

## Example

```python
from .models.rest_insight_report_data import RestInsightReportData

# TODO update the JSON string below
json = "{}"
# create an instance of RestInsightReportData from a JSON string
rest_insight_report_data_instance = RestInsightReportData.from_json(json)
# print the JSON string representation of the object
print(RestInsightReportData.to_json())

# convert the object into a dict
rest_insight_report_data_dict = rest_insight_report_data_instance.to_dict()
# create an instance of RestInsightReportData from a dict
rest_insight_report_data_from_dict = RestInsightReportData.from_dict(rest_insight_report_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


