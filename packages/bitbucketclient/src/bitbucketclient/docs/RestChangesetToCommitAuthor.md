# RestChangesetToCommitAuthor


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**avatar_url** | **str** |  | [optional] 
**email_address** | **str** |  | [optional] 
**name** | **str** |  | [optional] 

## Example

```python
from .models.rest_changeset_to_commit_author import RestChangesetToCommitAuthor

# TODO update the JSON string below
json = "{}"
# create an instance of RestChangesetToCommitAuthor from a JSON string
rest_changeset_to_commit_author_instance = RestChangesetToCommitAuthor.from_json(json)
# print the JSON string representation of the object
print(RestChangesetToCommitAuthor.to_json())

# convert the object into a dict
rest_changeset_to_commit_author_dict = rest_changeset_to_commit_author_instance.to_dict()
# create an instance of RestChangesetToCommitAuthor from a dict
rest_changeset_to_commit_author_from_dict = RestChangesetToCommitAuthor.from_dict(rest_changeset_to_commit_author_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


