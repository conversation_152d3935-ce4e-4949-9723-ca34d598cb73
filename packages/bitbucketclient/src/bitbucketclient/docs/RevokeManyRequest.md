# RevokeManyRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**projects** | [**RestProject**](RestProject.md) |  | [optional] 
**repositories** | [**RestRepository**](RestRepository.md) |  | [optional] 

## Example

```python
from .models.revoke_many_request import RevokeManyRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RevokeManyRequest from a JSON string
revoke_many_request_instance = RevokeManyRequest.from_json(json)
# print the JSON string representation of the object
print(RevokeManyRequest.to_json())

# convert the object into a dict
revoke_many_request_dict = revoke_many_request_instance.to_dict()
# create an instance of RevokeManyRequest from a dict
revoke_many_request_from_dict = RevokeManyRequest.from_dict(revoke_many_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


