# RestHookScriptConfigScript


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**created_date** | **datetime** |  | [optional] 
**description** | **str** |  | [optional] 
**id** | **int** |  | [optional] 
**name** | **str** |  | [optional] 
**plugin_key** | **str** |  | [optional] 
**type** | **str** |  | [optional] 
**updated_date** | **datetime** |  | [optional] 
**version** | **int** |  | [optional] 

## Example

```python
from .models.rest_hook_script_config_script import RestHookScriptConfigScript

# TODO update the JSON string below
json = "{}"
# create an instance of RestHookScriptConfigScript from a JSON string
rest_hook_script_config_script_instance = RestHookScriptConfigScript.from_json(json)
# print the JSON string representation of the object
print(RestHookScriptConfigScript.to_json())

# convert the object into a dict
rest_hook_script_config_script_dict = rest_hook_script_config_script_instance.to_dict()
# create an instance of RestHookScriptConfigScript from a dict
rest_hook_script_config_script_from_dict = RestHookScriptConfigScript.from_dict(rest_hook_script_config_script_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


