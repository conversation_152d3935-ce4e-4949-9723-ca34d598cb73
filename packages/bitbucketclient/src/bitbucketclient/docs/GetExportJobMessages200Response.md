# GetExportJobMessages200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**is_last_page** | **bool** |  | [optional] 
**limit** | **float** |  | [optional] 
**next_page_start** | **int** |  | [optional] 
**size** | **float** |  | [optional] 
**start** | **int** |  | [optional] 
**values** | [**List[RestJobMessage]**](RestJobMessage.md) |  | [optional] 

## Example

```python
from .models.get_export_job_messages200_response import GetExportJobMessages200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetExportJobMessages200Response from a JSON string
get_export_job_messages200_response_instance = GetExportJobMessages200Response.from_json(json)
# print the JSON string representation of the object
print(GetExportJobMessages200Response.to_json())

# convert the object into a dict
get_export_job_messages200_response_dict = get_export_job_messages200_response_instance.to_dict()
# create an instance of GetExportJobMessages200Response from a dict
get_export_job_messages200_response_from_dict = GetExportJobMessages200Response.from_dict(get_export_job_messages200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


