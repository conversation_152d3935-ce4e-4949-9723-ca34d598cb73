# RestInsightAnnotation


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**external_id** | **str** |  | [optional] 
**line** | **int** |  | [optional] 
**link** | **str** |  | [optional] 
**message** | **str** |  | [optional] 
**path** | **str** |  | [optional] 
**report_key** | **str** |  | [optional] 
**severity** | **str** |  | [optional] 
**type** | **str** |  | [optional] 

## Example

```python
from .models.rest_insight_annotation import RestInsightAnnotation

# TODO update the JSON string below
json = "{}"
# create an instance of RestInsightAnnotation from a JSON string
rest_insight_annotation_instance = RestInsightAnnotation.from_json(json)
# print the JSON string representation of the object
print(RestInsightAnnotation.to_json())

# convert the object into a dict
rest_insight_annotation_dict = rest_insight_annotation_instance.to_dict()
# create an instance of RestInsightAnnotation from a dict
rest_insight_annotation_from_dict = RestInsightAnnotation.from_dict(rest_insight_annotation_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


