# RestRequiredBuildCondition


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**build_parent_keys** | **List[str]** | A non-empty list of build parent keys that require green builds for this merge check to pass | [optional] 
**exempt_ref_matcher** | [**UpdatePullRequestCondition1RequestSourceMatcher**](UpdatePullRequestCondition1RequestSourceMatcher.md) |  | [optional] 
**id** | **int** |  | [optional] 
**ref_matcher** | [**UpdatePullRequestCondition1RequestSourceMatcher**](UpdatePullRequestCondition1RequestSourceMatcher.md) |  | [optional] 

## Example

```python
from .models.rest_required_build_condition import RestRequiredBuildCondition

# TODO update the JSON string below
json = "{}"
# create an instance of RestRequiredBuildCondition from a JSON string
rest_required_build_condition_instance = RestRequiredBuildCondition.from_json(json)
# print the JSON string representation of the object
print(RestRequiredBuildCondition.to_json())

# convert the object into a dict
rest_required_build_condition_dict = rest_required_build_condition_instance.to_dict()
# create an instance of RestRequiredBuildCondition from a dict
rest_required_build_condition_from_dict = RestRequiredBuildCondition.from_dict(rest_required_build_condition_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


