# Credentials


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**password** | **str** |  | 
**username** | **str** |  | 
**token** | **str** |  | 
**algorithm** | **str** | The key algorithm, if passing in a legacy X.509 encoded key. Do not specify for OpenSSH encoded keys | [optional] 
**public_key** | **str** | The public key text in the OpenSSH format. The algorithm must be specified in case of the legacy X.509 keys | 

## Example

```python
from .models.credentials import Credentials

# TODO update the JSON string below
json = "{}"
# create an instance of Credentials from a JSON string
credentials_instance = Credentials.from_json(json)
# print the JSON string representation of the object
print(Credentials.to_json())

# convert the object into a dict
credentials_dict = credentials_instance.to_dict()
# create an instance of Credentials from a dict
credentials_from_dict = Credentials.from_dict(credentials_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


