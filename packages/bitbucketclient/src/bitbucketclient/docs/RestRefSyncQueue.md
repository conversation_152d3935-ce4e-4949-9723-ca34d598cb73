# RestRefSyncQueue


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**values** | [**List[RestFarmSynchronizationRequest]**](RestFarmSynchronizationRequest.md) |  | [optional] 

## Example

```python
from .models.rest_ref_sync_queue import RestRefSyncQueue

# TODO update the JSON string below
json = "{}"
# create an instance of RestRefSyncQueue from a JSON string
rest_ref_sync_queue_instance = RestRefSyncQueue.from_json(json)
# print the JSON string representation of the object
print(RestRefSyncQueue.to_json())

# convert the object into a dict
rest_ref_sync_queue_dict = rest_ref_sync_queue_instance.to_dict()
# create an instance of RestRefSyncQueue from a dict
rest_ref_sync_queue_from_dict = RestRefSyncQueue.from_dict(rest_ref_sync_queue_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


