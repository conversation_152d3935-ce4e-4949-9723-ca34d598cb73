# RestSshSettings


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**access_keys_enabled** | **bool** |  | [optional] 
**base_url** | **str** |  | [optional] 
**enabled** | **bool** |  | [optional] 
**fingerprint** | [**SimpleSshKeyFingerprint**](SimpleSshKeyFingerprint.md) |  | [optional] 
**port** | **int** |  | [optional] 

## Example

```python
from .models.rest_ssh_settings import RestSshSettings

# TODO update the JSON string below
json = "{}"
# create an instance of RestSshSettings from a JSON string
rest_ssh_settings_instance = RestSshSettings.from_json(json)
# print the JSON string representation of the object
print(RestSshSettings.to_json())

# convert the object into a dict
rest_ssh_settings_dict = rest_ssh_settings_instance.to_dict()
# create an instance of RestSshSettings from a dict
rest_ssh_settings_from_dict = RestSshSettings.from_dict(rest_ssh_settings_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


