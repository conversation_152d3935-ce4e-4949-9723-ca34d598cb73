# StartMeshMigrationRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**all** | **bool** |  | [optional] 
**max_bytes_per_second** | [**StartMeshMigrationRequestMaxBytesPerSecond**](StartMeshMigrationRequestMaxBytesPerSecond.md) |  | [optional] 
**project_ids** | **List[int]** |  | [optional] 
**repository_ids** | **List[int]** |  | [optional] 

## Example

```python
from .models.start_mesh_migration_request import StartMeshMigrationRequest

# TODO update the JSON string below
json = "{}"
# create an instance of StartMeshMigrationRequest from a JSON string
start_mesh_migration_request_instance = StartMeshMigrationRequest.from_json(json)
# print the JSON string representation of the object
print(StartMeshMigrationRequest.to_json())

# convert the object into a dict
start_mesh_migration_request_dict = start_mesh_migration_request_instance.to_dict()
# create an instance of StartMeshMigrationRequest from a dict
start_mesh_migration_request_from_dict = StartMeshMigrationRequest.from_dict(start_mesh_migration_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


