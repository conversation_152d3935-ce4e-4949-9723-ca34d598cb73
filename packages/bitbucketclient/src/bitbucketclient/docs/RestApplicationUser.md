# RestApplicationUser


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**active** | **bool** |  | [optional] 
**avatar_url** | **str** |  | [optional] 
**display_name** | **str** |  | [optional] 
**email_address** | **str** |  | [optional] 
**id** | **int** |  | [optional] [readonly] 
**links** | **object** |  | [optional] 
**name** | **str** |  | [optional] 
**slug** | **str** |  | [optional] 
**type** | **str** |  | [optional] 

## Example

```python
from .models.rest_application_user import RestApplicationUser

# TODO update the JSON string below
json = "{}"
# create an instance of RestApplicationUser from a JSON string
rest_application_user_instance = RestApplicationUser.from_json(json)
# print the JSON string representation of the object
print(RestApplicationUser.to_json())

# convert the object into a dict
rest_application_user_dict = rest_application_user_instance.to_dict()
# create an instance of RestApplicationUser from a dict
rest_application_user_from_dict = RestApplicationUser.from_dict(rest_application_user_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


