# ExampleSettings


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**boolean_value** | **bool** |  | [optional] 
**double_value** | **float** |  | [optional] 
**integer_value** | **int** |  | [optional] 
**long_value** | **int** |  | [optional] 
**string_value** | **str** |  | [optional] 

## Example

```python
from .models.example_settings import ExampleSettings

# TODO update the JSON string below
json = "{}"
# create an instance of ExampleSettings from a JSON string
example_settings_instance = ExampleSettings.from_json(json)
# print the JSON string representation of the object
print(ExampleSettings.to_json())

# convert the object into a dict
example_settings_dict = example_settings_instance.to_dict()
# create an instance of ExampleSettings from a dict
example_settings_from_dict = ExampleSettings.from_dict(example_settings_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


