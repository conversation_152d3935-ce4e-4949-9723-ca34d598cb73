# UserPasswordUpdate


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**old_password** | **str** |  | [optional] 
**password** | **str** |  | [optional] 
**password_confirm** | **str** |  | [optional] 

## Example

```python
from .models.user_password_update import UserPasswordUpdate

# TODO update the JSON string below
json = "{}"
# create an instance of UserPasswordUpdate from a JSON string
user_password_update_instance = UserPasswordUpdate.from_json(json)
# print the JSON string representation of the object
print(UserPasswordUpdate.to_json())

# convert the object into a dict
user_password_update_dict = user_password_update_instance.to_dict()
# create an instance of UserPasswordUpdate from a dict
user_password_update_from_dict = UserPasswordUpdate.from_dict(user_password_update_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


