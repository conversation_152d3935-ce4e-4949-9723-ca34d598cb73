# RestBearerTokenCredentials


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**token** | **str** |  | 

## Example

```python
from .models.rest_bearer_token_credentials import RestBearerTokenCredentials

# TODO update the JSON string below
json = "{}"
# create an instance of RestBearerTokenCredentials from a JSON string
rest_bearer_token_credentials_instance = RestBearerTokenCredentials.from_json(json)
# print the JSON string representation of the object
print(RestBearerTokenCredentials.to_json())

# convert the object into a dict
rest_bearer_token_credentials_dict = rest_bearer_token_credentials_instance.to_dict()
# create an instance of RestBearerTokenCredentials from a dict
rest_bearer_token_credentials_from_dict = RestBearerTokenCredentials.from_dict(rest_bearer_token_credentials_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


