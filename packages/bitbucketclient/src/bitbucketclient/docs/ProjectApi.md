# .ProjectApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_default_task**](ProjectApi.md#add_default_task) | **POST** /default-tasks/latest/projects/{projectKey}/tasks | Add a default task
[**create3**](ProjectApi.md#create3) | **POST** /api/latest/projects/{projectKey}/settings-restriction | Enforce project restriction
[**create_project**](ProjectApi.md#create_project) | **POST** /api/latest/projects | Create a new project
[**create_repository**](ProjectApi.md#create_repository) | **POST** /api/latest/projects/{projectKey}/repos | Create repository
[**create_restrictions**](ProjectApi.md#create_restrictions) | **POST** /branch-permissions/latest/projects/{projectKey}/restrictions | Create multiple ref restrictions
[**create_webhook**](ProjectApi.md#create_webhook) | **POST** /api/latest/projects/{projectKey}/webhooks | Create webhook
[**delete4**](ProjectApi.md#delete4) | **DELETE** /api/latest/projects/{projectKey}/settings/auto-merge | Delete pull request auto-merge settings
[**delete9**](ProjectApi.md#delete9) | **DELETE** /api/latest/projects/{projectKey}/settings-restriction | Stop enforcing project restriction
[**delete_all_default_tasks**](ProjectApi.md#delete_all_default_tasks) | **DELETE** /default-tasks/latest/projects/{projectKey}/tasks | Deletes all default tasks for the project
[**delete_auto_decline_settings**](ProjectApi.md#delete_auto_decline_settings) | **DELETE** /api/latest/projects/{projectKey}/settings/auto-decline | Delete auto decline settings
[**delete_default_task**](ProjectApi.md#delete_default_task) | **DELETE** /default-tasks/latest/projects/{projectKey}/tasks/{taskId} | Delete a specific default task
[**delete_project**](ProjectApi.md#delete_project) | **DELETE** /api/latest/projects/{projectKey} | Delete project
[**delete_repository**](ProjectApi.md#delete_repository) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug} | Delete repository
[**delete_restriction**](ProjectApi.md#delete_restriction) | **DELETE** /branch-permissions/latest/projects/{projectKey}/restrictions/{id} | Delete a ref restriction
[**delete_webhook**](ProjectApi.md#delete_webhook) | **DELETE** /api/latest/projects/{projectKey}/webhooks/{webhookId} | Delete webhook
[**disable_hook**](ProjectApi.md#disable_hook) | **DELETE** /api/latest/projects/{projectKey}/settings/hooks/{hookKey}/enabled | Disable repository hook
[**enable_hook**](ProjectApi.md#enable_hook) | **PUT** /api/latest/projects/{projectKey}/settings/hooks/{hookKey}/enabled | Enable repository hook
[**find_webhooks**](ProjectApi.md#find_webhooks) | **GET** /api/latest/projects/{projectKey}/webhooks | Find webhooks
[**fork_repository**](ProjectApi.md#fork_repository) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug} | Fork repository
[**get4**](ProjectApi.md#get4) | **GET** /api/latest/projects/{projectKey}/settings/auto-merge | Get pull request auto-merge settings
[**get7**](ProjectApi.md#get7) | **GET** /api/latest/projects/{projectKey}/settings-restriction | Get enforcing project setting
[**get_all**](ProjectApi.md#get_all) | **GET** /api/latest/projects/{projectKey}/settings-restriction/all | Get all enforcing project settings
[**get_auto_decline_settings**](ProjectApi.md#get_auto_decline_settings) | **GET** /api/latest/projects/{projectKey}/settings/auto-decline | Get auto decline settings
[**get_avatar**](ProjectApi.md#get_avatar) | **GET** /api/latest/hooks/{hookKey}/avatar | Get project avatar
[**get_configurations**](ProjectApi.md#get_configurations) | **GET** /api/latest/projects/{projectKey}/hook-scripts | Get configured hook scripts
[**get_default_branch2**](ProjectApi.md#get_default_branch2) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/default-branch | Get repository default branch
[**get_default_tasks**](ProjectApi.md#get_default_tasks) | **GET** /default-tasks/latest/projects/{projectKey}/tasks | Get a page of default tasks
[**get_forked_repositories**](ProjectApi.md#get_forked_repositories) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/forks | Get repository forks
[**get_groups_with_any_permission1**](ProjectApi.md#get_groups_with_any_permission1) | **GET** /api/latest/projects/{projectKey}/permissions/groups | Get groups with permission to project
[**get_groups_without_any_permission1**](ProjectApi.md#get_groups_without_any_permission1) | **GET** /api/latest/projects/{projectKey}/permissions/groups/none | Get groups without project permission
[**get_latest_invocation**](ProjectApi.md#get_latest_invocation) | **GET** /api/latest/projects/{projectKey}/webhooks/{webhookId}/latest | Get last webhook invocation details
[**get_project**](ProjectApi.md#get_project) | **GET** /api/latest/projects/{projectKey} | Get a project
[**get_project_avatar**](ProjectApi.md#get_project_avatar) | **GET** /api/latest/projects/{projectKey}/avatar.png | Get avatar for project
[**get_projects**](ProjectApi.md#get_projects) | **GET** /api/latest/projects | Get projects
[**get_pull_request_settings**](ProjectApi.md#get_pull_request_settings) | **GET** /api/latest/projects/{projectKey}/settings/pull-requests/{scmId} | Get merge strategy
[**get_related_repositories**](ProjectApi.md#get_related_repositories) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/related | Get related repository
[**get_repositories**](ProjectApi.md#get_repositories) | **GET** /api/latest/projects/{projectKey}/repos | Get repositories for project
[**get_repository**](ProjectApi.md#get_repository) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug} | Get repository
[**get_repository_hook**](ProjectApi.md#get_repository_hook) | **GET** /api/latest/projects/{projectKey}/settings/hooks/{hookKey} | Get a repository hook
[**get_repository_hooks**](ProjectApi.md#get_repository_hooks) | **GET** /api/latest/projects/{projectKey}/settings/hooks | Get repository hooks
[**get_restriction**](ProjectApi.md#get_restriction) | **GET** /branch-permissions/latest/projects/{projectKey}/restrictions/{id} | Get a ref restriction
[**get_restrictions**](ProjectApi.md#get_restrictions) | **GET** /branch-permissions/latest/projects/{projectKey}/restrictions | Search for ref restrictions
[**get_settings**](ProjectApi.md#get_settings) | **GET** /api/latest/projects/{projectKey}/settings/hooks/{hookKey}/settings | Get repository hook settings
[**get_statistics**](ProjectApi.md#get_statistics) | **GET** /api/latest/projects/{projectKey}/webhooks/{webhookId}/statistics | Get webhook statistics
[**get_statistics_summary**](ProjectApi.md#get_statistics_summary) | **GET** /api/latest/projects/{projectKey}/webhooks/{webhookId}/statistics/summary | Get webhook statistics summary
[**get_users_with_any_permission1**](ProjectApi.md#get_users_with_any_permission1) | **GET** /api/latest/projects/{projectKey}/permissions/users | Get users with permission to project
[**get_users_without_permission**](ProjectApi.md#get_users_without_permission) | **GET** /api/latest/projects/{projectKey}/permissions/users/none | Get users without project permission
[**get_webhook**](ProjectApi.md#get_webhook) | **GET** /api/latest/projects/{projectKey}/webhooks/{webhookId} | Get webhook
[**has_all_user_permission**](ProjectApi.md#has_all_user_permission) | **GET** /api/latest/projects/{projectKey}/permissions/{permission}/all | Check default project permission
[**modify_all_user_permission**](ProjectApi.md#modify_all_user_permission) | **POST** /api/latest/projects/{projectKey}/permissions/{permission}/all | Grant project permission
[**remove_configuration**](ProjectApi.md#remove_configuration) | **DELETE** /api/latest/projects/{projectKey}/hook-scripts/{scriptId} | Remove a hook script
[**retry_create_repository**](ProjectApi.md#retry_create_repository) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/recreate | Retry repository creation
[**revoke_permissions**](ProjectApi.md#revoke_permissions) | **DELETE** /api/latest/projects/{projectKey}/permissions | Revoke project permissions
[**revoke_permissions_for_group1**](ProjectApi.md#revoke_permissions_for_group1) | **DELETE** /api/latest/projects/{projectKey}/permissions/groups | Revoke group project permission
[**revoke_permissions_for_user1**](ProjectApi.md#revoke_permissions_for_user1) | **DELETE** /api/latest/projects/{projectKey}/permissions/users | Revoke user project permission
[**search_permissions**](ProjectApi.md#search_permissions) | **GET** /api/latest/projects/{projectKey}/permissions/search | Search project permissions
[**set**](ProjectApi.md#set) | **PUT** /api/latest/projects/{projectKey}/settings/auto-merge | Create or update the pull request auto-merge settings
[**set_auto_decline_settings**](ProjectApi.md#set_auto_decline_settings) | **PUT** /api/latest/projects/{projectKey}/settings/auto-decline | Create/Update auto decline settings
[**set_configuration**](ProjectApi.md#set_configuration) | **PUT** /api/latest/projects/{projectKey}/hook-scripts/{scriptId} | Create/update a hook script
[**set_default_branch2**](ProjectApi.md#set_default_branch2) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/default-branch | Update default branch for repository
[**set_permission_for_groups1**](ProjectApi.md#set_permission_for_groups1) | **PUT** /api/latest/projects/{projectKey}/permissions/groups | Update group project permission
[**set_permission_for_users1**](ProjectApi.md#set_permission_for_users1) | **PUT** /api/latest/projects/{projectKey}/permissions/users | Update user project permission
[**set_settings**](ProjectApi.md#set_settings) | **PUT** /api/latest/projects/{projectKey}/settings/hooks/{hookKey}/settings | Update repository hook settings
[**stream_contributing**](ProjectApi.md#stream_contributing) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/contributing | Get repository contributing guidelines
[**stream_license**](ProjectApi.md#stream_license) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/license | Get repository license
[**stream_readme**](ProjectApi.md#stream_readme) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/readme | Get repository readme
[**test_webhook**](ProjectApi.md#test_webhook) | **POST** /api/latest/projects/{projectKey}/webhooks/test | Test webhook
[**update_default_task**](ProjectApi.md#update_default_task) | **PUT** /default-tasks/latest/projects/{projectKey}/tasks/{taskId} | Update a default task
[**update_project**](ProjectApi.md#update_project) | **PUT** /api/latest/projects/{projectKey} | Update project
[**update_pull_request_settings**](ProjectApi.md#update_pull_request_settings) | **POST** /api/latest/projects/{projectKey}/settings/pull-requests/{scmId} | Update merge strategy
[**update_repository**](ProjectApi.md#update_repository) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug} | Update repository
[**update_webhook**](ProjectApi.md#update_webhook) | **PUT** /api/latest/projects/{projectKey}/webhooks/{webhookId} | Update webhook
[**upload_avatar**](ProjectApi.md#upload_avatar) | **POST** /api/latest/projects/{projectKey}/avatar.png | Update project avatar


# **add_default_task**
> RestDefaultTask add_default_task(project_key, rest_default_task_request)

Add a default task

Creates a default task for the project.

The authenticated user must have **PROJECT_ADMIN** permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_default_task import RestDefaultTask
from .models.rest_default_task_request import RestDefaultTaskRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_default_task_request = .RestDefaultTaskRequest() # RestDefaultTaskRequest | The task to be added

    try:
        # Add a default task
        api_response = api_instance.add_default_task(project_key, rest_default_task_request)
        print("The response of ProjectApi->add_default_task:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->add_default_task: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_default_task_request** | [**RestDefaultTaskRequest**](RestDefaultTaskRequest.md)| The task to be added | 

### Return type

[**RestDefaultTask**](RestDefaultTask.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default task |  -  |
**400** | One or more of the following error cases occurred (check the error message for more details):    - the description is empty- the sourceMatcher or targetMatcher is invalid |  -  |
**401** | The currently authenticated user has insufficient permissions to add a default task |  -  |
**404** | The specified project does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create3**
> RestProjectSettingsRestriction create3(project_key, rest_project_settings_restriction_request)

Enforce project restriction

Create a new project settings restriction for the given project.

The authenticated user must have **PROJECT_ADMIN** permission for the target project to create a settings restriction.

### Example


```python
import bitbucketclient
from .models.rest_project_settings_restriction import RestProjectSettingsRestriction
from .models.rest_project_settings_restriction_request import RestProjectSettingsRestrictionRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_project_settings_restriction_request = .RestProjectSettingsRestrictionRequest() # RestProjectSettingsRestrictionRequest | The project settings restriction to create

    try:
        # Enforce project restriction
        api_response = api_instance.create3(project_key, rest_project_settings_restriction_request)
        print("The response of ProjectApi->create3:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->create3: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_project_settings_restriction_request** | [**RestProjectSettingsRestrictionRequest**](RestProjectSettingsRestrictionRequest.md)| The project settings restriction to create | 

### Return type

[**RestProjectSettingsRestriction**](RestProjectSettingsRestriction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The settings restriction was successfully created |  -  |
**400** | The settings restriction was not created because the request was invalid. Possible issues include:  - The namespace was not provided, or longer than 255 characters - The featureKey was not provided, or longer than 255 characters - The provided componentKey was fewer than 2 characters, or longer than 255 characters |  -  |
**401** | The currently authenticated user has insufficient permissions to create a settings restriction |  -  |
**404** | The specified project does not exist |  -  |
**409** | A settings restriction with the same namespace, featureKey and componentKey already exists on this project |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_project**
> RestProject create_project(rest_project=rest_project)

Create a new project

Create a new project. 

To include a custom avatar for the project, the project definition should contain an additional attribute with the key <code>avatar</code> and the value a data URI containing Base64-encoded image data. The URI should be in the following format: <pre>    data:(content type, e.g. image/png);base64,(data) </pre>If the data is not Base64-encoded, or if a character set is defined in the URI, or the URI is otherwise invalid, <em>project creation will fail</em>. 

The authenticated user must have <strong>PROJECT_CREATE</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_project import RestProject
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    rest_project = .RestProject() # RestProject | The project. (optional)

    try:
        # Create a new project
        api_response = api_instance.create_project(rest_project=rest_project)
        print("The response of ProjectApi->create_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->create_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_project** | [**RestProject**](RestProject.md)| The project. | [optional] 

### Return type

[**RestProject**](RestProject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created project. |  -  |
**400** | The currently authenticated user has insufficient permissions to update the project. |  -  |
**401** | The currently authenticated user has insufficient permissions to create a project. |  -  |
**409** | The project key or name is already in use. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_repository**
> RestRepository create_repository(project_key, rest_repository=rest_repository)

Create repository

Create a new repository. Requires an existing project in which this repository will be created. The only parameters which will be used are name and scmId. 

The authenticated user must have <strong>REPO_CREATE</strong> permission or higher, for the context project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository import RestRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_repository = .RestRepository() # RestRepository | The repository (optional)

    try:
        # Create repository
        api_response = api_instance.create_repository(project_key, rest_repository=rest_repository)
        print("The response of ProjectApi->create_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->create_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_repository** | [**RestRepository**](RestRepository.md)| The repository | [optional] 

### Return type

[**RestRepository**](RestRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created repository. |  -  |
**400** | The repository was not created due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to create a repository. |  -  |
**409** | A repository with same name already exists. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_restrictions**
> RestRefRestriction create_restrictions(project_key, rest_restriction_request=rest_restriction_request)

Create multiple ref restrictions

Allows creating multiple restrictions at once.

### Example


```python
import bitbucketclient
from .models.rest_ref_restriction import RestRefRestriction
from .models.rest_restriction_request import RestRestrictionRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_restriction_request = [.RestRestrictionRequest()] # List[RestRestrictionRequest] | The request containing a list of the details of the restrictions to create. (optional)

    try:
        # Create multiple ref restrictions
        api_response = api_instance.create_restrictions(project_key, rest_restriction_request=rest_restriction_request)
        print("The response of ProjectApi->create_restrictions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->create_restrictions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_restriction_request** | [**List[RestRestrictionRequest]**](RestRestrictionRequest.md)| The request containing a list of the details of the restrictions to create. | [optional] 

### Return type

[**RestRefRestriction**](RestRefRestriction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/vnd.atl.bitbucket.bulk+json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Response contains the ref restriction that was just created. |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user has insufficient permissions to perform this operation. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_webhook**
> RestWebhook create_webhook(project_key, rest_webhook=rest_webhook)

Create webhook

Create a webhook for the project specified via the URL. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook import RestWebhook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_webhook = .RestWebhook() # RestWebhook | The webhook to be created for this project. (optional)

    try:
        # Create webhook
        api_response = api_instance.create_webhook(project_key, rest_webhook=rest_webhook)
        print("The response of ProjectApi->create_webhook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->create_webhook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_webhook** | [**RestWebhook**](RestWebhook.md)| The webhook to be created for this project. | [optional] 

### Return type

[**RestWebhook**](RestWebhook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A created webhook. |  -  |
**400** | The webhook parameters were invalid or not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to create webhooks in the project. |  -  |
**404** | The project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete4**
> delete4(project_key)

Delete pull request auto-merge settings

Deletes pull request auto-merge settings for the supplied project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key

    try:
        # Delete pull request auto-merge settings
        api_instance.delete4(project_key)
    except Exception as e:
        print("Exception when calling ProjectApi->delete4: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The pull request auto-merge settings |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the pull request auto-merge settings. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete9**
> delete9(project_key, namespace, feature_key, component_key=component_key)

Stop enforcing project restriction

Delete a specified project settings restriction.

If a restriction does not exist for the specified project, namespace, featureKey, and componentKey, the request will be ignored and a 204 response will be returned.

The authenticated user must have **PROJECT_ADMIN** permission for the target project to delete a settings restriction.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    namespace = 'namespace_example' # str | A namespace used to identify the provider of the feature
    feature_key = 'feature_key_example' # str | A key to uniquely identify the feature within the provided namespace
    component_key = 'component_key_example' # str | A key to uniquely identify individually restrictable subcomponents of a feature within the provided feature key and namespace (optional)

    try:
        # Stop enforcing project restriction
        api_instance.delete9(project_key, namespace, feature_key, component_key=component_key)
    except Exception as e:
        print("Exception when calling ProjectApi->delete9: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **namespace** | **str**| A namespace used to identify the provider of the feature | 
 **feature_key** | **str**| A key to uniquely identify the feature within the provided namespace | 
 **component_key** | **str**| A key to uniquely identify individually restrictable subcomponents of a feature within the provided feature key and namespace | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The specified settings restriction was successfully deleted or there were no existing restrictions that match the specified criteria. |  -  |
**400** | The settings restriction was not deleted because the request was invalid. Possible issues include:  - The namespace was not provided, or longer than 255 characters - The featureKey was not provided, or longer than 255 characters - The provided componentKey was fewer than 2 characters, or longer than 255 characters |  -  |
**401** | The currently authenticated user has insufficient permissions to delete a settings restriction |  -  |
**404** | The specified project does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_all_default_tasks**
> delete_all_default_tasks(project_key)

Deletes all default tasks for the project

Delete all the default tasks for the supplied project

The authenticated user must have **PROJECT_ADMIN** permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.

    try:
        # Deletes all default tasks for the project
        api_instance.delete_all_default_tasks(project_key)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_all_default_tasks: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default tasks have been deleted successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete default tasks |  -  |
**404** | The specified project does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_auto_decline_settings**
> delete_auto_decline_settings(project_key)

Delete auto decline settings

Delete auto decline settings for the supplied project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key

    try:
        # Delete auto decline settings
        api_instance.delete_auto_decline_settings(project_key)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_auto_decline_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The auto decline settings have been deleted successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the auto decline settings. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_default_task**
> delete_default_task(project_key, task_id)

Delete a specific default task

Delete a specific default task for a project.

The authenticated user must have **PROJECT_ADMIN** permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    task_id = 'task_id_example' # str | The ID of the default task

    try:
        # Delete a specific default task
        api_instance.delete_default_task(project_key, task_id)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_default_task: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **task_id** | **str**| The ID of the default task | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default task has been deleted successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete default tasks |  -  |
**404** | The specified project or task does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_project**
> delete_project(project_key)

Delete project

Delete the project matching the supplied <strong>projectKey</strong>. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.

    try:
        # Delete project
        api_instance.delete_project(project_key)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The project matching the supplied &lt;strong&gt;projectKey&lt;/strong&gt; was deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the project. |  -  |
**404** | The specified project does not exist. |  -  |
**409** | The project can not be deleted as it contains repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_repository**
> delete_repository(project_key, repository_slug)

Delete repository

Schedule the repository matching the supplied <strong>projectKey</strong> and <strong>repositorySlug</strong> to be deleted. 

The authenticated user must have sufficient permissions specified by the repository delete policy to call this resource. The default permission required is <strong>REPO_ADMIN</strong> permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete repository
        api_instance.delete_repository(project_key, repository_slug)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**202** | The repository has been scheduled for deletion. |  -  |
**204** | No repository matching the supplied &lt;strong&gt;projectKey&lt;/strong&gt; and &lt;strong&gt;repositorySlug&lt;/strong&gt; was found. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_restriction**
> delete_restriction(project_key, id)

Delete a ref restriction

Deletes a restriction as specified by a restriction id.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission or higher to call this resource. Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The restriction id.

    try:
        # Delete a ref restriction
        api_instance.delete_restriction(project_key, id)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_restriction: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The restriction id. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the operation was successful |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user is not permitted to delete restrictions on the provided project |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_webhook**
> delete_webhook(project_key, webhook_id)

Delete webhook

Delete a webhook for the project specified via the URL. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | The ID of the webhook to be deleted.

    try:
        # Delete webhook
        api_instance.delete_webhook(project_key, webhook_id)
    except Exception as e:
        print("Exception when calling ProjectApi->delete_webhook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| The ID of the webhook to be deleted. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The webhook for the project has been deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete webhooks in the project. |  -  |
**404** | The specified project does not exist, or webhook does not exist in this project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **disable_hook**
> RestRepositoryHook disable_hook(project_key, hook_key)

Disable repository hook

Disable a repository hook for this project. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository_hook import RestRepositoryHook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.

    try:
        # Disable repository hook
        api_response = api_instance.disable_hook(project_key, hook_key)
        print("The response of ProjectApi->disable_hook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->disable_hook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 

### Return type

[**RestRepositoryHook**](RestRepositoryHook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository hooks with their associated enabled state for the supplied hookKey. |  -  |
**401** | The currently authenticated user has insufficient permissions to disable the hook. |  -  |
**404** | The specified project or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **enable_hook**
> RestRepositoryHook enable_hook(project_key, hook_key, content_length=content_length)

Enable repository hook

Enable a repository hook for this project and optionally apply new configuration. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource. 

A JSON document may be provided to use as the settings for the hook. These structure and validity of the document is decided by the plugin providing the hook.

### Example


```python
import bitbucketclient
from .models.rest_repository_hook import RestRepositoryHook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    content_length = 56 # int | The content length. (optional)

    try:
        # Enable repository hook
        api_response = api_instance.enable_hook(project_key, hook_key, content_length=content_length)
        print("The response of ProjectApi->enable_hook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->enable_hook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **content_length** | **int**| The content length. | [optional] 

### Return type

[**RestRepositoryHook**](RestRepositoryHook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository hooks with their associated enabled state for the supplied hookKey. |  -  |
**400** | The settings specified are invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to enable the hook. |  -  |
**404** | The specified project or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **find_webhooks**
> find_webhooks(project_key, event=event, statistics=statistics)

Find webhooks

Find webhooks in this project. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    event = 'event_example' # str | List of <code>com.atlassian.webhooks.WebhookEvent</code> IDs to filter for (optional)
    statistics = True # bool | <code>true</code> if statistics should be provided for all found webhooks (optional)

    try:
        # Find webhooks
        api_instance.find_webhooks(project_key, event=event, statistics=statistics)
    except Exception as e:
        print("Exception when calling ProjectApi->find_webhooks: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **event** | **str**| List of &lt;code&gt;com.atlassian.webhooks.WebhookEvent&lt;/code&gt; IDs to filter for | [optional] 
 **statistics** | **bool**| &lt;code&gt;true&lt;/code&gt; if statistics should be provided for all found webhooks | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of webhooks. |  -  |
**401** | The currently authenticated user has insufficient permissions to find webhooks in the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **fork_repository**
> RestRepository fork_repository(project_key, repository_slug, rest_repository=rest_repository)

Fork repository

Create a new repository forked from an existing repository. 

The JSON body for this <code>POST</code> is not required to contain <i>any</i> properties. Even the name may be omitted. The following properties will be used, if provided: 

- <code>"name":"Fork name"</code> - Specifies the forked repository's name 
  - Defaults to the name of the origin repository if not specified
- <code>"defaultBranch":"main"</code> - Specifies the forked repository's default branch
  - Defaults to the origin repository's default branch if not specified
- <code>"project":{"key":"TARGET_KEY"}</code> - Specifies the forked repository's target project by key
  - Defaults to the current user's personal project if not specified


The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository and <strong>PROJECT_ADMIN</strong> on the target project to call this resource. Note that users <i>always</i> have <b>PROJECT_ADMIN</b> permission on their personal projects.

### Example


```python
import bitbucketclient
from .models.rest_repository import RestRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_repository = .RestRepository() # RestRepository | The rest fork. (optional)

    try:
        # Fork repository
        api_response = api_instance.fork_repository(project_key, repository_slug, rest_repository=rest_repository)
        print("The response of ProjectApi->fork_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->fork_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_repository** | [**RestRepository**](RestRepository.md)| The rest fork. | [optional] 

### Return type

[**RestRepository**](RestRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created fork. |  -  |
**400** | A validation error prevented the fork from being created. Possible validation errors include: The name or slug for the fork collides with another repository in the target project; an SCM type was specified in the JSON body; a project was specified in the JSON body without a \&quot;key\&quot; property. |  -  |
**401** | The currently authenticated user has insufficient permissions to create a fork. |  -  |
**404** | The specified repository does not exist, or, if a target project was specified, the target project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get4**
> RestAutoMergeRestrictedSettings get4(project_key)

Get pull request auto-merge settings

Retrieves the pull request auto-merge settings for the supplied project. Default settings will be returned if no explicit settings have been set for the project

The authenticated user must have <strong>PROJECT_VIEW</strong> permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_merge_restricted_settings import RestAutoMergeRestrictedSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key

    try:
        # Get pull request auto-merge settings
        api_response = api_instance.get4(project_key)
        print("The response of ProjectApi->get4:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get4: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 

### Return type

[**RestAutoMergeRestrictedSettings**](RestAutoMergeRestrictedSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The pull request auto-merge settings |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the pull request auto-merge settings. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get7**
> RestProjectSettingsRestriction get7(project_key, namespace, feature_key, component_key=component_key)

Get enforcing project setting

Get a specified project settings restriction for the given namespace, feature key and component key.
Note that not providing the component key will **not** return restrictions for the namespace and feature key with a component key set.

The authenticated user must have **PROJECT_VIEW** permission for the target project to retrieve a settings restriction.

### Example


```python
import bitbucketclient
from .models.rest_project_settings_restriction import RestProjectSettingsRestriction
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    namespace = 'namespace_example' # str | The namespace used to identify the provider of the feature
    feature_key = 'feature_key_example' # str | The feature key to uniquely identify the feature within the provided namespace
    component_key = 'component_key_example' # str | The component key to uniquely identify individually restrictable subcomponents of a feature within the provided feature key and namespace (optional)

    try:
        # Get enforcing project setting
        api_response = api_instance.get7(project_key, namespace, feature_key, component_key=component_key)
        print("The response of ProjectApi->get7:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get7: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **namespace** | **str**| The namespace used to identify the provider of the feature | 
 **feature_key** | **str**| The feature key to uniquely identify the feature within the provided namespace | 
 **component_key** | **str**| The component key to uniquely identify individually restrictable subcomponents of a feature within the provided feature key and namespace | [optional] 

### Return type

[**RestProjectSettingsRestriction**](RestProjectSettingsRestriction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The settings restriction associated with the provided namespace and feature key |  -  |
**400** | The settings restriction could not be retrieved because the provided parameters were invalid. Possible issues include:  - The namespace was not provided, or longer than 255 characters - The featureKey was not provided, or longer than 255 characters - The provided componentKey was fewer than 2 characters, or longer than 255 characters |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve a settings restriction |  -  |
**404** | The specified project, or settings restriction does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all**
> GetAll200Response get_all(project_key, namespace, feature_key, start=start, limit=limit)

Get all enforcing project settings

Get all project settings restrictions for the given namespace and feature key, including those with a component key set.

The authenticated user must have **PROJECT_VIEW** permission for the target project to retrieve a settings restrictions.

### Example


```python
import bitbucketclient
from .models.get_all200_response import GetAll200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    namespace = 'namespace_example' # str | A namespace used to identify the provider of the feature
    feature_key = 'feature_key_example' # str | A key to uniquely identify the feature within the provided namespace
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get all enforcing project settings
        api_response = api_instance.get_all(project_key, namespace, feature_key, start=start, limit=limit)
        print("The response of ProjectApi->get_all:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_all: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **namespace** | **str**| A namespace used to identify the provider of the feature | 
 **feature_key** | **str**| A key to uniquely identify the feature within the provided namespace | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetAll200Response**](GetAll200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of settings restrictions associated with the provided namespace and feature key |  -  |
**400** | The settings restrictions could not be retrieved because the provided parameters were invalid. Possible issues include:  - The namespace was not provided, or longer than 255 characters - The featureKey was not provided, or longer than 255 characters |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve project settings restrictions |  -  |
**404** | The specified project does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_auto_decline_settings**
> RestAutoDeclineSettings get_auto_decline_settings(project_key)

Get auto decline settings

Retrieves the auto decline settings for the supplied project. Default settings are returned if no explicit settings have been set for the project.

### Example


```python
import bitbucketclient
from .models.rest_auto_decline_settings import RestAutoDeclineSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key

    try:
        # Get auto decline settings
        api_response = api_instance.get_auto_decline_settings(project_key)
        print("The response of ProjectApi->get_auto_decline_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_auto_decline_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 

### Return type

[**RestAutoDeclineSettings**](RestAutoDeclineSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The auto decline settings |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the auto decline settings. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_avatar**
> get_avatar(hook_key, version=version)

Get project avatar

Retrieve the avatar for the project matching the supplied <strong>moduleKey</strong>.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    hook_key = 'hook_key_example' # str | The complete module key of the hook module.
    version = 'version_example' # str | (optional) Version used for HTTP caching only - any non-blank version will result in a large max-age Cache-Control header. Note that this does not affect the Last-Modified header. (optional)

    try:
        # Get project avatar
        api_instance.get_avatar(hook_key, version=version)
    except Exception as e:
        print("Exception when calling ProjectApi->get_avatar: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **hook_key** | **str**| The complete module key of the hook module. | 
 **version** | **str**| (optional) Version used for HTTP caching only - any non-blank version will result in a large max-age Cache-Control header. Note that this does not affect the Last-Modified header. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The avatar of the project matching the supplied &lt;strong&gt;moduleKey&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_configurations**
> GetConfigurations200Response get_configurations(project_key, start=start, limit=limit)

Get configured hook scripts

Return a page of hook scripts configured for the specified project. 

This endpoint requires **PROJECT_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.get_configurations200_response import GetConfigurations200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get configured hook scripts
        api_response = api_instance.get_configurations(project_key, start=start, limit=limit)
        print("The response of ProjectApi->get_configurations:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_configurations: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetConfigurations200Response**](GetConfigurations200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of hook scripts. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_default_branch2**
> RestMinimalRef get_default_branch2(project_key, repository_slug)

Get repository default branch

Retrieves the repository's <i>configured</i> default branch. 

Every repository has a <i>configured</i> default branch, but that branch may not actually <i>exist</i> in the repository. For example, a newly-created repository will have a configured default branch even though no branches have been pushed yet. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_minimal_ref import RestMinimalRef
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get repository default branch
        api_response = api_instance.get_default_branch2(project_key, repository_slug)
        print("The response of ProjectApi->get_default_branch2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_default_branch2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestMinimalRef**](RestMinimalRef.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The configured default branch for the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist, or its configured default branch does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_default_tasks**
> GetDefaultTasks1200Response get_default_tasks(project_key, markup=markup, start=start, limit=limit)

Get a page of default tasks

Retrieves the default tasks for the supplied project.

The authenticated user must have **PROJECT_VIEW** permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .models.get_default_tasks1200_response import GetDefaultTasks1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    markup = 'markup_example' # str | If present or \"true\", includes a markup-rendered description (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get a page of default tasks
        api_response = api_instance.get_default_tasks(project_key, markup=markup, start=start, limit=limit)
        print("The response of ProjectApi->get_default_tasks:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_default_tasks: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **markup** | **str**| If present or \&quot;true\&quot;, includes a markup-rendered description | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetDefaultTasks1200Response**](GetDefaultTasks1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of default tasks |  -  |
**401** | The currently authenticated user has insufficient permissions to delete default tasks |  -  |
**404** | The specified project does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_forked_repositories**
> GetRepositoriesRecentlyAccessed200Response get_forked_repositories(project_key, repository_slug, start=start, limit=limit)

Get repository forks

Retrieve repositories which have been forked from this one. Unlike #getRelatedRepositories(Repository, PageRequest) related repositories, this only looks at a given repository's direct forks. If those forks have themselves been the origin of more forks, such "grandchildren" repositories will not be retrieved. 

Only repositories to which the authenticated user has <b>REPO_READ</b> permission will be included, even if other repositories have been forked from this one.

### Example


```python
import bitbucketclient
from .models.get_repositories_recently_accessed200_response import GetRepositoriesRecentlyAccessed200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repository forks
        api_response = api_instance.get_forked_repositories(project_key, repository_slug, start=start, limit=limit)
        print("The response of ProjectApi->get_forked_repositories:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_forked_repositories: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoriesRecentlyAccessed200Response**](GetRepositoriesRecentlyAccessed200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of repositories related to the request repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the request repository. |  -  |
**404** | The request repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_groups_with_any_permission1**
> GetGroupsWithAnyPermission200Response get_groups_with_any_permission1(project_key, filter=filter, start=start, limit=limit)

Get groups with permission to project

Retrieve a page of groups that have been granted at least one permission for the specified project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_groups_with_any_permission200_response import GetGroupsWithAnyPermission200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    filter = 'filter_example' # str | If specified only group names containing the supplied string will be returned (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get groups with permission to project
        api_response = api_instance.get_groups_with_any_permission1(project_key, filter=filter, start=start, limit=limit)
        print("The response of ProjectApi->get_groups_with_any_permission1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_groups_with_any_permission1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **filter** | **str**| If specified only group names containing the supplied string will be returned | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetGroupsWithAnyPermission200Response**](GetGroupsWithAnyPermission200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of groups and their highest permissions for the specified project. |  -  |
**401** | The currently authenticated user is not a project administrator for the specified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_groups_without_any_permission1**
> GetGroups1200Response get_groups_without_any_permission1(project_key, filter=filter, start=start, limit=limit)

Get groups without project permission

Retrieve a page of groups that have no granted permissions for the specified project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher

### Example


```python
import bitbucketclient
from .models.get_groups1200_response import GetGroups1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    filter = 'filter_example' # str | If specified only group names containing the supplied string will be returned (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get groups without project permission
        api_response = api_instance.get_groups_without_any_permission1(project_key, filter=filter, start=start, limit=limit)
        print("The response of ProjectApi->get_groups_without_any_permission1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_groups_without_any_permission1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **filter** | **str**| If specified only group names containing the supplied string will be returned | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetGroups1200Response**](GetGroups1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**202** | A page of groups that have not been granted any permissions for the specifiedproject. |  -  |
**401** | The currently authenticated user is not a project administrator for thespecified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_latest_invocation**
> RestDetailedInvocation get_latest_invocation(project_key, webhook_id, event=event, outcome=outcome)

Get last webhook invocation details

Get the latest invocations for a specific webhook. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_detailed_invocation import RestDetailedInvocation
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    event = 'event_example' # str | The string ID of a specific event to retrieve the last invocation for. (optional)
    outcome = 'outcome_example' # str | The outcome to filter for. Can be SUCCESS, FAILURE, ERROR. None specified means that the all will be considered (optional)

    try:
        # Get last webhook invocation details
        api_response = api_instance.get_latest_invocation(project_key, webhook_id, event=event, outcome=outcome)
        print("The response of ProjectApi->get_latest_invocation:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_latest_invocation: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **event** | **str**| The string ID of a specific event to retrieve the last invocation for. | [optional] 
 **outcome** | **str**| The outcome to filter for. Can be SUCCESS, FAILURE, ERROR. None specified means that the all will be considered | [optional] 

### Return type

[**RestDetailedInvocation**](RestDetailedInvocation.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook invocation dataset. |  -  |
**401** | The currently authenticated user has insufficient permissions to get webhook invocations in the project. |  -  |
**404** | The specified project does not exist, or the webhook does not exist in the project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_project**
> RestProject get_project(project_key)

Get a project

Retrieve the project matching the supplied <strong>projectKey</strong>. 

The authenticated user must have <strong>PROJECT_VIEW</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_project import RestProject
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.

    try:
        # Get a project
        api_response = api_instance.get_project(project_key)
        print("The response of ProjectApi->get_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 

### Return type

[**RestProject**](RestProject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The project matching the supplied &lt;strong&gt;projectKey&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_project_avatar**
> get_project_avatar(project_key, s=s)

Get avatar for project

Retrieve the avatar for the project matching the supplied <strong>projectKey</strong>. 

The authenticated user must have <strong>PROJECT_VIEW</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    s = 's_example' # str | The desired size of the image. The server will return an image as close as possible to the specified size. (optional)

    try:
        # Get avatar for project
        api_instance.get_project_avatar(project_key, s=s)
    except Exception as e:
        print("Exception when calling ProjectApi->get_project_avatar: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **s** | **str**| The desired size of the image. The server will return an image as close as possible to the specified size. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: image/png, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The avatar of the project matching the supplied &lt;strong&gt;projectKey&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_projects**
> GetProjects200Response get_projects(name=name, permission=permission, start=start, limit=limit)

Get projects

Retrieve a page of projects. 

Only projects for which the authenticated user has the <strong>PROJECT_VIEW</strong> permission will be returned.

### Example


```python
import bitbucketclient
from .models.get_projects200_response import GetProjects200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    name = 'name_example' # str | Name to filter by. (optional)
    permission = 'permission_example' # str | Permission to filter by (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get projects
        api_response = api_instance.get_projects(name=name, permission=permission, start=start, limit=limit)
        print("The response of ProjectApi->get_projects:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_projects: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **name** | **str**| Name to filter by. | [optional] 
 **permission** | **str**| Permission to filter by | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetProjects200Response**](GetProjects200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of projects. |  -  |
**400** | The permission level is unknown or not related to projects. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_pull_request_settings**
> RestPullRequestSettings get_pull_request_settings(project_key, scm_id)

Get merge strategy

Retrieve the merge strategy configuration for this project and SCM. 

The authenticated user must have <strong>PROJECT_READ</strong> permission for the context repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_settings import RestPullRequestSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    scm_id = 'scm_id_example' # str | The SCM to get strategies for.

    try:
        # Get merge strategy
        api_response = api_instance.get_pull_request_settings(project_key, scm_id)
        print("The response of ProjectApi->get_pull_request_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_pull_request_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **scm_id** | **str**| The SCM to get strategies for. | 

### Return type

[**RestPullRequestSettings**](RestPullRequestSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The merge configuration of the request project. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the request repository. |  -  |
**404** | The request repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_related_repositories**
> GetRepositoriesRecentlyAccessed200Response get_related_repositories(project_key, repository_slug, start=start, limit=limit)

Get related repository

Retrieve repositories which are related to this one. Related repositories are from the same Repository#getHierarchyId() hierarchy as this repository. 

Only repositories to which the authenticated user has <b>REPO_READ</b> permission will be included, even if more repositories are part of this repository's hierarchy.

### Example


```python
import bitbucketclient
from .models.get_repositories_recently_accessed200_response import GetRepositoriesRecentlyAccessed200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get related repository
        api_response = api_instance.get_related_repositories(project_key, repository_slug, start=start, limit=limit)
        print("The response of ProjectApi->get_related_repositories:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_related_repositories: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoriesRecentlyAccessed200Response**](GetRepositoriesRecentlyAccessed200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of repositories related to the request repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the request repository. |  -  |
**404** | The request repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repositories**
> GetRepositoriesRecentlyAccessed200Response get_repositories(project_key, start=start, limit=limit)

Get repositories for project

Retrieve repositories from the project corresponding to the supplied <strong>projectKey</strong>. 

The authenticated user must have <strong>PROJECT_READ</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.get_repositories_recently_accessed200_response import GetRepositoriesRecentlyAccessed200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repositories for project
        api_response = api_instance.get_repositories(project_key, start=start, limit=limit)
        print("The response of ProjectApi->get_repositories:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_repositories: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoriesRecentlyAccessed200Response**](GetRepositoriesRecentlyAccessed200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repositories matching the supplied &lt;strong&gt;projectKey&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the specified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository**
> RestRepository get_repository(project_key, repository_slug)

Get repository

Retrieve the repository matching the supplied <strong>projectKey</strong> and <strong>repositorySlug</strong>. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository import RestRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get repository
        api_response = api_instance.get_repository(project_key, repository_slug)
        print("The response of ProjectApi->get_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestRepository**](RestRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository which matches the supplied &lt;strong&gt;projectKey&lt;/strong&gt; and &lt;strong&gt;repositorySlug&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_hook**
> RestRepositoryHook get_repository_hook(project_key, hook_key)

Get a repository hook

Retrieve a repository hook for this project. 

The authenticated user must have <strong>PROJECT_READ</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository_hook import RestRepositoryHook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.

    try:
        # Get a repository hook
        api_response = api_instance.get_repository_hook(project_key, hook_key)
        print("The response of ProjectApi->get_repository_hook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_repository_hook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 

### Return type

[**RestRepositoryHook**](RestRepositoryHook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Returns the repository hooks with their associated enabled state for the supplied hookKey. |  -  |
**401** | The currently authenticated user has insufficient permissions to enable the hook. |  -  |
**404** | The specified repository hook does not exist for the given project, or the project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_hooks**
> GetRepositoryHooks1200Response get_repository_hooks(project_key, type=type, start=start, limit=limit)

Get repository hooks

Retrieve a page of repository hooks for this project. 

The authenticated user must have <strong>PROJECT_READ</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.get_repository_hooks1200_response import GetRepositoryHooks1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    type = 'type_example' # str | The optional type to filter by. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repository hooks
        api_response = api_instance.get_repository_hooks(project_key, type=type, start=start, limit=limit)
        print("The response of ProjectApi->get_repository_hooks:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_repository_hooks: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **type** | **str**| The optional type to filter by. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoryHooks1200Response**](GetRepositoryHooks1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of repository hooks with their associated enabled state. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the hooks. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_restriction**
> RestRefRestriction get_restriction(project_key, id)

Get a ref restriction

Returns a restriction as specified by a restriction id.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission or higher to call this resource. Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .models.rest_ref_restriction import RestRefRestriction
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The restriction id.

    try:
        # Get a ref restriction
        api_response = api_instance.get_restriction(project_key, id)
        print("The response of ProjectApi->get_restriction:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_restriction: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The restriction id. | 

### Return type

[**RestRefRestriction**](RestRefRestriction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the restriction. |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user is not permitted to get restrictions on the provided project |  -  |
**404** | No restriction exists for the provided ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_restrictions**
> GetRestrictions1200Response get_restrictions(project_key, matcher_type=matcher_type, matcher_id=matcher_id, type=type, start=start, limit=limit)

Search for ref restrictions

Search for restrictions using the supplied parameters.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission or higher to call this resource. Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .models.get_restrictions1200_response import GetRestrictions1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    matcher_type = 'matcher_type_example' # str | Matcher type to filter on (optional)
    matcher_id = 'matcher_id_example' # str | Matcher id to filter on. Requires the matcherType parameter to be specified also. (optional)
    type = 'type_example' # str | Types of restrictions to filter on. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Search for ref restrictions
        api_response = api_instance.get_restrictions(project_key, matcher_type=matcher_type, matcher_id=matcher_id, type=type, start=start, limit=limit)
        print("The response of ProjectApi->get_restrictions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_restrictions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **matcher_type** | **str**| Matcher type to filter on | [optional] 
 **matcher_id** | **str**| Matcher id to filter on. Requires the matcherType parameter to be specified also. | [optional] 
 **type** | **str**| Types of restrictions to filter on. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRestrictions1200Response**](GetRestrictions1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing a page of restrictions. |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user is not permitted to get restrictions on the provided project |  -  |
**404** | No restriction exists for the provided ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_settings**
> ExampleSettings get_settings(project_key, hook_key)

Get repository hook settings

Retrieve the settings for a repository hook for this project. 

The authenticated user must have <strong>PROJECT_READ</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.example_settings import ExampleSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.

    try:
        # Get repository hook settings
        api_response = api_instance.get_settings(project_key, hook_key)
        print("The response of ProjectApi->get_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 

### Return type

[**ExampleSettings**](ExampleSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The settings for the hook. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the hook settings. |  -  |
**404** | The specified project or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_statistics**
> object get_statistics(project_key, webhook_id, event=event)

Get webhook statistics

Get the statistics for a specific webhook. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    event = 'event_example' # str | The string ID of a specific event to retrieve the last invocation for. May be empty, in which case all events are considered (optional)

    try:
        # Get webhook statistics
        api_response = api_instance.get_statistics(project_key, webhook_id, event=event)
        print("The response of ProjectApi->get_statistics:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_statistics: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **event** | **str**| The string ID of a specific event to retrieve the last invocation for. May be empty, in which case all events are considered | [optional] 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook invocation dataset. |  -  |
**401** | The currently authenticated user has insufficient permissions to get webhook statistics in the project. |  -  |
**404** | The specified project does not exist, or the webhook does not exist in the project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_statistics_summary**
> object get_statistics_summary(project_key, webhook_id)

Get webhook statistics summary

Get the statistics summary for a specific webhook. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook

    try:
        # Get webhook statistics summary
        api_response = api_instance.get_statistics_summary(project_key, webhook_id)
        print("The response of ProjectApi->get_statistics_summary:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_statistics_summary: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook invocation dataset. |  -  |
**204** | No webhook invocations exist. |  -  |
**401** | The currently authenticated user has insufficient permissions to get webhook statistics summary in the project. |  -  |
**404** | The project does not exist, or the webhook does not exist in the project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_users_with_any_permission1**
> GetUsersWithAnyPermission1200Response get_users_with_any_permission1(project_key, filter=filter, start=start, limit=limit)

Get users with permission to project

Retrieve a page of users that have been granted at least one permission for the specified project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_users_with_any_permission1200_response import GetUsersWithAnyPermission1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    filter = 'filter_example' # str | If specified only user names containing the supplied string will be returned (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get users with permission to project
        api_response = api_instance.get_users_with_any_permission1(project_key, filter=filter, start=start, limit=limit)
        print("The response of ProjectApi->get_users_with_any_permission1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_users_with_any_permission1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **filter** | **str**| If specified only user names containing the supplied string will be returned | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetUsersWithAnyPermission1200Response**](GetUsersWithAnyPermission1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of users and their highest permissions for the specified project. |  -  |
**401** | The currently authenticated user is not a project administrator for thespecified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_users_without_permission**
> GetUsersWithoutAnyPermission200Response get_users_without_permission(project_key, filter=filter, start=start, limit=limit)

Get users without project permission

Retrieve a page of <i>licensed</i> users that have no granted permissions for the specified project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_users_without_any_permission200_response import GetUsersWithoutAnyPermission200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    filter = 'filter_example' # str | If specified only user names containing the supplied string will be returned (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get users without project permission
        api_response = api_instance.get_users_without_permission(project_key, filter=filter, start=start, limit=limit)
        print("The response of ProjectApi->get_users_without_permission:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_users_without_permission: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **filter** | **str**| If specified only user names containing the supplied string will be returned | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetUsersWithoutAnyPermission200Response**](GetUsersWithoutAnyPermission200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of users that have not been granted any permissions for the specified project |  -  |
**401** | The currently authenticated user is not a project administrator for thespecified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_webhook**
> RestWebhook get_webhook(project_key, webhook_id, statistics=statistics)

Get webhook

Get a webhook by ID. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook import RestWebhook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    statistics = 'statistics_example' # str | <code>true</code> if statistics should be provided for the webhook (optional)

    try:
        # Get webhook
        api_response = api_instance.get_webhook(project_key, webhook_id, statistics=statistics)
        print("The response of ProjectApi->get_webhook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->get_webhook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **statistics** | **str**| &lt;code&gt;true&lt;/code&gt; if statistics should be provided for the webhook | [optional] 

### Return type

[**RestWebhook**](RestWebhook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook. |  -  |
**401** | The currently authenticated user has insufficient permissions to get a webhook in the project. |  -  |
**404** | The project does not exist, or the webhook does not exist in the project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **has_all_user_permission**
> RestPermitted has_all_user_permission(project_key, permission)

Check default project permission

Check whether the specified permission is the default permission (granted to all users) for a project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_permitted import RestPermitted
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    permission = 'permission_example' # str | The permission to grant. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN   

    try:
        # Check default project permission
        api_response = api_instance.has_all_user_permission(project_key, permission)
        print("The response of ProjectApi->has_all_user_permission:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->has_all_user_permission: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **permission** | **str**| The permission to grant. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN    | 

### Return type

[**RestPermitted**](RestPermitted.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A simple entity indicating whether the specified permission is the defaultpermission for this project. |  -  |
**400** | The request was malformed or the specified permission does not exist. |  -  |
**401** | The currently authenticated user is not an administrator for the specifiedspecified project. |  -  |
**403** | The action was disallowed as it would reduce the currently authenticated user&#39;spermission level. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **modify_all_user_permission**
> modify_all_user_permission(project_key, permission, allow=allow)

Grant project permission

Grant or revoke a project permission to all users, i.e. set the default permission.


The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher
global permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    permission = 'permission_example' # str | The permission to grant. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN   
    allow = 'allow_example' # str | <em>true</em> to grant the specified permission to all users, or <em>false</em> to revoke it (optional)

    try:
        # Grant project permission
        api_instance.modify_all_user_permission(project_key, permission, allow=allow)
    except Exception as e:
        print("Exception when calling ProjectApi->modify_all_user_permission: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **permission** | **str**| The permission to grant. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN    | 
 **allow** | **str**| &lt;em&gt;true&lt;/em&gt; to grant the specified permission to all users, or &lt;em&gt;false&lt;/em&gt; to revoke it | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The requested permission was successfully granted or revoked. |  -  |
**400** | The request was malformed or the specified permission does not exist. |  -  |
**401** | The currently authenticated user is not an administrator for the specified project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_configuration**
> remove_configuration(project_key, script_id)

Remove a hook script

Removes the hook script from the set of hook scripts configured to run in all repositories under the project. 

This endpoint requires **PROJECT_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    script_id = 'script_id_example' # str | The ID of the hook script

    try:
        # Remove a hook script
        api_instance.remove_configuration(project_key, script_id)
    except Exception as e:
        print("Exception when calling ProjectApi->remove_configuration: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **script_id** | **str**| The ID of the hook script | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The hook script was successfully deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified project. |  -  |
**404** | The project key or hook script ID supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **retry_create_repository**
> RestRepository retry_create_repository(project_key, repository_slug)

Retry repository creation

If a create or fork operation fails, calling this method will clean up the broken repository and try again. The repository must be in an INITIALISATION_FAILED state. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository import RestRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Retry repository creation
        api_response = api_instance.retry_create_repository(project_key, repository_slug)
        print("The response of ProjectApi->retry_create_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->retry_create_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestRepository**](RestRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly created repository. |  -  |
**400** | The repository was not created due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to create a repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **revoke_permissions**
> revoke_permissions(project_key, user=user, group=group)

Revoke project permissions

Revoke all permissions for the specified project for the given groups and users.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

In addition, a user may not revoke a group's permission if their own permission would be revoked as a result, nor may they revoke their own permission unless they have a global permission that already implies that permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    user = 'user_example' # str | The names of the users (optional)
    group = 'group_example' # str | The names of the groups (optional)

    try:
        # Revoke project permissions
        api_instance.revoke_permissions(project_key, user=user, group=group)
    except Exception as e:
        print("Exception when calling ProjectApi->revoke_permissions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **user** | **str**| The names of the users | [optional] 
 **group** | **str**| The names of the groups | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | All project permissions were revoked from the users and groups for the specified project. |  -  |
**400** | No permissions were revoked because the request was invalid. No users or groups were provided. |  -  |
**401** | The currently authenticated user is not an administrator for the specifiedspecified project. |  -  |
**404** | The specified project does not exist, or one or more of the users or groups provided does not exist. |  -  |
**409** | The action was disallowed as it would revoke the currently authenticated user&#39;s permission on the project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **revoke_permissions_for_group1**
> revoke_permissions_for_group1(project_key, name=name)

Revoke group project permission

 Revoke all permissions for the specified project for a group.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

In addition, a user may not revoke a group's permissions if it will reduce their own permission level.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    name = 'name_example' # str | The name of the group (optional)

    try:
        # Revoke group project permission
        api_instance.revoke_permissions_for_group1(project_key, name=name)
    except Exception as e:
        print("Exception when calling ProjectApi->revoke_permissions_for_group1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **name** | **str**| The name of the group | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | All project permissions were revoked from the group for the specified project. |  -  |
**401** | The currently authenticated user is not an administrator for the specifiedspecified project. |  -  |
**404** | The specified project does not exist. |  -  |
**409** |  The action was disallowed as it would reduce the currently authenticated user&#39;spermission level. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **revoke_permissions_for_user1**
> revoke_permissions_for_user1(project_key, name=name)

Revoke user project permission

Revoke all permissions for the specified project for a user.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

In addition, a user may not revoke their own project permissions if they do not have a higher global permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    name = 'name_example' # str | The name of the user (optional)

    try:
        # Revoke user project permission
        api_instance.revoke_permissions_for_user1(project_key, name=name)
    except Exception as e:
        print("Exception when calling ProjectApi->revoke_permissions_for_user1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **name** | **str**| The name of the user | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | All project permissions were revoked from the user for the specified project. |  -  |
**401** | The currently authenticated user is not an administrator for the specifiedspecified project. |  -  |
**404** | The specified project does not exist. |  -  |
**409** |  The action was disallowed as it would reduce the currently authenticated user&#39;spermission level. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **search_permissions**
> search_permissions(project_key, permission=permission, filter_text=filter_text, type=type)

Search project permissions

Search direct and implied permissions of principals (users and groups). This endpoint returns a superset of the results returned by the /users and /groups endpoints because it allows filtering by global permissions too.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    permission = 'permission_example' # str | Permissions to filter by. See the [permissions documentation](https://confluence.atlassian.com/display/BitbucketServer/Using+project+permissions)for a detailed explanation of what each permission entails. This parameter can be specified multiple times to filter by more than one permission, and can contain global and project permissions.   (optional)
    filter_text = 'filter_text_example' # str | Name of the user or group to filter the name of (optional)
    type = 'type_example' # str | Type of entity (user or group)Valid entity types are:  - USER- GROUP (optional)

    try:
        # Search project permissions
        api_instance.search_permissions(project_key, permission=permission, filter_text=filter_text, type=type)
    except Exception as e:
        print("Exception when calling ProjectApi->search_permissions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **permission** | **str**| Permissions to filter by. See the [permissions documentation](https://confluence.atlassian.com/display/BitbucketServer/Using+project+permissions)for a detailed explanation of what each permission entails. This parameter can be specified multiple times to filter by more than one permission, and can contain global and project permissions.   | [optional] 
 **filter_text** | **str**| Name of the user or group to filter the name of | [optional] 
 **type** | **str**| Type of entity (user or group)Valid entity types are:  - USER- GROUP | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**0** | default response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set**
> RestAutoMergeRestrictedSettings set(project_key, rest_auto_merge_project_settings_request=rest_auto_merge_project_settings_request)

Create or update the pull request auto-merge settings

Creates or updates the pull request auto-merge settings for the supplied project, and applies the restriction action specified in the request.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_merge_project_settings_request import RestAutoMergeProjectSettingsRequest
from .models.rest_auto_merge_restricted_settings import RestAutoMergeRestrictedSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    rest_auto_merge_project_settings_request = .RestAutoMergeProjectSettingsRequest() # RestAutoMergeProjectSettingsRequest | The settings to create or update (optional)

    try:
        # Create or update the pull request auto-merge settings
        api_response = api_instance.set(project_key, rest_auto_merge_project_settings_request=rest_auto_merge_project_settings_request)
        print("The response of ProjectApi->set:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->set: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **rest_auto_merge_project_settings_request** | [**RestAutoMergeProjectSettingsRequest**](RestAutoMergeProjectSettingsRequest.md)| The settings to create or update | [optional] 

### Return type

[**RestAutoMergeRestrictedSettings**](RestAutoMergeRestrictedSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The pull request auto-merge settings |  -  |
**400** | The &#39;enabled&#39; and &#39;restrictionAction&#39; fields were not provided correctly. |  -  |
**401** | The currently authenticated user has insufficient permissions to create or update the pull request auto-merge settings. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_auto_decline_settings**
> RestAutoDeclineSettings set_auto_decline_settings(project_key, rest_auto_decline_settings_request=rest_auto_decline_settings_request)

Create/Update auto decline settings

Creates or updates the auto decline settings for the supplied project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_decline_settings import RestAutoDeclineSettings
from .models.rest_auto_decline_settings_request import RestAutoDeclineSettingsRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    rest_auto_decline_settings_request = .RestAutoDeclineSettingsRequest() # RestAutoDeclineSettingsRequest | The settings to create or update (optional)

    try:
        # Create/Update auto decline settings
        api_response = api_instance.set_auto_decline_settings(project_key, rest_auto_decline_settings_request=rest_auto_decline_settings_request)
        print("The response of ProjectApi->set_auto_decline_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->set_auto_decline_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **rest_auto_decline_settings_request** | [**RestAutoDeclineSettingsRequest**](RestAutoDeclineSettingsRequest.md)| The settings to create or update | [optional] 

### Return type

[**RestAutoDeclineSettings**](RestAutoDeclineSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The auto decline settings |  -  |
**400** | inactivityWeeks was not one of 1, 2, 4, 8, or, 12, or the enabled parameter was not included in the request. |  -  |
**401** | The currently authenticated user has insufficient permissions to create or update the auto decline settings. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_configuration**
> RestHookScriptConfig set_configuration(project_key, script_id, rest_hook_script_triggers=rest_hook_script_triggers)

Create/update a hook script

Creates/updates the hook script configuration for the provided hook script and project. 

This endpoint requires **PROJECT_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_hook_script_config import RestHookScriptConfig
from .models.rest_hook_script_triggers import RestHookScriptTriggers
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    script_id = 'script_id_example' # str | The ID of the hook script
    rest_hook_script_triggers = .RestHookScriptTriggers() # RestHookScriptTriggers | The hook triggers for which the hook script should be run (optional)

    try:
        # Create/update a hook script
        api_response = api_instance.set_configuration(project_key, script_id, rest_hook_script_triggers=rest_hook_script_triggers)
        print("The response of ProjectApi->set_configuration:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->set_configuration: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **script_id** | **str**| The ID of the hook script | 
 **rest_hook_script_triggers** | [**RestHookScriptTriggers**](RestHookScriptTriggers.md)| The hook triggers for which the hook script should be run | [optional] 

### Return type

[**RestHookScriptConfig**](RestHookScriptConfig.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated hook script. |  -  |
**400** | The hook script was not created/updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified project. |  -  |
**404** | The project key supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_default_branch2**
> set_default_branch2(project_key, repository_slug, rest_branch=rest_branch)

Update default branch for repository

Update the default branch of a repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_branch import RestBranch
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_branch = .RestBranch() # RestBranch | The branch to set as default (optional)

    try:
        # Update default branch for repository
        api_instance.set_default_branch2(project_key, repository_slug, rest_branch=rest_branch)
    except Exception as e:
        print("Exception when calling ProjectApi->set_default_branch2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_branch** | [**RestBranch**](RestBranch.md)| The branch to set as default | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default branch was updated. |  -  |
**401** | The authenticated user does not have permission to modify the default branch. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_permission_for_groups1**
> set_permission_for_groups1(project_key, name=name, permission=permission)

Update group project permission

Promote or demote a group's permission level for the specified project.

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource. In addition, a user may not demote a group's permission level if theirown permission level would be reduced as a result.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    name = 'name_example' # str | The names of the groups (optional)
    permission = 'permission_example' # str | The permission to grant.See the [permissions documentation](https://confluence.atlassian.com/display/BitbucketServer/Using+project+permissions)for a detailed explanation of what each permission entails. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN    (optional)

    try:
        # Update group project permission
        api_instance.set_permission_for_groups1(project_key, name=name, permission=permission)
    except Exception as e:
        print("Exception when calling ProjectApi->set_permission_for_groups1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **name** | **str**| The names of the groups | [optional] 
 **permission** | **str**| The permission to grant.See the [permissions documentation](https://confluence.atlassian.com/display/BitbucketServer/Using+project+permissions)for a detailed explanation of what each permission entails. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN    | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The requested permission was granted. |  -  |
**400** | The request was malformed or the specified permission does not exist. |  -  |
**401** | The currently authenticated user is not an administrator for the specifiedspecified project. |  -  |
**403** | The action was disallowed as it would reduce the currently authenticated user&#39;spermission level. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_permission_for_users1**
> set_permission_for_users1(project_key, name=name, permission=permission)

Update user project permission

Promote or demote a user's permission level for the specified project.


The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project or a higher global permission to call this resource. In addition, a user may not reduce their own permission level unless they have a global permission that already implies that permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key
    name = 'name_example' # str | The names of the users (optional)
    permission = 'permission_example' # str | The permission to grant.See the [permissions documentation](https://confluence.atlassian.com/display/BitbucketServer/Using+project+permissions)for a detailed explanation of what each permission entails. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN    (optional)

    try:
        # Update user project permission
        api_instance.set_permission_for_users1(project_key, name=name, permission=permission)
    except Exception as e:
        print("Exception when calling ProjectApi->set_permission_for_users1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **name** | **str**| The names of the users | [optional] 
 **permission** | **str**| The permission to grant.See the [permissions documentation](https://confluence.atlassian.com/display/BitbucketServer/Using+project+permissions)for a detailed explanation of what each permission entails. Available project permissions are:  - PROJECT_READ - PROJECT_WRITE - PROJECT_ADMIN    | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The requested permission was granted. |  -  |
**400** | The request was malformed or the specified permission does not exist. |  -  |
**401** | The currently authenticated user is not an administrator for the specifiedspecified project. |  -  |
**403** | The action was disallowed as it would reduce the currently authenticated user&#39;spermission level. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_settings**
> ExampleSettings set_settings(project_key, hook_key, example_settings=example_settings)

Update repository hook settings

Modify the settings for a repository hook for this project. 

The service will reject any settings which are too large, the current limit is 32KB once serialized. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource. 

A JSON document can be provided to use as the settings for the hook. These structure and validity of the document is decided by the plugin providing the hook.

### Example


```python
import bitbucketclient
from .models.example_settings import ExampleSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The complete module key of the hook module.
    example_settings = .ExampleSettings() # ExampleSettings | The raw settings. (optional)

    try:
        # Update repository hook settings
        api_response = api_instance.set_settings(project_key, hook_key, example_settings=example_settings)
        print("The response of ProjectApi->set_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->set_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The complete module key of the hook module. | 
 **example_settings** | [**ExampleSettings**](ExampleSettings.md)| The raw settings. | [optional] 

### Return type

[**ExampleSettings**](ExampleSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The settings for the hook. |  -  |
**400** | The settings specified are invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to modify the hook settings. |  -  |
**404** | The specified project or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_contributing**
> stream_contributing(project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)

Get repository contributing guidelines

Retrieves the contributing guidelines for the repository, if they've been defined. 

This checks the repository for a CONTRIBUTING file, optionally with an md or txt extension, and, if found, streams it. By default, the <i>raw content</i> of the file is streamed. Appending <code>?markup</code> to the URL will stream an HTML-rendered version instead. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | A specific commit or ref to retrieve the guidelines at, or the default branch if not specified (optional)
    markup = 'markup_example' # str | If present or <code>\"true\"</code>, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than <code>\"true\"</code>, the content is streamed without markup (optional)
    html_escape = 'html_escape_example' # str | (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the <code>markup.render.html.escape</code> property, which is <code>true</code> by default, will be used (optional)
    include_heading_id = 'include_heading_id_example' # str | (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the <code>markup.render.headerids</code> property, which is false by default, will be used (optional)
    hardwrap = 'hardwrap_example' # str | (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the <code>markup.render.hardwrap</code> property, which is <code>true</code> by default, will be used (optional)

    try:
        # Get repository contributing guidelines
        api_instance.stream_contributing(project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)
    except Exception as e:
        print("Exception when calling ProjectApi->stream_contributing: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| A specific commit or ref to retrieve the guidelines at, or the default branch if not specified | [optional] 
 **markup** | **str**| If present or &lt;code&gt;\&quot;true\&quot;&lt;/code&gt;, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than &lt;code&gt;\&quot;true\&quot;&lt;/code&gt;, the content is streamed without markup | [optional] 
 **html_escape** | **str**| (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the &lt;code&gt;markup.render.html.escape&lt;/code&gt; property, which is &lt;code&gt;true&lt;/code&gt; by default, will be used | [optional] 
 **include_heading_id** | **str**| (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the &lt;code&gt;markup.render.headerids&lt;/code&gt; property, which is false by default, will be used | [optional] 
 **hardwrap** | **str**| (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the &lt;code&gt;markup.render.hardwrap&lt;/code&gt; property, which is &lt;code&gt;true&lt;/code&gt; by default, will be used | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The contributing guidelines for the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_license**
> stream_license(project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)

Get repository license

Retrieves the license for the repository, if it's been defined. 

This checks the repository for a <pre>LICENSE</pre> file, optionally with an <pre>md</pre> or <pre>txt</pre>extension, and, if found, streams it. By default, the <i>raw content</i> of the file is streamed. Appending <pre>?markup</pre> to the URL will stream an HTML-rendered version instead. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | A specific commit or ref to retrieve the guidelines at, or the default branch if not specified (optional)
    markup = 'markup_example' # str | If present or <code>\"true\"</code>, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than <code>\"true\"</code>, the content is streamed without markup (optional)
    html_escape = 'html_escape_example' # str | (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the <code>markup.render.html.escape</code> property, which is <code>true</code> by default, will be used (optional)
    include_heading_id = 'include_heading_id_example' # str | (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the <code>markup.render.headerids</code> property, which is false by default, will be used (optional)
    hardwrap = 'hardwrap_example' # str | (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the <code>markup.render.hardwrap</code> property, which is <code>true</code> by default, will be used (optional)

    try:
        # Get repository license
        api_instance.stream_license(project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)
    except Exception as e:
        print("Exception when calling ProjectApi->stream_license: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| A specific commit or ref to retrieve the guidelines at, or the default branch if not specified | [optional] 
 **markup** | **str**| If present or &lt;code&gt;\&quot;true\&quot;&lt;/code&gt;, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than &lt;code&gt;\&quot;true\&quot;&lt;/code&gt;, the content is streamed without markup | [optional] 
 **html_escape** | **str**| (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the &lt;code&gt;markup.render.html.escape&lt;/code&gt; property, which is &lt;code&gt;true&lt;/code&gt; by default, will be used | [optional] 
 **include_heading_id** | **str**| (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the &lt;code&gt;markup.render.headerids&lt;/code&gt; property, which is false by default, will be used | [optional] 
 **hardwrap** | **str**| (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the &lt;code&gt;markup.render.hardwrap&lt;/code&gt; property, which is &lt;code&gt;true&lt;/code&gt; by default, will be used | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The license for the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_readme**
> stream_readme(project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)

Get repository readme

Retrieves the README for the repository, if it's been defined. 

This checks the repository for a <pre>README</pre> file, optionally with an <pre>md</pre> or <pre>txt</pre>extension, and, if found, streams it. By default, the <i>raw content</i> of the file is streamed. Appending <pre>?markup</pre> to the URL will stream an HTML-rendered version instead. Note that, when streaming HTML, relative URLs in the README will not work if applied relative to this URL. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | A specific commit or ref to retrieve the guidelines at, or the default branch if not specified (optional)
    markup = 'markup_example' # str | If present or <code>\"true\"</code>, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than <code>\"true\"</code>, the content is streamed without markup (optional)
    html_escape = 'html_escape_example' # str | (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the <code>markup.render.html.escape</code> property, which is <code>true</code> by default, will be used (optional)
    include_heading_id = 'include_heading_id_example' # str | (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the <code>markup.render.headerids</code> property, which is false by default, will be used (optional)
    hardwrap = 'hardwrap_example' # str | (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the <code>markup.render.hardwrap</code> property, which is <code>true</code> by default, will be used (optional)

    try:
        # Get repository readme
        api_instance.stream_readme(project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)
    except Exception as e:
        print("Exception when calling ProjectApi->stream_readme: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| A specific commit or ref to retrieve the guidelines at, or the default branch if not specified | [optional] 
 **markup** | **str**| If present or &lt;code&gt;\&quot;true\&quot;&lt;/code&gt;, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than &lt;code&gt;\&quot;true\&quot;&lt;/code&gt;, the content is streamed without markup | [optional] 
 **html_escape** | **str**| (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the &lt;code&gt;markup.render.html.escape&lt;/code&gt; property, which is &lt;code&gt;true&lt;/code&gt; by default, will be used | [optional] 
 **include_heading_id** | **str**| (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the &lt;code&gt;markup.render.headerids&lt;/code&gt; property, which is false by default, will be used | [optional] 
 **hardwrap** | **str**| (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the &lt;code&gt;markup.render.hardwrap&lt;/code&gt; property, which is &lt;code&gt;true&lt;/code&gt; by default, will be used | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: */*, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The README for the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **test_webhook**
> object test_webhook(project_key, webhook_id=webhook_id, ssl_verification_required=ssl_verification_required, url=url, rest_webhook_credentials=rest_webhook_credentials)

Test webhook

Test connectivity to a specific endpoint. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook_credentials import RestWebhookCredentials
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 56 # int |  (optional)
    ssl_verification_required = True # bool |  (optional) (default to True)
    url = 'url_example' # str | The url in which to connect to (optional)
    rest_webhook_credentials = .RestWebhookCredentials() # RestWebhookCredentials | Basic authentication credentials, if required. (optional)

    try:
        # Test webhook
        api_response = api_instance.test_webhook(project_key, webhook_id=webhook_id, ssl_verification_required=ssl_verification_required, url=url, rest_webhook_credentials=rest_webhook_credentials)
        print("The response of ProjectApi->test_webhook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->test_webhook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **int**|  | [optional] 
 **ssl_verification_required** | **bool**|  | [optional] [default to True]
 **url** | **str**| The url in which to connect to | [optional] 
 **rest_webhook_credentials** | [**RestWebhookCredentials**](RestWebhookCredentials.md)| Basic authentication credentials, if required. | [optional] 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook. |  -  |
**401** | The currently authenticated user has insufficient permissions to test a connection. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_default_task**
> RestDefaultTask update_default_task(project_key, task_id, rest_default_task_request)

Update a default task

Updates a default task for the supplied project.

The authenticated user must have **PROJECT_ADMIN** permission for this project to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_default_task import RestDefaultTask
from .models.rest_default_task_request import RestDefaultTaskRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    task_id = 'task_id_example' # str | The ID of the default task
    rest_default_task_request = .RestDefaultTaskRequest() # RestDefaultTaskRequest | The task to be updated

    try:
        # Update a default task
        api_response = api_instance.update_default_task(project_key, task_id, rest_default_task_request)
        print("The response of ProjectApi->update_default_task:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->update_default_task: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **task_id** | **str**| The ID of the default task | 
 **rest_default_task_request** | [**RestDefaultTaskRequest**](RestDefaultTaskRequest.md)| The task to be updated | 

### Return type

[**RestDefaultTask**](RestDefaultTask.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default task |  -  |
**400** | One or more of the following error cases occurred (check the error message for more details):    - the provided taskId does not exist- the description is empty- the sourceMatcher or targetMatcher is invalid |  -  |
**401** | The currently authenticated user has insufficient permissions to add a default task |  -  |
**404** | The specified project does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_project**
> RestProject update_project(project_key, rest_project=rest_project)

Update project

Update the project matching the <strong>projectKey</strong> supplied in the resource path. 

To include a custom avatar for the updated project, the project definition should contain an additional attribute with the key <code>avatar</code> and the value a data URI containing Base64-encoded image data. The URI should be in the following format: 
```    data:(content type, e.g. image/png);base64,(data)```

If the data is not Base64-encoded, or if a character set is defined in the URI, or the URI is otherwise invalid, <em>project creation will fail</em>. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_project import RestProject
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_project = .RestProject() # RestProject | Project parameters to update. (optional)

    try:
        # Update project
        api_response = api_instance.update_project(project_key, rest_project=rest_project)
        print("The response of ProjectApi->update_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->update_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_project** | [**RestProject**](RestProject.md)| Project parameters to update. | [optional] 

### Return type

[**RestProject**](RestProject.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated project. The project&#39;s key &lt;strong&gt;was not&lt;/strong&gt; updated. |  -  |
**201** | The updated project. The project&#39;s key &lt;strong&gt;was&lt;/strong&gt; updated. |  -  |
**400** | The project was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_pull_request_settings**
> RestPullRequestSettings update_pull_request_settings(project_key, scm_id, rest_pull_request_settings=rest_pull_request_settings)

Update merge strategy

Update the pull request merge strategy configuration for this project and SCM. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the context repository to call this resource. 

Only the strategies provided will be enabled, the default must be set and included in the set of strategies. 

An explicitly set pull request merge strategy configuration can be deleted by POSTing a document with an empty "mergeConfig" attribute. i.e: 
<pre>{ 
    "mergeConfig": {} 
} 
</pre>

Upon completion of this request, the effective configuration will be the configuration explicitly set for the SCM, or if no such explicit configuration is set then the default configuration will be used.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_settings import RestPullRequestSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    scm_id = 'scm_id_example' # str | The SCM to get strategies for.
    rest_pull_request_settings = .RestPullRequestSettings() # RestPullRequestSettings | The settings. (optional)

    try:
        # Update merge strategy
        api_response = api_instance.update_pull_request_settings(project_key, scm_id, rest_pull_request_settings=rest_pull_request_settings)
        print("The response of ProjectApi->update_pull_request_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->update_pull_request_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **scm_id** | **str**| The SCM to get strategies for. | 
 **rest_pull_request_settings** | [**RestPullRequestSettings**](RestPullRequestSettings.md)| The settings. | [optional] 

### Return type

[**RestPullRequestSettings**](RestPullRequestSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The merge configuration of the request project. |  -  |
**400** | The repository pull request merge strategies were not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to administrate the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_repository**
> RestRepository update_repository(project_key, repository_slug, rest_repository=rest_repository)

Update repository

Update the repository matching the <strong>repositorySlug</strong> supplied in the resource path. 

The repository's slug is derived from its name. If the name changes the slug may also change. 

This resource can be used to change the repository's default branch by specifying a new default branch in the request. For example: <code>"defaultBranch":"main"</code>

This resource can be used to move the repository to a different project by specifying a new project in the request. For example: <code>"project":{"key":"NEW_KEY"}</code>

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository import RestRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_repository = .RestRepository() # RestRepository | The updated repository. (optional)

    try:
        # Update repository
        api_response = api_instance.update_repository(project_key, repository_slug, rest_repository=rest_repository)
        print("The response of ProjectApi->update_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->update_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_repository** | [**RestRepository**](RestRepository.md)| The updated repository. | [optional] 

### Return type

[**RestRepository**](RestRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The updated repository. |  -  |
**400** | The repository was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to update a repository. |  -  |
**403** | Cannot archive repository because it has open pull requests. |  -  |
**404** | The specified repository does not exist. |  -  |
**409** | A repository with the same name as the target already exists, or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_webhook**
> RestWebhook update_webhook(project_key, webhook_id, rest_webhook=rest_webhook)

Update webhook

Update an existing webhook. 

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook import RestWebhook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | Id of the existing webhook
    rest_webhook = .RestWebhook() # RestWebhook | The representation of the updated values for the webhook (optional)

    try:
        # Update webhook
        api_response = api_instance.update_webhook(project_key, webhook_id, rest_webhook=rest_webhook)
        print("The response of ProjectApi->update_webhook:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling ProjectApi->update_webhook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| Id of the existing webhook | 
 **rest_webhook** | [**RestWebhook**](RestWebhook.md)| The representation of the updated values for the webhook | [optional] 

### Return type

[**RestWebhook**](RestWebhook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook. |  -  |
**401** | The currently authenticated user has insufficient permissions to update a webhook in this project. |  -  |
**404** | The project does not exist, or the webhook does not exist in the project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upload_avatar**
> upload_avatar(project_key, avatar=avatar)

Update project avatar

Update the avatar for the project matching the supplied <strong>projectKey</strong>. 

This resource accepts POST multipart form data, containing a single image in a form-field named 'avatar'. 

There are configurable server limits on both the dimensions (1024x1024 pixels by default) and uploaded file size (1MB by default). Several different image formats are supported, but <strong>PNG</strong> and <strong>JPEG</strong> are preferred due to the file size limit. 

This resource has Cross-Site Request Forgery (XSRF) protection. To allow the request to pass the XSRF check the caller needs to send an <code>X-Atlassian-Token</code> HTTP header with the value <code>no-check</code>. 

An example <a href="http://curl.haxx.se/">curl</a> request to upload an image name 'avatar.png' would be: ```curl -X POST -u username:password -H "X-Atlassian-Token: no-check" http://example.com/rest/api/1.0/projects/STASH/avatar.png -F avatar=@avatar.png ```

The authenticated user must have <strong>PROJECT_ADMIN</strong> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .ProjectApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    avatar = None # bytearray | The avatar file to upload. (optional)

    try:
        # Update project avatar
        api_instance.upload_avatar(project_key, avatar=avatar)
    except Exception as e:
        print("Exception when calling ProjectApi->upload_avatar: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **avatar** | **bytearray**| The avatar file to upload. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The avatar was uploaded successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

