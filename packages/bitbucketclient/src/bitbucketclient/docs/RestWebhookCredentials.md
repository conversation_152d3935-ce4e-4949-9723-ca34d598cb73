# RestWebhookCredentials


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**password** | **str** |  | [optional] 
**username** | **str** |  | [optional] 

## Example

```python
from .models.rest_webhook_credentials import RestWebhookCredentials

# TODO update the JSON string below
json = "{}"
# create an instance of RestWebhookCredentials from a JSON string
rest_webhook_credentials_instance = RestWebhookCredentials.from_json(json)
# print the JSON string representation of the object
print(RestWebhookCredentials.to_json())

# convert the object into a dict
rest_webhook_credentials_dict = rest_webhook_credentials_instance.to_dict()
# create an instance of RestWebhookCredentials from a dict
rest_webhook_credentials_from_dict = RestWebhookCredentials.from_dict(rest_webhook_credentials_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


