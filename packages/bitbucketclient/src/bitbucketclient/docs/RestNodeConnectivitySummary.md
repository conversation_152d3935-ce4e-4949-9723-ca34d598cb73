# RestNodeConnectivitySummary


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**node** | [**RestNodeConnectivityReportNode**](RestNodeConnectivityReportNode.md) |  | [optional] 
**summary** | [**RestNodeConnectivitySummarySummary**](RestNodeConnectivitySummarySummary.md) |  | [optional] 

## Example

```python
from .models.rest_node_connectivity_summary import RestNodeConnectivitySummary

# TODO update the JSON string below
json = "{}"
# create an instance of RestNodeConnectivitySummary from a JSON string
rest_node_connectivity_summary_instance = RestNodeConnectivitySummary.from_json(json)
# print the JSON string representation of the object
print(RestNodeConnectivitySummary.to_json())

# convert the object into a dict
rest_node_connectivity_summary_dict = rest_node_connectivity_summary_instance.to_dict()
# create an instance of RestNodeConnectivitySummary from a dict
rest_node_connectivity_summary_from_dict = RestNodeConnectivitySummary.from_dict(rest_node_connectivity_summary_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


