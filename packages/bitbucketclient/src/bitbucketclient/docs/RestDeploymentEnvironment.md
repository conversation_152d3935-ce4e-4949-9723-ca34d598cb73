# RestDeploymentEnvironment


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**display_name** | **str** |  | 
**key** | **str** |  | 
**type** | **str** |  | [optional] 
**url** | **str** |  | [optional] 

## Example

```python
from .models.rest_deployment_environment import RestDeploymentEnvironment

# TODO update the JSON string below
json = "{}"
# create an instance of RestDeploymentEnvironment from a JSON string
rest_deployment_environment_instance = RestDeploymentEnvironment.from_json(json)
# print the JSON string representation of the object
print(RestDeploymentEnvironment.to_json())

# convert the object into a dict
rest_deployment_environment_dict = rest_deployment_environment_instance.to_dict()
# create an instance of RestDeploymentEnvironment from a dict
rest_deployment_environment_from_dict = RestDeploymentEnvironment.from_dict(rest_deployment_environment_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


