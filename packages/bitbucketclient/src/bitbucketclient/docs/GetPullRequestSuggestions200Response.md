# GetPullRequestSuggestions200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**is_last_page** | **bool** |  | [optional] 
**limit** | **float** |  | [optional] 
**next_page_start** | **int** |  | [optional] 
**size** | **float** |  | [optional] 
**start** | **int** |  | [optional] 
**values** | [**List[RestPullRequestSuggestion]**](RestPullRequestSuggestion.md) |  | [optional] 

## Example

```python
from .models.get_pull_request_suggestions200_response import GetPullRequestSuggestions200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetPullRequestSuggestions200Response from a JSON string
get_pull_request_suggestions200_response_instance = GetPullRequestSuggestions200Response.from_json(json)
# print the JSON string representation of the object
print(GetPullRequestSuggestions200Response.to_json())

# convert the object into a dict
get_pull_request_suggestions200_response_dict = get_pull_request_suggestions200_response_instance.to_dict()
# create an instance of GetPullRequestSuggestions200Response from a dict
get_pull_request_suggestions200_response_from_dict = GetPullRequestSuggestions200Response.from_dict(get_pull_request_suggestions200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


