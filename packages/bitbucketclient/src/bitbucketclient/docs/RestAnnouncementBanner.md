# RestAnnouncementBanner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**audience** | **str** |  | [optional] 
**enabled** | **bool** |  | [optional] 
**message** | **str** |  | [optional] 

## Example

```python
from .models.rest_announcement_banner import RestAnnouncementBanner

# TODO update the JSON string below
json = "{}"
# create an instance of RestAnnouncementBanner from a JSON string
rest_announcement_banner_instance = RestAnnouncementBanner.from_json(json)
# print the JSON string representation of the object
print(RestAnnouncementBanner.to_json())

# convert the object into a dict
rest_announcement_banner_dict = rest_announcement_banner_instance.to_dict()
# create an instance of RestAnnouncementBanner from a dict
rest_announcement_banner_from_dict = RestAnnouncementBanner.from_dict(rest_announcement_banner_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


