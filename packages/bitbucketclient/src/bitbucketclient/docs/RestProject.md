# RestProject


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**avatar** | **str** |  | [optional] 
**avatar_url** | **str** |  | [optional] 
**description** | **str** |  | [optional] [readonly] 
**id** | **int** |  | [optional] [readonly] 
**key** | **str** |  | [optional] 
**links** | **object** |  | [optional] 
**name** | **str** |  | [optional] [readonly] 
**public** | **bool** |  | [optional] [readonly] 
**scope** | **str** |  | [optional] [readonly] 
**type** | **str** |  | [optional] [readonly] 

## Example

```python
from .models.rest_project import RestProject

# TODO update the JSON string below
json = "{}"
# create an instance of RestProject from a JSON string
rest_project_instance = RestProject.from_json(json)
# print the JSON string representation of the object
print(RestProject.to_json())

# convert the object into a dict
rest_project_dict = rest_project_instance.to_dict()
# create an instance of RestProject from a dict
rest_project_from_dict = RestProject.from_dict(rest_project_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


