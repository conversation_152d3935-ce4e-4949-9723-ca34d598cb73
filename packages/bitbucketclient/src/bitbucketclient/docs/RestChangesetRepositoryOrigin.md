# RestChangesetRepositoryOrigin


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**archived** | **bool** |  | [optional] [readonly] 
**default_branch** | **str** |  | [optional] 
**description** | **str** |  | [optional] [readonly] 
**forkable** | **bool** |  | [optional] [readonly] 
**hierarchy_id** | **str** |  | [optional] [readonly] 
**id** | **int** |  | [optional] [readonly] 
**links** | **object** |  | [optional] 
**name** | **str** |  | [optional] 
**partition** | **int** |  | [optional] [readonly] 
**project** | [**RestChangesetRepositoryOriginProject**](RestChangesetRepositoryOriginProject.md) |  | [optional] 
**public** | **bool** |  | [optional] [readonly] 
**related_links** | **object** |  | [optional] [readonly] 
**scm_id** | **str** |  | [optional] 
**scope** | **str** |  | [optional] [readonly] 
**slug** | **str** |  | [optional] 
**state** | **str** |  | [optional] [readonly] 
**status_message** | **str** |  | [optional] [readonly] 

## Example

```python
from .models.rest_changeset_repository_origin import RestChangesetRepositoryOrigin

# TODO update the JSON string below
json = "{}"
# create an instance of RestChangesetRepositoryOrigin from a JSON string
rest_changeset_repository_origin_instance = RestChangesetRepositoryOrigin.from_json(json)
# print the JSON string representation of the object
print(RestChangesetRepositoryOrigin.to_json())

# convert the object into a dict
rest_changeset_repository_origin_dict = rest_changeset_repository_origin_instance.to_dict()
# create an instance of RestChangesetRepositoryOrigin from a dict
rest_changeset_repository_origin_from_dict = RestChangesetRepositoryOrigin.from_dict(rest_changeset_repository_origin_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


