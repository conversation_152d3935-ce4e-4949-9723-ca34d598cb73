# TotpRecoveryCodeDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**recovery_code** | **str** |  | [optional] 

## Example

```python
from .models.totp_recovery_code_dto import TotpRecoveryCodeDTO

# TODO update the JSON string below
json = "{}"
# create an instance of TotpRecoveryCodeDTO from a JSON string
totp_recovery_code_dto_instance = TotpRecoveryCodeDTO.from_json(json)
# print the JSON string representation of the object
print(TotpRecoveryCodeDTO.to_json())

# convert the object into a dict
totp_recovery_code_dto_dict = totp_recovery_code_dto_instance.to_dict()
# create an instance of TotpRecoveryCodeDTO from a dict
totp_recovery_code_dto_from_dict = TotpRecoveryCodeDTO.from_dict(totp_recovery_code_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


