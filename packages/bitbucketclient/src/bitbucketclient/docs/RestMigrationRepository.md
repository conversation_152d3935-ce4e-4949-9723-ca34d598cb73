# RestMigrationRepository


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**migration_state** | **str** |  | [optional] 
**repository** | [**RestChangesetRepository**](RestChangesetRepository.md) |  | [optional] 

## Example

```python
from .models.rest_migration_repository import RestMigrationRepository

# TODO update the JSON string below
json = "{}"
# create an instance of RestMigrationRepository from a JSON string
rest_migration_repository_instance = RestMigrationRepository.from_json(json)
# print the JSON string representation of the object
print(RestMigrationRepository.to_json())

# convert the object into a dict
rest_migration_repository_dict = rest_migration_repository_instance.to_dict()
# create an instance of RestMigrationRepository from a dict
rest_migration_repository_from_dict = RestMigrationRepository.from_dict(rest_migration_repository_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


