# .PullRequestsApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**apply_suggestion**](PullRequestsApi.md#apply_suggestion) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments/{commentId}/apply-suggestion | Apply pull request suggestion
[**approve**](PullRequestsApi.md#approve) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/approve | Approve pull request
[**assign_participant_role**](PullRequestsApi.md#assign_participant_role) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants | Assign pull request participant role
[**can_merge**](PullRequestsApi.md#can_merge) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/merge | Test if pull request can be merged
[**can_rebase**](PullRequestsApi.md#can_rebase) | **GET** /git/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/rebase | Check PR rebase precondition
[**cancel_auto_merge**](PullRequestsApi.md#cancel_auto_merge) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/auto-merge | Cancel auto-merge for pull request
[**create**](PullRequestsApi.md#create) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests | Create pull request
[**create1**](PullRequestsApi.md#create1) | **POST** /api/latest/projects/{projectKey}/settings/reviewer-groups | Create reviewer group
[**create2**](PullRequestsApi.md#create2) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/reviewer-groups | Create reviewer group
[**create_comment1**](PullRequestsApi.md#create_comment1) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/blocker-comments | Add new blocker comment
[**create_comment2**](PullRequestsApi.md#create_comment2) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments | Add pull request comment
[**create_pull_request_condition**](PullRequestsApi.md#create_pull_request_condition) | **POST** /default-reviewers/latest/projects/{projectKey}/condition | Create default reviewer condition
[**create_pull_request_condition1**](PullRequestsApi.md#create_pull_request_condition1) | **POST** /default-reviewers/latest/projects/{projectKey}/repos/{repositorySlug}/condition | Create default reviewer condition
[**decline**](PullRequestsApi.md#decline) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/decline | Decline pull request
[**delete3**](PullRequestsApi.md#delete3) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId} | Delete pull request
[**delete6**](PullRequestsApi.md#delete6) | **DELETE** /api/latest/projects/{projectKey}/settings/reviewer-groups/{id} | Delete reviewer group
[**delete7**](PullRequestsApi.md#delete7) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/reviewer-groups/{id} | Delete reviewer group
[**delete_comment1**](PullRequestsApi.md#delete_comment1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/blocker-comments/{commentId} | Delete pull request comment
[**delete_comment2**](PullRequestsApi.md#delete_comment2) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments/{commentId} | Delete a pull request comment
[**delete_pull_request_condition**](PullRequestsApi.md#delete_pull_request_condition) | **DELETE** /default-reviewers/latest/projects/{projectKey}/condition/{id} | Delete default reviewer condition
[**delete_pull_request_condition1**](PullRequestsApi.md#delete_pull_request_condition1) | **DELETE** /default-reviewers/latest/projects/{projectKey}/repos/{repositorySlug}/condition/{id} | Delete default reviewer condition
[**discard_review**](PullRequestsApi.md#discard_review) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/review | Discard pull request review
[**finish_review**](PullRequestsApi.md#finish_review) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/review | Complete pull request review
[**get3**](PullRequestsApi.md#get3) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId} | Get pull request
[**get_activities**](PullRequestsApi.md#get_activities) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/activities | Get pull request activity
[**get_auto_merge_request**](PullRequestsApi.md#get_auto_merge_request) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/auto-merge | Get auto-merge request for pull request
[**get_comment1**](PullRequestsApi.md#get_comment1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/blocker-comments/{commentId} | Get pull request comment
[**get_comment2**](PullRequestsApi.md#get_comment2) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments/{commentId} | Get a pull request comment
[**get_comments1**](PullRequestsApi.md#get_comments1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/blocker-comments | Search pull request comments
[**get_comments2**](PullRequestsApi.md#get_comments2) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments | Get pull request comments for path
[**get_commit_message_suggestion**](PullRequestsApi.md#get_commit_message_suggestion) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/commit-message-suggestion | Get commit message suggestion
[**get_commits1**](PullRequestsApi.md#get_commits1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/commits | Get pull request commits
[**get_diff_stats_summary2**](PullRequestsApi.md#get_diff_stats_summary2) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/diff-stats-summary/{path} | Get diff stats summary for pull request
[**get_merge_base1**](PullRequestsApi.md#get_merge_base1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/merge-base | Get the common ancestor between the latest commits of the source and target branches of the pull request
[**get_merge_config**](PullRequestsApi.md#get_merge_config) | **GET** /api/latest/admin/pull-requests/{scmId} | Get merge strategies
[**get_page**](PullRequestsApi.md#get_page) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests | Get pull requests for repository
[**get_pull_request_conditions**](PullRequestsApi.md#get_pull_request_conditions) | **GET** /default-reviewers/latest/projects/{projectKey}/conditions | Get default reviewer conditions
[**get_pull_request_conditions1**](PullRequestsApi.md#get_pull_request_conditions1) | **GET** /default-reviewers/latest/projects/{projectKey}/repos/{repositorySlug}/conditions | Get default reviewer conditions
[**get_pull_requests**](PullRequestsApi.md#get_pull_requests) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/pull-requests | Get repository pull requests containing commit
[**get_review**](PullRequestsApi.md#get_review) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/review | Get pull request comment thread
[**get_reviewer_group**](PullRequestsApi.md#get_reviewer_group) | **GET** /api/latest/projects/{projectKey}/settings/reviewer-groups/{id} | Get reviewer group
[**get_reviewer_group1**](PullRequestsApi.md#get_reviewer_group1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/reviewer-groups/{id} | Get reviewer group
[**get_reviewer_groups**](PullRequestsApi.md#get_reviewer_groups) | **GET** /api/latest/projects/{projectKey}/settings/reviewer-groups | Get all reviewer groups
[**get_reviewer_groups1**](PullRequestsApi.md#get_reviewer_groups1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/reviewer-groups | Get all reviewer groups
[**get_reviewers**](PullRequestsApi.md#get_reviewers) | **GET** /default-reviewers/latest/projects/{projectKey}/repos/{repositorySlug}/reviewers | Get required reviewers for PR creation
[**get_users**](PullRequestsApi.md#get_users) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/reviewer-groups/{id}/users | Get reviewer group users
[**list_participants**](PullRequestsApi.md#list_participants) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants | Get pull request participants
[**merge**](PullRequestsApi.md#merge) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/merge | Merge pull request
[**react1**](PullRequestsApi.md#react1) | **PUT** /comment-likes/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments/{commentId}/reactions/{emoticon} | React to a PR comment
[**rebase**](PullRequestsApi.md#rebase) | **POST** /git/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/rebase | Rebase pull request
[**reopen**](PullRequestsApi.md#reopen) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/reopen | Re-open pull request
[**search**](PullRequestsApi.md#search) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/participants | Search pull request participants
[**set_merge_config**](PullRequestsApi.md#set_merge_config) | **POST** /api/latest/admin/pull-requests/{scmId} | Update merge strategies
[**stream_changes1**](PullRequestsApi.md#stream_changes1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/changes | Gets pull request changes
[**stream_diff2**](PullRequestsApi.md#stream_diff2) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/diff/{path} | Stream a diff within a pull request
[**stream_patch1**](PullRequestsApi.md#stream_patch1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}.patch | Stream pull request as patch
[**stream_raw_diff2**](PullRequestsApi.md#stream_raw_diff2) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}.diff | Stream raw pull request diff
[**try_auto_merge**](PullRequestsApi.md#try_auto_merge) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/auto-merge | Auto-merge pull request
[**un_react1**](PullRequestsApi.md#un_react1) | **DELETE** /comment-likes/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments/{commentId}/reactions/{emoticon} | Remove a reaction from a PR comment
[**unassign_participant_role**](PullRequestsApi.md#unassign_participant_role) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants/{userSlug} | Unassign pull request participant
[**unassign_participant_role1**](PullRequestsApi.md#unassign_participant_role1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants | Unassign pull request participant
[**unwatch1**](PullRequestsApi.md#unwatch1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/watch | Stop watching pull request
[**update**](PullRequestsApi.md#update) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId} | Update pull request metadata
[**update1**](PullRequestsApi.md#update1) | **PUT** /api/latest/projects/{projectKey}/settings/reviewer-groups/{id} | Update reviewer group attributes
[**update2**](PullRequestsApi.md#update2) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/reviewer-groups/{id} | Update reviewer group attributes
[**update_comment1**](PullRequestsApi.md#update_comment1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/blocker-comments/{commentId} | Update pull request comment
[**update_comment2**](PullRequestsApi.md#update_comment2) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/comments/{commentId} | Update pull request comment
[**update_pull_request_condition**](PullRequestsApi.md#update_pull_request_condition) | **PUT** /default-reviewers/latest/projects/{projectKey}/condition/{id} | Update default reviewer condition
[**update_pull_request_condition1**](PullRequestsApi.md#update_pull_request_condition1) | **PUT** /default-reviewers/latest/projects/{projectKey}/repos/{repositorySlug}/condition/{id} | Update default reviewer condition
[**update_status**](PullRequestsApi.md#update_status) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants/{userSlug} | Change pull request status
[**watch1**](PullRequestsApi.md#watch1) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/watch | Watch pull request
[**withdraw_approval**](PullRequestsApi.md#withdraw_approval) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/approve | Unapprove pull request


# **apply_suggestion**
> apply_suggestion(project_key, comment_id, pull_request_id, repository_slug, rest_apply_suggestion_request=rest_apply_suggestion_request)

Apply pull request suggestion

Apply a suggestion contained within a comment.

### Example


```python
import bitbucketclient
from .models.rest_apply_suggestion_request import RestApplySuggestionRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_apply_suggestion_request = .RestApplySuggestionRequest() # RestApplySuggestionRequest | A request containing other parameters required to apply a suggestion - The given versions/hashes must match the server's version/hashes or the suggestion application will fail (in order to avoid applying the suggestion to the wrong place (optional)

    try:
        # Apply pull request suggestion
        api_instance.apply_suggestion(project_key, comment_id, pull_request_id, repository_slug, rest_apply_suggestion_request=rest_apply_suggestion_request)
    except Exception as e:
        print("Exception when calling PullRequestsApi->apply_suggestion: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_apply_suggestion_request** | [**RestApplySuggestionRequest**](RestApplySuggestionRequest.md)| A request containing other parameters required to apply a suggestion - The given versions/hashes must match the server&#39;s version/hashes or the suggestion application will fail (in order to avoid applying the suggestion to the wrong place | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating the suggestion has been applied. |  -  |
**400** | The suggestion was not applied due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to apply the suggestion. |  -  |
**404** | Unable to find the supplied project, repository, pull request or parent comment. |  -  |
**409** | There was an error applying the suggestion to the source branch. It must be applied manually. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **approve**
> RestPullRequestParticipant approve(project_key, pull_request_id, repository_slug)

Approve pull request

Approve a pull request as the current user. Implicitly adds the user as a participant if they are not already. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource. 

<strong>Deprecated since 4.2</strong>. Use /rest/api/1.0/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants/{userSlug} instead

### Example


```python
import bitbucketclient
from .models.rest_pull_request_participant import RestPullRequestParticipant
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Approve pull request
        api_response = api_instance.approve(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->approve:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->approve: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestPullRequestParticipant**](RestPullRequestParticipant.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details of the new participant. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | The pull request is not open. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **assign_participant_role**
> RestPullRequestParticipant assign_participant_role(project_key, pull_request_id, repository_slug, rest_pull_request_assign_participant_role_request)

Assign pull request participant role

Assigns a participant to an explicit role in pull request. Currently only the REVIEWER role may be assigned. 

If the user is not yet a participant in the pull request, they are made one and assigned the supplied role. 

If the user is already a participant in the pull request, their previous role is replaced with the supplied role unless they are already assigned the AUTHOR role which cannot be changed and will result in a Bad Request (400) response code. 

The authenticated user must have <strong>REPO_WRITE</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_assign_participant_role_request import RestPullRequestAssignParticipantRoleRequest
from .models.rest_pull_request_participant import RestPullRequestParticipant
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_pull_request_assign_participant_role_request = .RestPullRequestAssignParticipantRoleRequest() # RestPullRequestAssignParticipantRoleRequest | The participant to be added to the pull request, includes the user and their role

    try:
        # Assign pull request participant role
        api_response = api_instance.assign_participant_role(project_key, pull_request_id, repository_slug, rest_pull_request_assign_participant_role_request)
        print("The response of PullRequestsApi->assign_participant_role:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->assign_participant_role: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_pull_request_assign_participant_role_request** | [**RestPullRequestAssignParticipantRoleRequest**](RestPullRequestAssignParticipantRoleRequest.md)| The participant to be added to the pull request, includes the user and their role | 

### Return type

[**RestPullRequestParticipant**](RestPullRequestParticipant.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details of the participants in this pull request. |  -  |
**400** | The request does not have the username and role, or is attempting an invalid assignment. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | Adding reviewers isn&#39;t supported on archived repositories |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **can_merge**
> RestPullRequestMergeability can_merge(project_key, pull_request_id, repository_slug)

Test if pull request can be merged

Test whether a pull request can be merged. 

A pull request may not be merged if: 

- there are conflicts that need to be manually resolved before merging; and/or
- one or more merge checks have vetoed the merge.


The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_mergeability import RestPullRequestMergeability
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Test if pull request can be merged
        api_response = api_instance.can_merge(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->can_merge:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->can_merge: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestPullRequestMergeability**](RestPullRequestMergeability.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The mergeability status of the pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | The specified pull request is not open. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **can_rebase**
> RestPullRequestRebaseability can_rebase(project_key, pull_request_id, repository_slug)

Check PR rebase precondition

Checks preconditions to determine whether the pull request can be rebased.

Some of the preconditions are:

- The pull request is between Git repositories
- The pull request is currently open
- The pull request's {@link PullRequest#getFromRef "from" ref} is a <i>branch</i>
   - In other words, the qualified ID for the "from" ref must start with <code>refs/heads/</code>
   - Tags, and other non-standard refs, cannot be rebased
- The current user has an e-mail address
   - Pull requests cannot be rebased anonymously
   - `git rebase` records the current user as the committer for the rebased commits, which        requires a name and e-mail address
- The current user has <i>write</i> access to the {@link PullRequest#getFromRef "from" ref}'s repository
   - Note that in order to <i>view</i> a pull request a user is only required to have <i>read</i>      access to the {@link PullRequest#getToRef toRef}'s repository, so just because a user can <i>see</i>      a pull request does not mean they can request a rebase


This list is not exhaustive, and the exact set of preconditions applied can be extended by third-party add-ons.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_rebaseability import RestPullRequestRebaseability
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Check PR rebase precondition
        api_response = api_instance.can_rebase(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->can_rebase:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->can_rebase: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestPullRequestRebaseability**](RestPullRequestRebaseability.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The rebaseability status of the pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **cancel_auto_merge**
> cancel_auto_merge(project_key, pull_request_id, repository_slug)

Cancel auto-merge for pull request

Cancels a request to auto-merge the pull request, if the pull request was not merged yet.

The authenticated user must have <strong>REPO_WRITE</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Cancel auto-merge for pull request
        api_instance.cancel_auto_merge(project_key, pull_request_id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->cancel_auto_merge: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The auto-merge request was cancelled. |  -  |
**401** | The currently authenticated user has insufficient permissions to modify the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | The specified pull request is not open. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create**
> RestPullRequest create(project_key, repository_slug, rest_pull_request=rest_pull_request)

Create pull request

Create a new pull request from a source branch or tag to a target branch. The source and target may be in the same repository, or different ones. (Note that different repositories must belong to the same <code>Repository#getHierarchyId()</code> hierarchy.) 

The <code>fromRef</code> may be a branch or a tag. The <code>toRef</code> is required to be a branch. Tags are not allowed as targets because tags are intended to be immutable and should not be changed after they are created. 

The authenticated user must have <strong>REPO_READ</strong> permission for the <code>fromRef</code> and <code>toRef</code> repositories to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request import RestPullRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_pull_request = .RestPullRequest() # RestPullRequest | The pull request data (optional)

    try:
        # Create pull request
        api_response = api_instance.create(project_key, repository_slug, rest_pull_request=rest_pull_request)
        print("The response of PullRequestsApi->create:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_pull_request** | [**RestPullRequest**](RestPullRequest.md)| The pull request data | [optional] 

### Return type

[**RestPullRequest**](RestPullRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created pull request. |  -  |
**400** | The pull request entity supplied in the request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to create a pull request between the two specified repositories. |  -  |
**404** | One of the specified repositories or branches does not exist. |  -  |
**409** | One of the following error cases occurred (check the error message for more details):   - There was a problem resolving one or more reviewers. - The specified branches were the same. - The &lt;em&gt;to&lt;/em&gt; branch is already up-to-date with all the commits on the     &lt;em&gt;from&lt;/em&gt; branch. - A pull request between the two branches already exists. - The &lt;em&gt;to&lt;/em&gt; repository is archived.  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create1**
> RestReviewerGroup create1(project_key, rest_reviewer_group=rest_reviewer_group)

Create reviewer group

Create a reviewer group.

The authenticated user must have <b>PROJECT_ADMIN</b> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_reviewer_group import RestReviewerGroup
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_reviewer_group = .RestReviewerGroup() # RestReviewerGroup | The reviewer group to be create (optional)

    try:
        # Create reviewer group
        api_response = api_instance.create1(project_key, rest_reviewer_group=rest_reviewer_group)
        print("The response of PullRequestsApi->create1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_reviewer_group** | [**RestReviewerGroup**](RestReviewerGroup.md)| The reviewer group to be create | [optional] 

### Return type

[**RestReviewerGroup**](RestReviewerGroup.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created reviewer group. |  -  |
**400** | The request is missing a reviewer group name. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The project scope supplied does not exist. |  -  |
**409** | The new created name already exists. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create2**
> RestReviewerGroup create2(project_key, repository_slug, rest_reviewer_group=rest_reviewer_group)

Create reviewer group

Create a reviewer group.

The authenticated user must have <b>REPO_ADMIN</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_reviewer_group import RestReviewerGroup
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_reviewer_group = .RestReviewerGroup() # RestReviewerGroup | The request containing the details of the reviewer group. (optional)

    try:
        # Create reviewer group
        api_response = api_instance.create2(project_key, repository_slug, rest_reviewer_group=rest_reviewer_group)
        print("The response of PullRequestsApi->create2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_reviewer_group** | [**RestReviewerGroup**](RestReviewerGroup.md)| The request containing the details of the reviewer group. | [optional] 

### Return type

[**RestReviewerGroup**](RestReviewerGroup.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created reviewer group. |  -  |
**400** | The request is missing a reviewer group name. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository scope supplied does not exist. |  -  |
**409** | The new created name already exists. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_comment1**
> RestComment create_comment1(project_key, pull_request_id, repository_slug, rest_comment=rest_comment)

Add new blocker comment

Add a new blocker comment. 

Comments can be added in a few places by setting different attributes: 

General pull request blocker comment: 
```

{ 
     "text": "A task on a pull request." 
}
```

Blocker reply to a comment: 

```

{
     "text": "This reply is a task.", 
     "parent": { 
         "id": 1 
     } 
} 
```

General blocker file comment:

```

{
     "text": "A blocker comment on a file.", 
     "anchor": { 
         "diffType": "RANGE", 
         "fromHash": "6df3858eeb9a53a911cd17e66a9174d44ffb02cd", 
         "path": "path/to/file", 
         "srcPath": "path/to/file", 
         "toHash": "04c7c5c931b9418ca7b66f51fe934d0bd9b2ba4b" 
     } 
 } 
```

Blocker file line comment: 

```

{ 
     "text": "A task on a particular line within a file.", 
     "anchor": { 
         "diffType": "COMMIT", 
         "line": 1, 
         "lineType": "CONTEXT", 
         "fileType": "FROM", 
         "fromHash": "6df3858eeb9a53a911cd17e66a9174d44ffb02cd", 
         "path": "path/to/file", 
         "srcPath": "path/to/file", 
         "toHash": "04c7c5c931b9418ca7b66f51fe934d0bd9b2ba4b" 
     } 
 } 
```

For file and line comments, 'path' refers to the path of the file to which the comment should be applied and 'srcPath' refers to the path the that file used to have (only required for copies and moves). Also, fromHash and toHash refer to the sinceId / untilId (respectively) used to produce the diff on which the comment was added. Finally diffType refers to the type of diff the comment was added on. For backwards compatibility purposes if no diffType is provided and no fromHash/toHash pair is provided the diffType will be resolved to 'EFFECTIVE'. In any other cases the diffType is REQUIRED. 

For line comments, 'line' refers to the line in the diff that the comment should apply to. 'lineType' refers to the type of diff hunk, which can be: 

- 'ADDED' - for an added line;
- 'REMOVED' - for a removed line; or
- 'CONTEXT' - for a line that was unmodified but is in the vicinity of the diff.
 

'fileType' refers to the file of the diff to which the anchor should be attached - which is of relevance when displaying the diff in a side-by-side way. Currently the supported values are: 

- 'FROM' - the source file of the diff
 - 'TO' - the destination file of the diff


If the current user is not a participant the user is added as a watcher of the pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_comment = .RestComment() # RestComment | The comment to add. (optional)

    try:
        # Add new blocker comment
        api_response = api_instance.create_comment1(project_key, pull_request_id, repository_slug, rest_comment=rest_comment)
        print("The response of PullRequestsApi->create_comment1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create_comment1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_comment** | [**RestComment**](RestComment.md)| The comment to add. | [optional] 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created comment. |  -  |
**400** | The comment was not created due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request, create a comment or watch the pull request. |  -  |
**404** | Unable to find the supplied project, repository, pull request or parent comment. |  -  |
**409** | The new created name already exists or adding, deleting, or editing comments isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_comment2**
> RestComment create_comment2(project_key, pull_request_id, repository_slug, rest_comment=rest_comment)

Add pull request comment

Add a new comment. 

Comments can be added in a few places by setting different attributes: </p>General pull request comment: 
 <pre> { 
   "text": "An insightful general comment on a pull request." 
 } 
 </pre> Reply to a comment:  <pre> { 
   "text": "A measured reply.", 
   "parent": { 
      "id": 1 
    } 
 } 
 </pre> General file comment:  <pre> { 
   "text": "An insightful general comment on a file.", 
   "anchor": { 
      "diffType": "RANGE", 
      "fromHash": "6df3858eeb9a53a911cd17e66a9174d44ffb02cd", 
      "path": "path/to/file", 
      "srcPath": "path/to/file", 
      "toHash": "04c7c5c931b9418ca7b66f51fe934d0bd9b2ba4b" 
   } 
 } 
 </pre> File line comment:  <pre> { 
   "text": "A pithy comment on a particular line within a file.", 
   "anchor": { 
      "diffType": "COMMIT", 
      "line": 1, 
      "lineType": "CONTEXT", 
      "fileType": "FROM", 
      "fromHash": "6df3858eeb9a53a911cd17e66a9174d44ffb02cd", 
      "path": "path/to/file", 
      "srcPath": "path/to/file", 
      "toHash": "04c7c5c931b9418ca7b66f51fe934d0bd9b2ba4b" 
    } 
 } 
 </pre> 

 Add a new task. 

Tasks are just comments with the attribute 'severity' set to 'BLOCKER': 

General pull request task:  <pre> { 
   "text": "A task on a pull request.", 
   "severity": "BLOCKER" 
 } 
 </pre> 

 Add a pending comment.  

Pending comments are just comments with the attribute 'state' set to 'PENDING': 

Pending comment: <pre> { 
   "text": "This is a pending comment", 
   "state": "PENDING" 
 } 
 </pre> 

For file and line comments, 'path' refers to the path of the file to which the comment should be applied and 'srcPath' refers to the path the that file used to have (only required for copies and moves).

fromHash and toHash refer to the sinceId / untilId (respectively) used to produce the diff on which the comment was added.

For diffType 'COMMIT' or 'RANGE', you must specify both the fromHash and toHash. Note that this behaviour differs from `/commits/comments`

Finally diffType refers to the type of diff the comment was added on. For backwards compatibility purposes if no diffType is provided and no fromHash/toHash pair is provided the diffType will be resolved to 'EFFECTIVE'. In any other cases the diffType is REQUIRED.

For line comments, 'line' refers to the line in the diff that the comment should apply to. 'lineType' refers to the type of diff hunk, which can be: 

- 'ADDED' - for an added line;
- 'REMOVED' - for a removed line; or
- 'CONTEXT' - for a line that was unmodified but is in the vicinity of the diff.
</ul>'fileType' refers to the file of the diff to which the anchor should be attached - which is of relevance when displaying the diff in a side-by-side way. Currently the supported values are: 

- 'FROM' - the source file of the diff
- 'TO' - the destination file of the diff
</ul>If the current user is not a participant the user is added as a watcher of the pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_comment = .RestComment() # RestComment | The comment to add (optional)

    try:
        # Add pull request comment
        api_response = api_instance.create_comment2(project_key, pull_request_id, repository_slug, rest_comment=rest_comment)
        print("The response of PullRequestsApi->create_comment2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create_comment2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_comment** | [**RestComment**](RestComment.md)| The comment to add | [optional] 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created comment. |  -  |
**400** | The comment was not created due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request, create a comment or watch the pull request. |  -  |
**404** | Unable to find the supplied project, repository, pull request or parent comment. |  -  |
**409** | Adding, deleting, or editing comments isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_pull_request_condition**
> RestPullRequestCondition create_pull_request_condition(project_key, rest_default_reviewers_request=rest_default_reviewers_request)

Create default reviewer condition

Create a default reviewer pull request condition for the given project.

### Example


```python
import bitbucketclient
from .models.rest_default_reviewers_request import RestDefaultReviewersRequest
from .models.rest_pull_request_condition import RestPullRequestCondition
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_default_reviewers_request = .RestDefaultReviewersRequest() # RestDefaultReviewersRequest | The details needed to create a default reviewer pull request condition. (optional)

    try:
        # Create default reviewer condition
        api_response = api_instance.create_pull_request_condition(project_key, rest_default_reviewers_request=rest_default_reviewers_request)
        print("The response of PullRequestsApi->create_pull_request_condition:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create_pull_request_condition: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_default_reviewers_request** | [**RestDefaultReviewersRequest**](RestDefaultReviewersRequest.md)| The details needed to create a default reviewer pull request condition. | [optional] 

### Return type

[**RestPullRequestCondition**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default reviewer pull request condition that was created. |  -  |
**400** | The request was malformed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_pull_request_condition1**
> RestPullRequestCondition create_pull_request_condition1(project_key, repository_slug, rest_default_reviewers_request=rest_default_reviewers_request)

Create default reviewer condition

Create a default reviewer pull request condition for the given repository.

### Example


```python
import bitbucketclient
from .models.rest_default_reviewers_request import RestDefaultReviewersRequest
from .models.rest_pull_request_condition import RestPullRequestCondition
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_default_reviewers_request = .RestDefaultReviewersRequest() # RestDefaultReviewersRequest | The details needed to create a default reviewer pull request condition. (optional)

    try:
        # Create default reviewer condition
        api_response = api_instance.create_pull_request_condition1(project_key, repository_slug, rest_default_reviewers_request=rest_default_reviewers_request)
        print("The response of PullRequestsApi->create_pull_request_condition1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->create_pull_request_condition1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_default_reviewers_request** | [**RestDefaultReviewersRequest**](RestDefaultReviewersRequest.md)| The details needed to create a default reviewer pull request condition. | [optional] 

### Return type

[**RestPullRequestCondition**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default reviewer pull request condition that was created. |  -  |
**400** | The request was malformed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **decline**
> RestPullRequest decline(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_decline_request=rest_pull_request_decline_request)

Decline pull request

Decline a pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request import RestPullRequest
from .models.rest_pull_request_decline_request import RestPullRequestDeclineRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pullrequest ID provided by the path
    repository_slug = 'repository_slug_example' # str | The repository slug.
    version = 'version_example' # str | The current version of the pull request. If the server's version isn't the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the 'version' attribute in the returned JSON structure. (optional)
    rest_pull_request_decline_request = .RestPullRequestDeclineRequest() # RestPullRequestDeclineRequest | Optional body (optional)

    try:
        # Decline pull request
        api_response = api_instance.decline(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_decline_request=rest_pull_request_decline_request)
        print("The response of PullRequestsApi->decline:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->decline: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pullrequest ID provided by the path | 
 **repository_slug** | **str**| The repository slug. | 
 **version** | **str**| The current version of the pull request. If the server&#39;s version isn&#39;t the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the &#39;version&#39; attribute in the returned JSON structure. | [optional] 
 **rest_pull_request_decline_request** | [**RestPullRequestDeclineRequest**](RestPullRequestDeclineRequest.md)| Optional body | [optional] 

### Return type

[**RestPullRequest**](RestPullRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The pull request was declined. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | The pull request is not OPEN or has been updated since the version specified by the request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete3**
> delete3(project_key, pull_request_id, repository_slug, rest_pull_request_delete_request=rest_pull_request_delete_request)

Delete pull request

Deletes a pull request. 

To call this resource, users must be authenticated and have permission to view the pull request. Additionally, they must: 

- be the pull request author, if the system is configured to allow authors to delete their own   pull requests (this is the default) OR 
- have repository administrator permission for the repository the pull request is targeting


A body containing the version of the pull request must be provided with this request. 

`{ "version": 1 }`

### Example


```python
import bitbucketclient
from .models.rest_pull_request_delete_request import RestPullRequestDeleteRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_pull_request_delete_request = .RestPullRequestDeleteRequest() # RestPullRequestDeleteRequest | A body containing the version of the pull request (optional)

    try:
        # Delete pull request
        api_instance.delete3(project_key, pull_request_id, repository_slug, rest_pull_request_delete_request=rest_pull_request_delete_request)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete3: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_pull_request_delete_request** | [**RestPullRequestDeleteRequest**](RestPullRequestDeleteRequest.md)| A body containing the version of the pull request | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The pull request was deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | Deleting pull requests isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete6**
> delete6(project_key, id)

Delete reviewer group

Deletes a reviewer group.

The authenticated user must have <b>PROJECT_ADMIN</b> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be deleted

    try:
        # Delete reviewer group
        api_instance.delete6(project_key, id)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete6: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be deleted | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The operation was successful. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the reviewer group in this project. |  -  |
**404** | Unable to find the supplied reviewer group ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete7**
> delete7(project_key, id, repository_slug)

Delete reviewer group

Deletes a reviewer group.

The authenticated user must have <b>REPO_ADMIN</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be deleted
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete reviewer group
        api_instance.delete7(project_key, id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete7: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be deleted | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The operation was successful |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the reviewer group in this repository. |  -  |
**404** | Unable to find the supplied reviewer group ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_comment1**
> delete_comment1(project_key, comment_id, pull_request_id, repository_slug, version=version)

Delete pull request comment

Delete a pull request comment. Anyone can delete their own comment. Only users with <strong>REPO_ADMIN</strong> and above may delete comments created by other users.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    version = 'version_example' # str | The expected version of the comment. This must match the server's version of the comment or the delete will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the delete. Look for the 'version' attribute in the returned JSON structure. (optional)

    try:
        # Delete pull request comment
        api_instance.delete_comment1(project_key, comment_id, pull_request_id, repository_slug, version=version)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete_comment1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **version** | **str**| The expected version of the comment. This must match the server&#39;s version of the comment or the delete will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the delete. Look for the &#39;version&#39; attribute in the returned JSON structure. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The operation was successful. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the comment. |  -  |
**404** | Unable to find the supplied project, repository or pull request. |  -  |
**409** | The comment has replies, the version supplied does not match the current version or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_comment2**
> delete_comment2(project_key, comment_id, pull_request_id, repository_slug, version=version)

Delete a pull request comment

Delete a pull request comment. Anyone can delete their own comment. Only users with <strong>REPO_ADMIN</strong> and above may delete comments created by other users. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    version = 'version_example' # str | The expected version of the comment. This must match the server's version of the comment or the delete will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the delete. Look for the 'version' attribute in the returned JSON structure. (optional)

    try:
        # Delete a pull request comment
        api_instance.delete_comment2(project_key, comment_id, pull_request_id, repository_slug, version=version)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete_comment2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **version** | **str**| The expected version of the comment. This must match the server&#39;s version of the comment or the delete will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the delete. Look for the &#39;version&#39; attribute in the returned JSON structure. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The operation was successful. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the comment. |  -  |
**404** | Unable to find the supplied project, repository or pull request. |  -  |
**409** | The comment has replies, the version supplied does not match the current version or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_pull_request_condition**
> delete_pull_request_condition(project_key, id)

Delete default reviewer condition

Delete the default reviewer pull request condition associated with the given ID.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the pull request condition.

    try:
        # Delete default reviewer condition
        api_instance.delete_pull_request_condition(project_key, id)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete_pull_request_condition: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the pull request condition. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the pull request condition was deleted. |  -  |
**404** | An empty response indicating a pull request condition with the given ID could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_pull_request_condition1**
> delete_pull_request_condition1(project_key, id, repository_slug)

Delete default reviewer condition

Delete the default reviewer pull request condition associated with the given ID.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 56 # int | 
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete default reviewer condition
        api_instance.delete_pull_request_condition1(project_key, id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->delete_pull_request_condition1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **int**|  | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the pull request condition was deleted |  -  |
**404** | An empty response indicating a pull request condition with the given ID could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **discard_review**
> discard_review(project_key, pull_request_id, repository_slug)

Discard pull request review

Discard a pull request review for the authenticated user. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Discard pull request review
        api_instance.discard_review(project_key, pull_request_id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->discard_review: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The pull request review has been discarded. |  -  |
**401** | The currently authenticated user has insufficient permissions to discard the the pull request review |  -  |
**404** | The specified pull request or repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **finish_review**
> finish_review(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_finish_review_request=rest_pull_request_finish_review_request)

Complete pull request review

Complete a review on a pull request.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_finish_review_request import RestPullRequestFinishReviewRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    version = 'version_example' # str | The current version of the pull request. If the server's version isn't the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the 'version' attribute in the returned JSON structure. Note: This parameter is deprecated. Use last reviewed commit in request body instead (optional)
    rest_pull_request_finish_review_request = .RestPullRequestFinishReviewRequest() # RestPullRequestFinishReviewRequest | The REST request which contains comment text, last reviewed commit and participant status. If last reviewed commit is provided, it will be used to update the participant status. The operation will fail if the latest commit of the pull request does not match the provided last reviewed commit. If last reviewed commit is not provided, the latest commit of the pull request will be used for the update by default. (optional)

    try:
        # Complete pull request review
        api_instance.finish_review(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_finish_review_request=rest_pull_request_finish_review_request)
    except Exception as e:
        print("Exception when calling PullRequestsApi->finish_review: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **version** | **str**| The current version of the pull request. If the server&#39;s version isn&#39;t the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the &#39;version&#39; attribute in the returned JSON structure. Note: This parameter is deprecated. Use last reviewed commit in request body instead | [optional] 
 **rest_pull_request_finish_review_request** | [**RestPullRequestFinishReviewRequest**](RestPullRequestFinishReviewRequest.md)| The REST request which contains comment text, last reviewed commit and participant status. If last reviewed commit is provided, it will be used to update the participant status. The operation will fail if the latest commit of the pull request does not match the provided last reviewed commit. If last reviewed commit is not provided, the latest commit of the pull request will be used for the update by default. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Getting back the number of published comments and completing the review on a pull request. |  -  |
**400** | The request is invalid when there is no request body provided, or the participant status in the request is invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request, update a comment or watch the pull request. |  -  |
**404** | There is no pull request review for the user to finish. |  -  |
**409** | The pull request has been updated since the last reviewed commit specified by the request, or reviews cannot be made on pull requests in archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get3**
> RestPullRequest get3(project_key, pull_request_id, repository_slug)

Get pull request

Retrieve a pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request import RestPullRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get pull request
        api_response = api_instance.get3(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->get3:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get3: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestPullRequest**](RestPullRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The specified pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_activities**
> GetActivities200Response get_activities(project_key, pull_request_id, repository_slug, from_type=from_type, from_id=from_id, start=start, limit=limit)

Get pull request activity

Retrieve a page of activity associated with a pull request. 

Activity items include comments, approvals, rescopes (i.e. adding and removing of commits), merges and more. 

Different types of activity items may be introduced in newer versions of Stash or by user installed plugins, so clients should be flexible enough to handle unexpected entity shapes in the returned page. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.get_activities200_response import GetActivities200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    from_type = 'from_type_example' # str | (required if <strong>fromId</strong> is present) the type of the activity item specified by <strong>fromId</strong> (either <strong>COMMENT</strong> or <strong>ACTIVITY</strong>) (optional)
    from_id = 'from_id_example' # str | (optional) the ID of the activity item to use as the first item in the returned page (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get pull request activity
        api_response = api_instance.get_activities(project_key, pull_request_id, repository_slug, from_type=from_type, from_id=from_id, start=start, limit=limit)
        print("The response of PullRequestsApi->get_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_activities: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **from_type** | **str**| (required if &lt;strong&gt;fromId&lt;/strong&gt; is present) the type of the activity item specified by &lt;strong&gt;fromId&lt;/strong&gt; (either &lt;strong&gt;COMMENT&lt;/strong&gt; or &lt;strong&gt;ACTIVITY&lt;/strong&gt;) | [optional] 
 **from_id** | **str**| (optional) the ID of the activity item to use as the first item in the returned page | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetActivities200Response**](GetActivities200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of activity relating to the specified pull request. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_auto_merge_request**
> RestAutoMergeRequest get_auto_merge_request(project_key, pull_request_id, repository_slug)

Get auto-merge request for pull request

Returns an auto-merge request for the pull request, if requested.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_merge_request import RestAutoMergeRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get auto-merge request for pull request
        api_response = api_instance.get_auto_merge_request(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->get_auto_merge_request:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_auto_merge_request: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestAutoMergeRequest**](RestAutoMergeRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The auto-merge request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_comment1**
> RestComment get_comment1(project_key, comment_id, pull_request_id, repository_slug)

Get pull request comment

Retrieves a pull request comment.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get pull request comment
        api_response = api_instance.get_comment1(project_key, comment_id, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->get_comment1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_comment1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The requested comment. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the comment. |  -  |
**404** | Unable to find the supplied project, repository, pull request or comment. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_comment2**
> RestComment get_comment2(project_key, comment_id, pull_request_id, repository_slug)

Get a pull request comment

Retrieves a pull request comment. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get a pull request comment
        api_response = api_instance.get_comment2(project_key, comment_id, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->get_comment2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_comment2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The requested comment. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the comment. |  -  |
**404** | Unable to find the supplied project, repository, pull request or comment. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_comments1**
> GetComments200Response get_comments1(project_key, pull_request_id, repository_slug, count=count, state=state, states=states, start=start, limit=limit)

Search pull request comments

Gets comments matching the given set of field values for the specified pull request. (Note this does <b>not</b> perform any kind of searching for comments by their text). 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.get_comments200_response import GetComments200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    count = 'count_example' # str | If true only the count of the comments by state will be returned (and not the body of the comments). (optional)
    state = ['state_example'] # List[str] |  (optional)
    states = 'states_example' # str | (optional). If supplied, only comments with a state in the given list will be returned. The state can be OPEN or RESOLVED. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Search pull request comments
        api_response = api_instance.get_comments1(project_key, pull_request_id, repository_slug, count=count, state=state, states=states, start=start, limit=limit)
        print("The response of PullRequestsApi->get_comments1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_comments1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **count** | **str**| If true only the count of the comments by state will be returned (and not the body of the comments). | [optional] 
 **state** | [**List[str]**](str.md)|  | [optional] 
 **states** | **str**| (optional). If supplied, only comments with a state in the given list will be returned. The state can be OPEN or RESOLVED. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetComments200Response**](GetComments200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of Comments from the supplied pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository or pull request. |  -  |
**404** | The repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_comments2**
> GetComments200Response get_comments2(path, project_key, pull_request_id, repository_slug, from_hash=from_hash, anchor_state=anchor_state, diff_type=diff_type, to_hash=to_hash, state=state, diff_types=diff_types, states=states, start=start, limit=limit)

Get pull request comments for path

Gets comments for the specified pull request and path. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.get_comments200_response import GetComments200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    path = 'path_example' # str | The path to stream comments for a given path
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    from_hash = 'from_hash_example' # str | The from commit hash to stream comments for a RANGE or COMMIT arbitrary change scope (optional)
    anchor_state = 'anchor_state_example' # str | ACTIVE to stream the active comments; ORPHANED to stream the orphaned comments; ALL to stream both the active and the orphaned comments; (optional)
    diff_type = ['diff_type_example'] # List[str] |  (optional)
    to_hash = 'to_hash_example' # str | The to commit hash to stream comments for a RANGE or COMMIT arbitrary change scope (optional)
    state = ['state_example'] # List[str] |  (optional)
    diff_types = 'diff_types_example' # str | EFFECTIVE to stream the comments related to the effective diff of the pull request; RANGE to stream comments related to a commit range between two arbitrary commits (requires 'fromHash' and 'toHash'); COMMIT to stream comments related to a commit between two arbitrary commits (requires 'fromHash' and 'toHash') (optional)
    states = 'states_example' # str | (optional). If supplied, only comments with a state in the given list will be returned. The state can be OPEN or RESOLVED. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get pull request comments for path
        api_response = api_instance.get_comments2(path, project_key, pull_request_id, repository_slug, from_hash=from_hash, anchor_state=anchor_state, diff_type=diff_type, to_hash=to_hash, state=state, diff_types=diff_types, states=states, start=start, limit=limit)
        print("The response of PullRequestsApi->get_comments2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_comments2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The path to stream comments for a given path | 
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **from_hash** | **str**| The from commit hash to stream comments for a RANGE or COMMIT arbitrary change scope | [optional] 
 **anchor_state** | **str**| ACTIVE to stream the active comments; ORPHANED to stream the orphaned comments; ALL to stream both the active and the orphaned comments; | [optional] 
 **diff_type** | [**List[str]**](str.md)|  | [optional] 
 **to_hash** | **str**| The to commit hash to stream comments for a RANGE or COMMIT arbitrary change scope | [optional] 
 **state** | [**List[str]**](str.md)|  | [optional] 
 **diff_types** | **str**| EFFECTIVE to stream the comments related to the effective diff of the pull request; RANGE to stream comments related to a commit range between two arbitrary commits (requires &#39;fromHash&#39; and &#39;toHash&#39;); COMMIT to stream comments related to a commit between two arbitrary commits (requires &#39;fromHash&#39; and &#39;toHash&#39;) | [optional] 
 **states** | **str**| (optional). If supplied, only comments with a state in the given list will be returned. The state can be OPEN or RESOLVED. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetComments200Response**](GetComments200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of Comments from the supplied pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository or pull request. |  -  |
**404** | The repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_commit_message_suggestion**
> RestCommitMessageSuggestion get_commit_message_suggestion(project_key, pull_request_id, repository_slug)

Get commit message suggestion

Retrieve a suggested commit message for the given Pull Request.

### Example


```python
import bitbucketclient
from .models.rest_commit_message_suggestion import RestCommitMessageSuggestion
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request to generate the suggestion for
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get commit message suggestion
        api_response = api_instance.get_commit_message_suggestion(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->get_commit_message_suggestion:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_commit_message_suggestion: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request to generate the suggestion for | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestCommitMessageSuggestion**](RestCommitMessageSuggestion.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The suggested commit message |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_commits1**
> GetCommits200Response get_commits1(project_key, pull_request_id, repository_slug, avatar_scheme=avatar_scheme, with_counts=with_counts, avatar_size=avatar_size, start=start, limit=limit)

Get pull request commits

Retrieve commits for the specified pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.get_commits200_response import GetCommits200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | ID of the pullrequest, part of the path
    repository_slug = 'repository_slug_example' # str | The repository slug.
    avatar_scheme = 'avatar_scheme_example' # str | The desired scheme for the avatar URL. If the parameter is not present URLs will use the same scheme as this request (optional)
    with_counts = 'with_counts_example' # str | If set to true, the service will add \"authorCount\" and \"totalCount\" at the end of the page. \"authorCount\" is the number of different authors and \"totalCount\" is the total number of commits. (optional)
    avatar_size = 'avatar_size_example' # str | If present the service adds avatar URLs for commit authors. Should be an integer specifying the desired size in pixels. If the parameter is not present, avatar URLs will not be setCOMMIT to stream comments related to a commit between two arbitrary commits (requires 'fromHash' and 'toHash') (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get pull request commits
        api_response = api_instance.get_commits1(project_key, pull_request_id, repository_slug, avatar_scheme=avatar_scheme, with_counts=with_counts, avatar_size=avatar_size, start=start, limit=limit)
        print("The response of PullRequestsApi->get_commits1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_commits1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| ID of the pullrequest, part of the path | 
 **repository_slug** | **str**| The repository slug. | 
 **avatar_scheme** | **str**| The desired scheme for the avatar URL. If the parameter is not present URLs will use the same scheme as this request | [optional] 
 **with_counts** | **str**| If set to true, the service will add \&quot;authorCount\&quot; and \&quot;totalCount\&quot; at the end of the page. \&quot;authorCount\&quot; is the number of different authors and \&quot;totalCount\&quot; is the total number of commits. | [optional] 
 **avatar_size** | **str**| If present the service adds avatar URLs for commit authors. Should be an integer specifying the desired size in pixels. If the parameter is not present, avatar URLs will not be setCOMMIT to stream comments related to a commit between two arbitrary commits (requires &#39;fromHash&#39; and &#39;toHash&#39;) | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetCommits200Response**](GetCommits200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of commits from the supplied pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository or pull request. |  -  |
**404** | The repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_diff_stats_summary2**
> object get_diff_stats_summary2(path, project_key, pull_request_id, repository_slug, since_id=since_id, src_path=src_path, until_id=until_id, whitespace=whitespace)

Get diff stats summary for pull request

Retrieve the diff stats summary for the given Pull Request. 

The stats summary include the total number of modified files, added lines, and deleted lines. 

Note: The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    path = 'path_example' # str | Optional path to the file which should be diffed
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    since_id = 'since_id_example' # str | The since commit hash to stream a diff between two arbitrary hashes (optional)
    src_path = 'src_path_example' # str | The previous path to the file, if the file has been copied, moved or renamed (optional)
    until_id = 'until_id_example' # str | The until commit hash to stream a diff between two arbitrary hashes (optional)
    whitespace = 'whitespace_example' # str | Optional whitespace flag which can be set to <code>ignore-all</code> (optional)

    try:
        # Get diff stats summary for pull request
        api_response = api_instance.get_diff_stats_summary2(path, project_key, pull_request_id, repository_slug, since_id=since_id, src_path=src_path, until_id=until_id, whitespace=whitespace)
        print("The response of PullRequestsApi->get_diff_stats_summary2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_diff_stats_summary2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| Optional path to the file which should be diffed | 
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **since_id** | **str**| The since commit hash to stream a diff between two arbitrary hashes | [optional] 
 **src_path** | **str**| The previous path to the file, if the file has been copied, moved or renamed | [optional] 
 **until_id** | **str**| The until commit hash to stream a diff between two arbitrary hashes | [optional] 
 **whitespace** | **str**| Optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The diff stats summary |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_merge_base1**
> RestCommit get_merge_base1(project_key, pull_request_id, repository_slug)

Get the common ancestor between the latest commits of the source and target branches of the pull request

Returns the best common ancestor between the latest commits of the source and target branches of the pull request.

If more than one best common ancestor exists, only one will be returned. It is unspecified which will be returned.

### Example


```python
import bitbucketclient
from .models.rest_commit import RestCommit
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get the common ancestor between the latest commits of the source and target branches of the pull request
        api_response = api_instance.get_merge_base1(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->get_merge_base1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_merge_base1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestCommit**](RestCommit.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The common ancestor of the latest commits in the source and target branches of this pull request |  -  |
**204** | No common parent between exist |  -  |
**404** | The project, repository, or pull request does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_merge_config**
> RestPullRequestMergeConfig get_merge_config(scm_id)

Get merge strategies

Retrieve the merge strategies available for this instance. 

The user must be authenticated to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_merge_config import RestPullRequestMergeConfig
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    scm_id = 'scm_id_example' # str | the id of the scm to get strategies for

    try:
        # Get merge strategies
        api_response = api_instance.get_merge_config(scm_id)
        print("The response of PullRequestsApi->get_merge_config:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_merge_config: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **scm_id** | **str**| the id of the scm to get strategies for | 

### Return type

[**RestPullRequestMergeConfig**](RestPullRequestMergeConfig.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The merge configuration of this instance. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the request repository. |  -  |
**404** | The request repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_page**
> GetPullRequests1200Response get_page(project_key, repository_slug, with_attributes=with_attributes, at=at, with_properties=with_properties, draft=draft, filter_text=filter_text, state=state, order=order, direction=direction, start=start, limit=limit)

Get pull requests for repository

Retrieve a page of pull requests to or from the specified repository. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.  Optionally clients can specify PR participant filters. Each filter has a mandatory username.N parameter, and the optional role.N and approved.N parameters. 

- username.N - the "root" of a single participant filter, where "N" is a natural number   starting from 1. This allows clients to specify multiple participant filters, by providing consecutive   filters as username.1, username.2 etc. Note that the filters numbering has to start   with 1 and be continuous for all filters to be processed. The total allowed number of participant   filters is 10 and all filters exceeding that limit will be dropped.
- role.N(optional) the role associated with username.N.   This must be one of AUTHOR, REVIEWER, or PARTICIPANT
- approved.N (optional) the approved status associated with username.N.   That is whether username.N has approved the PR. Either true, or false


### Example


```python
import bitbucketclient
from .models.get_pull_requests1200_response import GetPullRequests1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    with_attributes = 'with_attributes_example' # str | (optional) defaults to true, whether to return additional pull request attributes (optional)
    at = 'at_example' # str | (optional) a <i>fully-qualified</i> branch ID to find pull requests to or from, such as refs/heads/master (optional)
    with_properties = 'with_properties_example' # str | (optional) defaults to true, whether to return additional pull request properties (optional)
    draft = 'draft_example' # str | (optional) If specified, only pull requests matching the supplied draft status will be returned. (optional)
    filter_text = 'filter_text_example' # str | (optional) If specified, only pull requests where the title or description contains the supplied string will be returned. (optional)
    state = 'state_example' # str | (optional, defaults to <strong>OPEN</strong>). Supply <strong>ALL</strong> to return pull request in any state. If a state is supplied only pull requests in the specified state will be returned. Either <strong>OPEN</strong>, <strong>DECLINED</strong> or <strong>MERGED</strong>. (optional)
    order = 'order_example' # str | (optional, defaults to <strong>NEWEST</strong>) the order to return pull requests in, either <strong>OLDEST</strong> (as in: \"oldest first\") or <strong>NEWEST</strong>. (optional)
    direction = 'direction_example' # str | (optional, defaults to <strong>INCOMING</strong>) the direction relative to the specified repository. Either <strong>INCOMING</strong> or <strong>OUTGOING</strong>. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get pull requests for repository
        api_response = api_instance.get_page(project_key, repository_slug, with_attributes=with_attributes, at=at, with_properties=with_properties, draft=draft, filter_text=filter_text, state=state, order=order, direction=direction, start=start, limit=limit)
        print("The response of PullRequestsApi->get_page:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_page: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **with_attributes** | **str**| (optional) defaults to true, whether to return additional pull request attributes | [optional] 
 **at** | **str**| (optional) a &lt;i&gt;fully-qualified&lt;/i&gt; branch ID to find pull requests to or from, such as refs/heads/master | [optional] 
 **with_properties** | **str**| (optional) defaults to true, whether to return additional pull request properties | [optional] 
 **draft** | **str**| (optional) If specified, only pull requests matching the supplied draft status will be returned. | [optional] 
 **filter_text** | **str**| (optional) If specified, only pull requests where the title or description contains the supplied string will be returned. | [optional] 
 **state** | **str**| (optional, defaults to &lt;strong&gt;OPEN&lt;/strong&gt;). Supply &lt;strong&gt;ALL&lt;/strong&gt; to return pull request in any state. If a state is supplied only pull requests in the specified state will be returned. Either &lt;strong&gt;OPEN&lt;/strong&gt;, &lt;strong&gt;DECLINED&lt;/strong&gt; or &lt;strong&gt;MERGED&lt;/strong&gt;. | [optional] 
 **order** | **str**| (optional, defaults to &lt;strong&gt;NEWEST&lt;/strong&gt;) the order to return pull requests in, either &lt;strong&gt;OLDEST&lt;/strong&gt; (as in: \&quot;oldest first\&quot;) or &lt;strong&gt;NEWEST&lt;/strong&gt;. | [optional] 
 **direction** | **str**| (optional, defaults to &lt;strong&gt;INCOMING&lt;/strong&gt;) the direction relative to the specified repository. Either &lt;strong&gt;INCOMING&lt;/strong&gt; or &lt;strong&gt;OUTGOING&lt;/strong&gt;. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetPullRequests1200Response**](GetPullRequests1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of pull requests that match the search criteria. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_pull_request_conditions**
> List[RestPullRequestCondition] get_pull_request_conditions(project_key)

Get default reviewer conditions

Return a page of default reviewer pull request conditions that have been configured for this project.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_condition import RestPullRequestCondition
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.

    try:
        # Get default reviewer conditions
        api_response = api_instance.get_pull_request_conditions(project_key)
        print("The response of PullRequestsApi->get_pull_request_conditions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_pull_request_conditions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 

### Return type

[**List[RestPullRequestCondition]**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default reviewer pull request conditions associated with the given project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_pull_request_conditions1**
> List[RestPullRequestCondition] get_pull_request_conditions1(project_key, repository_slug)

Get default reviewer conditions

Return a page of default reviewer pull request conditions that have been configured for this repository.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_condition import RestPullRequestCondition
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get default reviewer conditions
        api_response = api_instance.get_pull_request_conditions1(project_key, repository_slug)
        print("The response of PullRequestsApi->get_pull_request_conditions1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_pull_request_conditions1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**List[RestPullRequestCondition]**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default reviewer pull request conditions associated with the given repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_pull_requests**
> GetPullRequests1200Response get_pull_requests(project_key, commit_id, repository_slug, start=start, limit=limit)

Get repository pull requests containing commit

Retrieve a page of pull requests in the current repository that contain the given commit.

The user must be authenticated and have access to the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_pull_requests1200_response import GetPullRequests1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | the commit ID
    repository_slug = 'repository_slug_example' # str | The repository slug
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repository pull requests containing commit
        api_response = api_instance.get_pull_requests(project_key, commit_id, repository_slug, start=start, limit=limit)
        print("The response of PullRequestsApi->get_pull_requests:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_pull_requests: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| the commit ID | 
 **repository_slug** | **str**| The repository slug | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetPullRequests1200Response**](GetPullRequests1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Return a page of pull requests in the current repository containing the given commit. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the request repository. |  -  |
**404** | The request repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_review**
> GetComments200Response get_review(project_key, pull_request_id, repository_slug, start=start, limit=limit)

Get pull request comment thread

Get the <code>CommentThread</code> threads which have <code>Comment</code> comments that have a <code>CommentState#PENDING</code> pending state and are part of the pull request review for the authenticated user.

### Example


```python
import bitbucketclient
from .models.get_comments200_response import GetComments200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get pull request comment thread
        api_response = api_instance.get_review(project_key, pull_request_id, repository_slug, start=start, limit=limit)
        print("The response of PullRequestsApi->get_review:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_review: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetComments200Response**](GetComments200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of Comments from the supplied pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository or pull request. |  -  |
**404** | The repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_reviewer_group**
> RestReviewerGroup get_reviewer_group(project_key, id)

Get reviewer group

Retrieve a reviewer group.

The authenticated user must have <b>PROJECT_READ</b> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_reviewer_group import RestReviewerGroup
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be retrieved

    try:
        # Get reviewer group
        api_response = api_instance.get_reviewer_group(project_key, id)
        print("The response of PullRequestsApi->get_reviewer_group:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_reviewer_group: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be retrieved | 

### Return type

[**RestReviewerGroup**](RestReviewerGroup.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The reviewer group. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The ID supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_reviewer_group1**
> RestReviewerGroup get_reviewer_group1(project_key, id, repository_slug)

Get reviewer group

Retrieve a reviewer group.

The authenticated user must have <b>REPO_READ</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_reviewer_group import RestReviewerGroup
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be retrieved
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get reviewer group
        api_response = api_instance.get_reviewer_group1(project_key, id, repository_slug)
        print("The response of PullRequestsApi->get_reviewer_group1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_reviewer_group1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be retrieved | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestReviewerGroup**](RestReviewerGroup.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The reviewer group. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The ID supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_reviewer_groups**
> GetReviewerGroups1200Response get_reviewer_groups(project_key, start=start, limit=limit)

Get all reviewer groups

Retrieve a page of reviewer groups of a given scope.

The authenticated user must have <b>PROJECT_READ</b> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.get_reviewer_groups1200_response import GetReviewerGroups1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get all reviewer groups
        api_response = api_instance.get_reviewer_groups(project_key, start=start, limit=limit)
        print("The response of PullRequestsApi->get_reviewer_groups:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_reviewer_groups: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetReviewerGroups1200Response**](GetReviewerGroups1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of reviewer group(s) of the provided scope. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The project scope supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_reviewer_groups1**
> GetReviewerGroups1200Response get_reviewer_groups1(project_key, repository_slug, start=start, limit=limit)

Get all reviewer groups

Retrieve a page of reviewer groups of a given scope.

The authenticated user must have <b>REPO_READ</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_reviewer_groups1200_response import GetReviewerGroups1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get all reviewer groups
        api_response = api_instance.get_reviewer_groups1(project_key, repository_slug, start=start, limit=limit)
        print("The response of PullRequestsApi->get_reviewer_groups1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_reviewer_groups1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetReviewerGroups1200Response**](GetReviewerGroups1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A &#x60;page&#x60; of reviewer group(s) of the provided scope and its inherited scope. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository scope supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_reviewers**
> List[RestPullRequestCondition] get_reviewers(project_key, repository_slug, target_repo_id=target_repo_id, source_repo_id=source_repo_id, source_ref_id=source_ref_id, target_ref_id=target_ref_id)

Get required reviewers for PR creation

Return a set of users who are required reviewers for pull requests created from the given source repository and ref to the given target ref in this repository.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_condition import RestPullRequestCondition
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    target_repo_id = 'target_repo_id_example' # str | The ID of the repository in which the target ref exists (optional)
    source_repo_id = 'source_repo_id_example' # str | The ID of the repository in which the source ref exists (optional)
    source_ref_id = 'source_ref_id_example' # str | The ID of the source ref (optional)
    target_ref_id = 'target_ref_id_example' # str | The ID of the target ref (optional)

    try:
        # Get required reviewers for PR creation
        api_response = api_instance.get_reviewers(project_key, repository_slug, target_repo_id=target_repo_id, source_repo_id=source_repo_id, source_ref_id=source_ref_id, target_ref_id=target_ref_id)
        print("The response of PullRequestsApi->get_reviewers:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_reviewers: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **target_repo_id** | **str**| The ID of the repository in which the target ref exists | [optional] 
 **source_repo_id** | **str**| The ID of the repository in which the source ref exists | [optional] 
 **source_ref_id** | **str**| The ID of the source ref | [optional] 
 **target_ref_id** | **str**| The ID of the target ref | [optional] 

### Return type

[**List[RestPullRequestCondition]**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default reviewer pull request conditions associated with the given repository. |  -  |
**400** | The request was malformed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_users**
> List[RestApplicationUser] get_users(project_key, id, repository_slug)

Get reviewer group users

Retrieve a list of the users of a reviewer group.

This does not return all the users of the group, only the users who are licensed and have <b>REPO_READ</b> permission for the specified repository.

The authenticated user must have <b>REPO_READ</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_application_user import RestApplicationUser
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be retrieved
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get reviewer group users
        api_response = api_instance.get_users(project_key, id, repository_slug)
        print("The response of PullRequestsApi->get_users:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->get_users: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be retrieved | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**List[RestApplicationUser]**](RestApplicationUser.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The list of users of a reviewer group. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The ID supplied does not exist.d |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **list_participants**
> ListParticipants200Response list_participants(project_key, pull_request_id, repository_slug, start=start, limit=limit)

Get pull request participants

Retrieves a page of the participants for a given pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.list_participants200_response import ListParticipants200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get pull request participants
        api_response = api_instance.list_participants(project_key, pull_request_id, repository_slug, start=start, limit=limit)
        print("The response of PullRequestsApi->list_participants:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->list_participants: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**ListParticipants200Response**](ListParticipants200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details of the participants in this pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **merge**
> RestPullRequest merge(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_merge_request=rest_pull_request_merge_request)

Merge pull request

Merge the specified pull request immediately or set the pull request to auto-merge when all the merge checks pass by setting <strong>autoMerge</strong> field in the request body.

The authenticated user must have <strong>REPO_WRITE</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request import RestPullRequest
from .models.rest_pull_request_merge_request import RestPullRequestMergeRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    version = 'version_example' # str | The current version of the pull request. If the server's version isn't the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the 'version' attribute in the returned JSON structure. (optional)
    rest_pull_request_merge_request = .RestPullRequestMergeRequest() # RestPullRequestMergeRequest | The body holder (optional)

    try:
        # Merge pull request
        api_response = api_instance.merge(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_merge_request=rest_pull_request_merge_request)
        print("The response of PullRequestsApi->merge:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->merge: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **version** | **str**| The current version of the pull request. If the server&#39;s version isn&#39;t the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the &#39;version&#39; attribute in the returned JSON structure. | [optional] 
 **rest_pull_request_merge_request** | [**RestPullRequestMergeRequest**](RestPullRequestMergeRequest.md)| The body holder | [optional] 

### Return type

[**RestPullRequest**](RestPullRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The merged pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to merge the specified pull request |  -  |
**403** | The auto-merge setting is not enabled for the repository that this pull request targets. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | One of the following error cases occurred (check the error message for more details):   - The pull request has conflicts. - A merge check vetoed the merge. - The specified version is out of date. - The specified pull request is not open. - The &lt;em&gt;to&lt;/em&gt; repository is archived.  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **react1**
> RestUserReaction react1(project_key, comment_id, pull_request_id, emoticon, repository_slug)

React to a PR comment

Add an emoticon reaction to a pull request comment

### Example


```python
import bitbucketclient
from .models.rest_user_reaction import RestUserReaction
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The comment id.
    pull_request_id = 'pull_request_id_example' # str | The pull request id.
    emoticon = 'emoticon_example' # str | The emoticon to add
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # React to a PR comment
        api_response = api_instance.react1(project_key, comment_id, pull_request_id, emoticon, repository_slug)
        print("The response of PullRequestsApi->react1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->react1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The comment id. | 
 **pull_request_id** | **str**| The pull request id. | 
 **emoticon** | **str**| The emoticon to add | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestUserReaction**](RestUserReaction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The added reaction |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **rebase**
> RestPullRequestRebaseResult rebase(project_key, pull_request_id, repository_slug, rest_pull_request_rebase_request=rest_pull_request_rebase_request)

Rebase pull request

Rebases the specified pull request, rewriting the incoming commits to start from the tip commit of the pull request's target branch. <i>This operation alters the pull request's source branch and cannot be undone.</i>

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets <i>and</i> <strong>REPO_WRITE</strong> permission for the pull request's source repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_rebase_request import RestPullRequestRebaseRequest
from .models.rest_pull_request_rebase_result import RestPullRequestRebaseResult
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_pull_request_rebase_request = .RestPullRequestRebaseRequest() # RestPullRequestRebaseRequest | The pull request rebase request. (optional)

    try:
        # Rebase pull request
        api_response = api_instance.rebase(project_key, pull_request_id, repository_slug, rest_pull_request_rebase_request=rest_pull_request_rebase_request)
        print("The response of PullRequestsApi->rebase:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->rebase: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_pull_request_rebase_request** | [**RestPullRequestRebaseRequest**](RestPullRequestRebaseRequest.md)| The pull request rebase request. | [optional] 

### Return type

[**RestPullRequestRebaseResult**](RestPullRequestRebaseResult.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The merged pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request and/or to update its source branch. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | Any of the following error cases occurred (check the error message for more details):  - The rebase encountered conflicts. - The rebase discarded all of the incoming commits and would have left the pull request empty - A &lt;tt&gt;PreRepositoryHook&lt;/tt&gt; vetoed the rebase. - The specified version is out of date. - The specified pull request is not open. - The target repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **reopen**
> RestPullRequest reopen(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_reopen_request=rest_pull_request_reopen_request)

Re-open pull request

Re-open a declined pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request import RestPullRequest
from .models.rest_pull_request_reopen_request import RestPullRequestReopenRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    version = 'version_example' # str | The current version of the pull request. If the server's version isn't the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the 'version' attribute in the returned JSON structure. (optional)
    rest_pull_request_reopen_request = .RestPullRequestReopenRequest() # RestPullRequestReopenRequest | The body holder (optional)

    try:
        # Re-open pull request
        api_response = api_instance.reopen(project_key, pull_request_id, repository_slug, version=version, rest_pull_request_reopen_request=rest_pull_request_reopen_request)
        print("The response of PullRequestsApi->reopen:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->reopen: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **version** | **str**| The current version of the pull request. If the server&#39;s version isn&#39;t the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the &#39;version&#39; attribute in the returned JSON structure. | [optional] 
 **rest_pull_request_reopen_request** | [**RestPullRequestReopenRequest**](RestPullRequestReopenRequest.md)| The body holder | [optional] 

### Return type

[**RestPullRequest**](RestPullRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The merged pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to reopen the specified pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | One of the following error cases occurred (check the error message for more details):   - The pull request is not in a declined state. - The specified version is out of date. - The &lt;em&gt;to&lt;/em&gt; repository is archived.  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **search**
> GetUsersWithoutAnyPermission200Response search(project_key, repository_slug, filter=filter, role=role, direction=direction, start=start, limit=limit)

Search pull request participants

Retrieve a page of participant users for all the pull requests to or from the specified repository. 

Optionally clients can specify following filters.

### Example


```python
import bitbucketclient
from .models.get_users_without_any_permission200_response import GetUsersWithoutAnyPermission200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    filter = 'filter_example' # str | (optional) Return only users, whose username, name or email address <i>contain</i> the filter value (optional)
    role = 'role_example' # str | (optional) The role associated with the pull request participant. This must be one of AUTHOR, REVIEWER, or PARTICIPANT (optional)
    direction = 'direction_example' # str | (optional), Defaults to <strong>INCOMING</strong>) the direction relative to the specified repository. Either <strong>INCOMING</strong> or <strong>OUTGOING</strong>. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Search pull request participants
        api_response = api_instance.search(project_key, repository_slug, filter=filter, role=role, direction=direction, start=start, limit=limit)
        print("The response of PullRequestsApi->search:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->search: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **filter** | **str**| (optional) Return only users, whose username, name or email address &lt;i&gt;contain&lt;/i&gt; the filter value | [optional] 
 **role** | **str**| (optional) The role associated with the pull request participant. This must be one of AUTHOR, REVIEWER, or PARTICIPANT | [optional] 
 **direction** | **str**| (optional), Defaults to &lt;strong&gt;INCOMING&lt;/strong&gt;) the direction relative to the specified repository. Either &lt;strong&gt;INCOMING&lt;/strong&gt; or &lt;strong&gt;OUTGOING&lt;/strong&gt;. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetUsersWithoutAnyPermission200Response**](GetUsersWithoutAnyPermission200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of users that match the search criteria. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_merge_config**
> RestPullRequestMergeConfig set_merge_config(scm_id, rest_pull_request_settings=rest_pull_request_settings)

Update merge strategies

Update the pull request merge strategies for the context repository. 

The authenticated user must have <strong>ADMIN</strong> permission to call this resource. 

Only the strategies provided will be enabled, only one may be set to default 

The commit message template will not be updated if not provided, and will be deleted if the `commitMessageTemplate` attribute is empty, i.e: `commitMessageTemplate: {}`.

An explicitly set pull request merge strategy configuration can be deleted by POSTing a document with an empty `mergeConfig` attribute. i.e:
```
{ 
    "mergeConfig": {} 
} 
```

Upon completion of this request, the effective configuration will be the default configuration.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_merge_config import RestPullRequestMergeConfig
from .models.rest_pull_request_settings import RestPullRequestSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    scm_id = 'scm_id_example' # str | the id of the scm to get strategies for
    rest_pull_request_settings = .RestPullRequestSettings() # RestPullRequestSettings | the settings (optional)

    try:
        # Update merge strategies
        api_response = api_instance.set_merge_config(scm_id, rest_pull_request_settings=rest_pull_request_settings)
        print("The response of PullRequestsApi->set_merge_config:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->set_merge_config: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **scm_id** | **str**| the id of the scm to get strategies for | 
 **rest_pull_request_settings** | [**RestPullRequestSettings**](RestPullRequestSettings.md)| the settings | [optional] 

### Return type

[**RestPullRequestMergeConfig**](RestPullRequestMergeConfig.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository pull request merge strategies for the context repository. |  -  |
**400** | The repository pull request merge strategies were not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to administrate thespecified repository. |  -  |
**404** | The specified repository does not exist. |  -  |
**409** | Setting or deleting merge configurations isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_changes1**
> RestChange stream_changes1(project_key, pull_request_id, repository_slug, since_id=since_id, change_scope=change_scope, until_id=until_id, with_comments=with_comments, start=start, limit=limit)

Gets pull request changes

Gets changes for the specified PullRequest.

If the changeScope query parameter is set to 'UNREVIEWED', the application will attempt to stream unreviewed changes based on the lastReviewedCommit of the current user, which are the changes between the lastReviewedCommit and the latest commit of the source branch. The current user is considered to <i>not</i> have any unreviewed changes for the pull request when the lastReviewedCommit is either null (everything is unreviewed, so all changes are streamed), equal to the latest commit of the source branch (everything is reviewed), or no longer on the source branch (the source branch has been rebased). In these cases, the application will fall back to streaming all changes (the default), which is the effective diff for the pull request. The type of changes streamed can be determined by the changeScope parameter included in the properties map of the response. 

Note: This resource is currently <i>not paged</i>. The server will return at most one page. The server will truncate the number of changes to either the request's page limit or an internal maximum, whichever is smaller. The start parameter of the page request is also ignored. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_change import RestChange
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    since_id = 'since_id_example' # str | The since commit hash to stream changes for a RANGE arbitrary change scope (optional)
    change_scope = 'change_scope_example' # str | UNREVIEWED to stream the unreviewed changes for the current user (if they exist); RANGE to stream changes between two arbitrary commits (requires 'sinceId' and 'untilId'); otherwise ALL to stream all changes (the default) (optional)
    until_id = 'until_id_example' # str | The until commit hash to stream changes for a RANGE arbitrary change scope (optional)
    with_comments = 'with_comments_example' # str | true to apply comment counts in the changes (the default); otherwise, false to stream changes without comment counts (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Gets pull request changes
        api_response = api_instance.stream_changes1(project_key, pull_request_id, repository_slug, since_id=since_id, change_scope=change_scope, until_id=until_id, with_comments=with_comments, start=start, limit=limit)
        print("The response of PullRequestsApi->stream_changes1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->stream_changes1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **since_id** | **str**| The since commit hash to stream changes for a RANGE arbitrary change scope | [optional] 
 **change_scope** | **str**| UNREVIEWED to stream the unreviewed changes for the current user (if they exist); RANGE to stream changes between two arbitrary commits (requires &#39;sinceId&#39; and &#39;untilId&#39;); otherwise ALL to stream all changes (the default) | [optional] 
 **until_id** | **str**| The until commit hash to stream changes for a RANGE arbitrary change scope | [optional] 
 **with_comments** | **str**| true to apply comment counts in the changes (the default); otherwise, false to stream changes without comment counts | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**RestChange**](RestChange.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of unreviewed Changes for the current user from the supplied pull request, including the unreviewedCommits in the properties map. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository or pull request. |  -  |
**404** | The repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_diff2**
> RestDiff stream_diff2(path, project_key, pull_request_id, repository_slug, avatar_scheme=avatar_scheme, context_lines=context_lines, since_id=since_id, src_path=src_path, diff_type=diff_type, until_id=until_id, whitespace=whitespace, with_comments=with_comments, avatar_size=avatar_size)

Stream a diff within a pull request

Streams a diff within a pull request. 

If the specified file has been copied, moved or renamed, the <code>srcPath</code> must also be specified to produce the correct diff. 

To stream a raw text representation of the diff, this endpoint can be called with the request header 'Accept: text/plain'. 

Note: This RESTful endpoint is currently <i>not paged</i>. The server will internally apply a hard cap to the streamed lines, and it is not possible to request subsequent pages if that cap is exceeded. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_diff import RestDiff
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    path = 'path_example' # str | The path to the file which should be diffed (optional)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    avatar_scheme = 'avatar_scheme_example' # str | The security scheme for avatar URLs. If the scheme is not present then it is inherited from the request. It can be set to \"https\" to force the use of secure URLs. Not applicable if streaming raw diff (optional)
    context_lines = 'context_lines_example' # str | The number of context lines to include around added/removed lines in the diff (optional)
    since_id = 'since_id_example' # str | The since commit hash to stream a diff between two arbitrary hashes (optional)
    src_path = 'src_path_example' # str | The previous path to the file, if the file has been copied, moved or renamed (optional)
    diff_type = 'diff_type_example' # str | The type of diff being requested. When withComments is true this works as a hint to the system to attach the correct set of comments to the diff. Not applicable if streaming raw diff (optional)
    until_id = 'until_id_example' # str | The until commit hash to stream a diff between two arbitrary hashes (optional)
    whitespace = 'whitespace_example' # str | Optional whitespace flag which can be set to <code>ignore-all</code> (optional)
    with_comments = 'with_comments_example' # str | <code>true</code> to embed comments in the diff (the default); otherwise, <code>false</code> to stream the diff without comments. Not applicable if streaming raw diff (optional)
    avatar_size = 'avatar_size_example' # str | If present the service adds avatar URLs for comment authors where the provided value specifies the desired avatar size in pixels. Not applicable if streaming raw diff (optional)

    try:
        # Stream a diff within a pull request
        api_response = api_instance.stream_diff2(path, project_key, pull_request_id, repository_slug, avatar_scheme=avatar_scheme, context_lines=context_lines, since_id=since_id, src_path=src_path, diff_type=diff_type, until_id=until_id, whitespace=whitespace, with_comments=with_comments, avatar_size=avatar_size)
        print("The response of PullRequestsApi->stream_diff2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->stream_diff2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The path to the file which should be diffed (optional) | 
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **avatar_scheme** | **str**| The security scheme for avatar URLs. If the scheme is not present then it is inherited from the request. It can be set to \&quot;https\&quot; to force the use of secure URLs. Not applicable if streaming raw diff | [optional] 
 **context_lines** | **str**| The number of context lines to include around added/removed lines in the diff | [optional] 
 **since_id** | **str**| The since commit hash to stream a diff between two arbitrary hashes | [optional] 
 **src_path** | **str**| The previous path to the file, if the file has been copied, moved or renamed | [optional] 
 **diff_type** | **str**| The type of diff being requested. When withComments is true this works as a hint to the system to attach the correct set of comments to the diff. Not applicable if streaming raw diff | [optional] 
 **until_id** | **str**| The until commit hash to stream a diff between two arbitrary hashes | [optional] 
 **whitespace** | **str**| Optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 
 **with_comments** | **str**| &lt;code&gt;true&lt;/code&gt; to embed comments in the diff (the default); otherwise, &lt;code&gt;false&lt;/code&gt; to stream the diff without comments. Not applicable if streaming raw diff | [optional] 
 **avatar_size** | **str**| If present the service adds avatar URLs for comment authors where the provided value specifies the desired avatar size in pixels. Not applicable if streaming raw diff | [optional] 

### Return type

[**RestDiff**](RestDiff.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of differences from a pull request. |  -  |
**400** | If the request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository or pull request. |  -  |
**404** | The repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_patch1**
> stream_patch1(project_key, pull_request_id, repository_slug)

Stream pull request as patch

Streams a patch representing a pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Stream pull request as patch
        api_instance.stream_patch1(project_key, pull_request_id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->stream_patch1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A patch representing the specified pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to access the pull request. |  -  |
**404** | The pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_raw_diff2**
> stream_raw_diff2(project_key, pull_request_id, repository_slug, context_lines=context_lines, whitespace=whitespace)

Stream raw pull request diff

Streams the raw diff for a pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    context_lines = 'context_lines_example' # str | The number of context lines to include around added/removed lines in the diff (optional)
    whitespace = 'whitespace_example' # str | optional whitespace flag which can be set to <code>ignore-all</code> (optional)

    try:
        # Stream raw pull request diff
        api_instance.stream_raw_diff2(project_key, pull_request_id, repository_slug, context_lines=context_lines, whitespace=whitespace)
    except Exception as e:
        print("Exception when calling PullRequestsApi->stream_raw_diff2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **context_lines** | **str**| The number of context lines to include around added/removed lines in the diff | [optional] 
 **whitespace** | **str**| optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, text/html

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A raw diff for the specified pull request. |  -  |
**400** | The currently authenticated user has insufficient permissions to view the specified pull request. |  -  |
**404** | The pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **try_auto_merge**
> RestAutoMergeProcessingResult try_auto_merge(project_key, pull_request_id, repository_slug)

Auto-merge pull request

Requests the system to try merging the pull request if auto-merge was requested on it.

The authenticated user must have <strong>REPO_WRITE</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_merge_processing_result import RestAutoMergeProcessingResult
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Auto-merge pull request
        api_response = api_instance.try_auto_merge(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->try_auto_merge:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->try_auto_merge: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestAutoMergeProcessingResult**](RestAutoMergeProcessingResult.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The result of trying to auto-merge the pull request. |  -  |
**400** | An auto-merge request was not submitted for this pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to modify the pull request. |  -  |
**403** | The auto-merge setting is not enabled for the repository that this pull request targets. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **un_react1**
> un_react1(project_key, comment_id, pull_request_id, emoticon, repository_slug)

Remove a reaction from a PR comment

Remove an emoticon reaction from a pull request comment

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The comment id.
    pull_request_id = 'pull_request_id_example' # str | The pull request id.
    emoticon = 'emoticon_example' # str | The emoticon to remove
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Remove a reaction from a PR comment
        api_instance.un_react1(project_key, comment_id, pull_request_id, emoticon, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->un_react1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The comment id. | 
 **pull_request_id** | **str**| The pull request id. | 
 **emoticon** | **str**| The emoticon to remove | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The added reaction |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unassign_participant_role**
> unassign_participant_role(project_key, user_slug, pull_request_id, repository_slug)

Unassign pull request participant

Unassigns a participant from the REVIEWER role they may have been given in a pull request. 

If the participant has no explicit role this method has no effect. 

Afterwards, the user will still remain a participant in the pull request but their role will be reduced to PARTICIPANT. This is because once made a participant of a pull request, a user will forever remain a participant. Only their role may be altered. 

The authenticated user must have <strong>REPO_WRITE</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    user_slug = 'user_slug_example' # str | The slug for the user being unassigned
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Unassign pull request participant
        api_instance.unassign_participant_role(project_key, user_slug, pull_request_id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->unassign_participant_role: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **user_slug** | **str**| The slug for the user being unassigned | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The update completed. |  -  |
**400** | The request does not have the username. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | Removing reviewers isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unassign_participant_role1**
> unassign_participant_role1(project_key, pull_request_id, repository_slug, username=username)

Unassign pull request participant

Unassigns a participant from the REVIEWER role they may have been given in a pull request. 

If the participant has no explicit role this method has no effect. 

Afterwards, the user will still remain a participant in the pull request but their role will be reduced to PARTICIPANT. This is because once made a participant of a pull request, a user will forever remain a participant. Only their role may be altered. 

The authenticated user must have <strong>REPO_WRITE</strong> permission for the repository that this pull request targets to call this resource. 

<strong>Deprecated since 4.2</strong>. Use /rest/api/1.0/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants/{userSlug} instead.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    username = 'username_example' # str |  (optional)

    try:
        # Unassign pull request participant
        api_instance.unassign_participant_role1(project_key, pull_request_id, repository_slug, username=username)
    except Exception as e:
        print("Exception when calling PullRequestsApi->unassign_participant_role1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **username** | **str**|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The update completed. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | Removing reviewers isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unwatch1**
> unwatch1(project_key, pull_request_id, repository_slug)

Stop watching pull request

Remove the authenticated user as a watcher for the specified pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Stop watching pull request
        api_instance.unwatch1(project_key, pull_request_id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->unwatch1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user is no longer watching the pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update**
> RestPullRequest update(project_key, pull_request_id, repository_slug, rest_pull_request=rest_pull_request)

Update pull request metadata

Update the title, description, reviewers, destination branch or draft status of an existing pull request. 

**Note:** the <em>reviewers</em> list may be updated using this resource. However the <em>author</em> and <em>participants</em> list may not. 

The authenticated user must either: 

- be the author of the pull request and have the <strong>REPO_READ</strong> permission for the repository that this pull request targets; or
- have the <strong>REPO_WRITE</strong> permission for the repository that this pull request targets


to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request import RestPullRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_pull_request = .RestPullRequest() # RestPullRequest | The updated pull request (optional)

    try:
        # Update pull request metadata
        api_response = api_instance.update(project_key, pull_request_id, repository_slug, rest_pull_request=rest_pull_request)
        print("The response of PullRequestsApi->update:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_pull_request** | [**RestPullRequest**](RestPullRequest.md)| The updated pull request | [optional] 

### Return type

[**RestPullRequest**](RestPullRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated pull request. |  -  |
**400** | One of the following error cases occurred (check the error message for more details):   - The request tried to modify the &lt;em&gt;author&lt;/em&gt; or &lt;em&gt;participants&lt;/em&gt;. - The pull request&#39;s version attribute was not specified. - A reviewer&#39;s username was not specified. - The toRef ID value was incorrectly left blank  |  -  |
**401** | The currently authenticated user has insufficient permissions to update the specified pull request. |  -  |
**404** | One of the specified repositories or branches does not exist. |  -  |
**409** | One of the following error cases occurred (check the error message for more details):   - The specified version is out of date. - One of the reviewers could not be added to the pull request. - If updating the destination branch:    - There is already an open pull request with an identical to branch    - The from and new to branch &lt;i&gt;are&lt;/i&gt; the same    - The new destination branch up-to-date is up-to-date with all of                 changes from the from branch, resulting in a pull request with                 nothing to merge             - The &lt;em&gt;to&lt;/em&gt; repository is archived.  |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update1**
> RestReviewerGroup update1(project_key, id, rest_reviewer_group=rest_reviewer_group)

Update reviewer group attributes

Update the attributes of a reviewer group.

The authenticated user must have <b>PROJECT_READ</b> permission for the specified project to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_reviewer_group import RestReviewerGroup
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be updated
    rest_reviewer_group = .RestReviewerGroup() # RestReviewerGroup | The request containing the attributes of the reviewer group to be updated. Only the attributes to be updated need to be present in this object. (optional)

    try:
        # Update reviewer group attributes
        api_response = api_instance.update1(project_key, id, rest_reviewer_group=rest_reviewer_group)
        print("The response of PullRequestsApi->update1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be updated | 
 **rest_reviewer_group** | [**RestReviewerGroup**](RestReviewerGroup.md)| The request containing the attributes of the reviewer group to be updated. Only the attributes to be updated need to be present in this object. | [optional] 

### Return type

[**RestReviewerGroup**](RestReviewerGroup.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of changes. |  -  |
**400** | The updated attribute does not meet the requirements. E.g. the name exceeds 50 characters, setting name to blank. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the project. |  -  |
**404** | The project scope supplied does not exist. |  -  |
**409** | The new updated name already exists. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update2**
> RestReviewerGroup update2(project_key, id, repository_slug, rest_reviewer_group=rest_reviewer_group)

Update reviewer group attributes

Update the attributes of a reviewer group.

The authenticated user must have <b>REPO_ADMIN</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_reviewer_group import RestReviewerGroup
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the reviewer group to be updated
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_reviewer_group = .RestReviewerGroup() # RestReviewerGroup | The request containing the attributes of the reviewer group to be updated. Only the attributes to be updated need to be present in this object. (optional)

    try:
        # Update reviewer group attributes
        api_response = api_instance.update2(project_key, id, repository_slug, rest_reviewer_group=rest_reviewer_group)
        print("The response of PullRequestsApi->update2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the reviewer group to be updated | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_reviewer_group** | [**RestReviewerGroup**](RestReviewerGroup.md)| The request containing the attributes of the reviewer group to be updated. Only the attributes to be updated need to be present in this object. | [optional] 

### Return type

[**RestReviewerGroup**](RestReviewerGroup.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated reviewer group. |  -  |
**400** | The updated attribute does not meet the requirements. E.g. the name exceeds 50 characters, setting name to blank. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository scope supplied does not exist. |  -  |
**409** | The new updated name already exists. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_comment1**
> RestComment update_comment1(project_key, comment_id, pull_request_id, repository_slug, rest_comment=rest_comment)

Update pull request comment

Update a comment, with the following restrictions: 

- only the author of the comment may update the <i>text</i> of the comment
- only the author of the comment, the author of the pull request or repository admins and above may update   the other fields of a comment


Convert a comment to a task or vice versa. 

Comments can be converted to tasks by setting the 'severity' attribute to 'BLOCKER': 
```

{ 
"severity": "BLOCKER" 
}

```

Tasks can be converted to comments by setting the 'severity' attribute to 'NORMAL': ```

{ 
"severity": "NORMAL" 
}

```

Resolve a blocker comment. 

Blocker comments can be resolved by setting the 'state' attribute to 'RESOLVED': ```

{ 
"state": "RESOLVED" 
} 
```

<strong>Note:</strong> the supplied JSON object must contain a <code>version</code> that must match the server's version of the comment or the update will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the update. Look for the 'version' attribute in the returned JSON structure. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_comment = .RestComment() # RestComment | The comment to add. (optional)

    try:
        # Update pull request comment
        api_response = api_instance.update_comment1(project_key, comment_id, pull_request_id, repository_slug, rest_comment=rest_comment)
        print("The response of PullRequestsApi->update_comment1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update_comment1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_comment** | [**RestComment**](RestComment.md)| The comment to add. | [optional] 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly updated comment. |  -  |
**400** | The comment was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request, update a comment or watch the pull request. |  -  |
**404** | Unable to find the supplied project, repository, pull request or comment. |  -  |
**409** | The comment version supplied does not match the current version or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_comment2**
> RestComment update_comment2(project_key, comment_id, pull_request_id, repository_slug, rest_comment=rest_comment)

Update pull request comment

Update a comment, with the following restrictions: 

- only the author of the comment may update the <i>text</i> of the comment
- only the author of the comment, the author of the pull request or repository admins and above may update the other fields of a comment
</ul> 

Convert a comment to a task or vice versa. 

Comments can be converted to tasks by setting the 'severity' attribute to 'BLOCKER': 
 <pre> { 
 "severity": "BLOCKER" 
 } 
 </pre>

Tasks can be converted to comments by setting the 'severity' attribute to 'NORMAL':  <pre> { 
 "severity": "NORMAL" 
 } 
 </pre>

Resolve a task. 

Tasks can be resolved by setting the 'state' attribute to 'RESOLVED':  <pre> { 
 "state": "RESOLVED" 
 } 
 </pre>

<strong>Note:</strong> the supplied JSON object must contain a <code>version</code> that must match the server's version of the comment or the update will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the update. Look for the 'version' attribute in the returned JSON structure. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_comment = .RestComment() # RestComment | The updated comment (optional)

    try:
        # Update pull request comment
        api_response = api_instance.update_comment2(project_key, comment_id, pull_request_id, repository_slug, rest_comment=rest_comment)
        print("The response of PullRequestsApi->update_comment2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update_comment2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The ID of the comment to retrieve. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_comment** | [**RestComment**](RestComment.md)| The updated comment | [optional] 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly updated comment. |  -  |
**400** | The comment was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request, update a comment or watch the pull request. |  -  |
**404** | Unable to find the supplied project, repository, pull request or comment. |  -  |
**409** | The comment version supplied does not match the current version or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_pull_request_condition**
> RestPullRequestCondition update_pull_request_condition(project_key, id, rest_default_reviewers_request=rest_default_reviewers_request)

Update default reviewer condition

Update the default reviewer pull request condition for the given ID.

### Example


```python
import bitbucketclient
from .models.rest_default_reviewers_request import RestDefaultReviewersRequest
from .models.rest_pull_request_condition import RestPullRequestCondition
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the pull request condition.
    rest_default_reviewers_request = .RestDefaultReviewersRequest() # RestDefaultReviewersRequest | The new details for the default reviewer pull request condition. (optional)

    try:
        # Update default reviewer condition
        api_response = api_instance.update_pull_request_condition(project_key, id, rest_default_reviewers_request=rest_default_reviewers_request)
        print("The response of PullRequestsApi->update_pull_request_condition:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update_pull_request_condition: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the pull request condition. | 
 **rest_default_reviewers_request** | [**RestDefaultReviewersRequest**](RestDefaultReviewersRequest.md)| The new details for the default reviewer pull request condition. | [optional] 

### Return type

[**RestPullRequestCondition**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated default reviewer pull request condition. |  -  |
**400** | The request was malformed |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_pull_request_condition1**
> RestPullRequestCondition update_pull_request_condition1(project_key, id, repository_slug, update_pull_request_condition1_request=update_pull_request_condition1_request)

Update default reviewer condition

Update the default reviewer pull request condition for the given ID.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_condition import RestPullRequestCondition
from .models.update_pull_request_condition1_request import UpdatePullRequestCondition1Request
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The ID of the pull request condition
    repository_slug = 'repository_slug_example' # str | The repository slug.
    update_pull_request_condition1_request = .UpdatePullRequestCondition1Request() # UpdatePullRequestCondition1Request |  (optional)

    try:
        # Update default reviewer condition
        api_response = api_instance.update_pull_request_condition1(project_key, id, repository_slug, update_pull_request_condition1_request=update_pull_request_condition1_request)
        print("The response of PullRequestsApi->update_pull_request_condition1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update_pull_request_condition1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The ID of the pull request condition | 
 **repository_slug** | **str**| The repository slug. | 
 **update_pull_request_condition1_request** | [**UpdatePullRequestCondition1Request**](UpdatePullRequestCondition1Request.md)|  | [optional] 

### Return type

[**RestPullRequestCondition**](RestPullRequestCondition.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated default reviewer pull request condition. |  -  |
**400** | The request was malformed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_status**
> RestPullRequestParticipant update_status(project_key, user_slug, pull_request_id, repository_slug, rest_pull_request_assign_status_request, version=version)

Change pull request status

Change the current user's status for a pull request. Implicitly adds the user as a participant if they are not already. If the current user is the author, this method will fail. 

The possible values for {@code status} are <strong>UNAPPROVED</strong>, <strong>NEEDS_WORK</strong> (which is referred to as "Requested changes" in the frontend from 8.10 onward), or <strong>APPROVED</strong>. 

If the new {@code status} is <strong>NEEDS_WORK</strong> or <strong>APPROVED</strong> then the {@code lastReviewedCommit} for the participant will be updated to the latest commit of the source branch of the pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_pull_request_assign_status_request import RestPullRequestAssignStatusRequest
from .models.rest_pull_request_participant import RestPullRequestParticipant
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    user_slug = 'user_slug_example' # str | The slug for the user changing their status
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_pull_request_assign_status_request = .RestPullRequestAssignStatusRequest() # RestPullRequestAssignStatusRequest | The participant representing the status to set, includes the status of the participant and last reviewed commit. If last reviewed commit is provided, it will be used to update the participant status. The operation will fail if the latest commit of the pull request does not match the provided last reviewed commit. If last reviewed commit is not provided, the latest commit of the pull request will be used for the update by default.
    version = 'version_example' # str | The current version of the pull request. If the server's version isn't the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the 'version' attribute in the returned JSON structure. Note: This parameter is deprecated. Use last reviewed commit in request body instead (optional)

    try:
        # Change pull request status
        api_response = api_instance.update_status(project_key, user_slug, pull_request_id, repository_slug, rest_pull_request_assign_status_request, version=version)
        print("The response of PullRequestsApi->update_status:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->update_status: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **user_slug** | **str**| The slug for the user changing their status | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_pull_request_assign_status_request** | [**RestPullRequestAssignStatusRequest**](RestPullRequestAssignStatusRequest.md)| The participant representing the status to set, includes the status of the participant and last reviewed commit. If last reviewed commit is provided, it will be used to update the participant status. The operation will fail if the latest commit of the pull request does not match the provided last reviewed commit. If last reviewed commit is not provided, the latest commit of the pull request will be used for the update by default. | 
 **version** | **str**| The current version of the pull request. If the server&#39;s version isn&#39;t the same as the specified version the operation will fail. To determine the current version of the pull request it should be fetched from the server prior to this operation. Look for the &#39;version&#39; attribute in the returned JSON structure. Note: This parameter is deprecated. Use last reviewed commit in request body instead | [optional] 

### Return type

[**RestPullRequestParticipant**](RestPullRequestParticipant.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details of the new participant. |  -  |
**400** | The specified status was invalid or the currently authenticated user is the author of the PR and cannot have its status updated. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |
**409** | The pull request is not open, or has been updated since the last reviewed commit specified by the request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **watch1**
> watch1(project_key, pull_request_id, repository_slug)

Watch pull request

Add the authenticated user as a watcher for the specified pull request. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The pull request ID.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Watch pull request
        api_instance.watch1(project_key, pull_request_id, repository_slug)
    except Exception as e:
        print("Exception when calling PullRequestsApi->watch1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The pull request ID. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user is now watching the pull request. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **withdraw_approval**
> RestPullRequestParticipant withdraw_approval(project_key, pull_request_id, repository_slug)

Unapprove pull request

Remove approval from a pull request as the current user. This does not remove the user as a participant. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that this pull request targets to call this resource. 

<strong>Deprecated since 4.2</strong>. Use /rest/api/1.0/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/participants/{userSlug} instead

### Example


```python
import bitbucketclient
from .models.rest_pull_request_participant import RestPullRequestParticipant
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .PullRequestsApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    pull_request_id = 'pull_request_id_example' # str | The ID of the pull request within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Unapprove pull request
        api_response = api_instance.withdraw_approval(project_key, pull_request_id, repository_slug)
        print("The response of PullRequestsApi->withdraw_approval:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling PullRequestsApi->withdraw_approval: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **pull_request_id** | **str**| The ID of the pull request within the repository | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestPullRequestParticipant**](RestPullRequestParticipant.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details of the updated participant. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified repository or pull request does not exist or the current user is not a participant on the pull request. |  -  |
**409** | The pull request is not open. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

