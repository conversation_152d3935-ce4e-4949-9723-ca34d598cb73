# .SystemMaintenanceApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**cancel_export_job**](SystemMaintenanceApi.md#cancel_export_job) | **POST** /api/latest/migration/exports/{jobId}/cancel | Cancel export job
[**cancel_import_job**](SystemMaintenanceApi.md#cancel_import_job) | **POST** /api/latest/migration/imports/{jobId}/cancel | Cancel import job
[**cancel_mesh_migration_job**](SystemMaintenanceApi.md#cancel_mesh_migration_job) | **POST** /api/latest/migration/mesh/{jobId}/cancel | Cancel Mesh migration job
[**clear_default_branch**](SystemMaintenanceApi.md#clear_default_branch) | **DELETE** /api/latest/admin/default-branch | Clear default branch
[**clear_sender_address**](SystemMaintenanceApi.md#clear_sender_address) | **DELETE** /api/latest/admin/mail-server/sender-address | Update mail configuration
[**connectivity**](SystemMaintenanceApi.md#connectivity) | **GET** /api/latest/admin/git/mesh/diagnostics/connectivity | Generate Mesh connectivity report
[**create_hook_script**](SystemMaintenanceApi.md#create_hook_script) | **POST** /api/latest/hook-scripts | Create a new hook script
[**delete2**](SystemMaintenanceApi.md#delete2) | **DELETE** /api/latest/admin/git/mesh/nodes/{id} | Delete Mesh node
[**delete8**](SystemMaintenanceApi.md#delete8) | **DELETE** /api/latest/admin/rate-limit/settings/users/{userSlug} | Delete user specific rate limit settings
[**delete_avatar**](SystemMaintenanceApi.md#delete_avatar) | **DELETE** /api/latest/users/{userSlug}/avatar.png | Delete user avatar
[**delete_banner**](SystemMaintenanceApi.md#delete_banner) | **DELETE** /api/latest/admin/banner | Delete announcement banner
[**delete_hook_script**](SystemMaintenanceApi.md#delete_hook_script) | **DELETE** /api/latest/hook-scripts/{scriptId} | Delete a hook script.
[**delete_mail_config**](SystemMaintenanceApi.md#delete_mail_config) | **DELETE** /api/latest/admin/mail-server | Delete mail configuration
[**dismiss_retention_config_review_notification**](SystemMaintenanceApi.md#dismiss_retention_config_review_notification) | **DELETE** /audit/latest/notification-settings/retention-config-review | Dismiss retention config notification
[**get2**](SystemMaintenanceApi.md#get2) | **GET** /api/latest/admin/license | Get license details
[**get6**](SystemMaintenanceApi.md#get6) | **GET** /api/latest/admin/rate-limit/settings/users/{userSlug} | Get user specific rate limit settings
[**get_active_mesh_migration_summary**](SystemMaintenanceApi.md#get_active_mesh_migration_summary) | **GET** /api/latest/migration/mesh/summary | Get summary for Mesh migration job
[**get_all_mesh_migration_summaries**](SystemMaintenanceApi.md#get_all_mesh_migration_summaries) | **GET** /api/latest/migration/mesh/summaries | Get all Mesh migration job summaries
[**get_all_rate_limit_settings**](SystemMaintenanceApi.md#get_all_rate_limit_settings) | **GET** /api/latest/admin/rate-limit/settings/users | Get rate limit settings for user
[**get_all_registered_mesh_nodes**](SystemMaintenanceApi.md#get_all_registered_mesh_nodes) | **GET** /api/latest/admin/git/mesh/nodes | Get all registered Mesh nodes
[**get_application_properties**](SystemMaintenanceApi.md#get_application_properties) | **GET** /api/latest/application-properties | Get application properties
[**get_banner**](SystemMaintenanceApi.md#get_banner) | **GET** /api/latest/admin/banner | Get announcement banner
[**get_control_plane_public_key**](SystemMaintenanceApi.md#get_control_plane_public_key) | **GET** /api/latest/admin/git/mesh/config/control-plane.pem | Get the control plane PEM
[**get_default_branch**](SystemMaintenanceApi.md#get_default_branch) | **GET** /api/latest/admin/default-branch | Get the default branch
[**get_export_job**](SystemMaintenanceApi.md#get_export_job) | **GET** /api/latest/migration/exports/{jobId} | Get export job details
[**get_export_job_messages**](SystemMaintenanceApi.md#get_export_job_messages) | **GET** /api/latest/migration/exports/{jobId}/messages | Get job messages
[**get_global_settings**](SystemMaintenanceApi.md#get_global_settings) | **GET** /admin | Get global SSH key settings
[**get_history**](SystemMaintenanceApi.md#get_history) | **GET** /api/latest/admin/rate-limit/history | Get rate limit history
[**get_hook_script**](SystemMaintenanceApi.md#get_hook_script) | **GET** /api/latest/hook-scripts/{scriptId} | Get a hook script
[**get_import_job**](SystemMaintenanceApi.md#get_import_job) | **GET** /api/latest/migration/imports/{jobId} | Get import job status
[**get_import_job_messages**](SystemMaintenanceApi.md#get_import_job_messages) | **GET** /api/latest/migration/imports/{jobId}/messages | Get import job messages
[**get_information**](SystemMaintenanceApi.md#get_information) | **GET** /api/latest/admin/cluster | Get cluster node information
[**get_label**](SystemMaintenanceApi.md#get_label) | **GET** /api/latest/labels/{labelName} | Get label
[**get_labelables**](SystemMaintenanceApi.md#get_labelables) | **GET** /api/latest/labels/{labelName}/labeled | Get labelables for label
[**get_labels**](SystemMaintenanceApi.md#get_labels) | **GET** /api/latest/labels | Get all labels
[**get_level**](SystemMaintenanceApi.md#get_level) | **GET** /api/latest/logs/logger/{loggerName} | Get current log level
[**get_mail_config**](SystemMaintenanceApi.md#get_mail_config) | **GET** /api/latest/admin/mail-server | Get mail configuration
[**get_mesh_migration_job**](SystemMaintenanceApi.md#get_mesh_migration_job) | **GET** /api/latest/migration/mesh/{jobId} | Get Mesh migration job details
[**get_mesh_migration_job_messages**](SystemMaintenanceApi.md#get_mesh_migration_job_messages) | **GET** /api/latest/migration/mesh/{jobId}/messages | Get Mesh migration job messages
[**get_mesh_migration_job_summary**](SystemMaintenanceApi.md#get_mesh_migration_job_summary) | **GET** /api/latest/migration/mesh/{jobId}/summary | Get Mesh migration job summary
[**get_registered_mesh_node_by_id**](SystemMaintenanceApi.md#get_registered_mesh_node_by_id) | **GET** /api/latest/admin/git/mesh/nodes/{id} | Get Mesh node
[**get_repository_archive_policy**](SystemMaintenanceApi.md#get_repository_archive_policy) | **GET** /policies/latest/admin/repos/archive | Get repository archive policy
[**get_repository_delete_policy**](SystemMaintenanceApi.md#get_repository_delete_policy) | **GET** /policies/latest/admin/repos/delete | Get repository delete policy
[**get_root_level**](SystemMaintenanceApi.md#get_root_level) | **GET** /api/latest/logs/rootLogger | Get root log level
[**get_sender_address**](SystemMaintenanceApi.md#get_sender_address) | **GET** /api/latest/admin/mail-server/sender-address | Get server mail address
[**get_settings2**](SystemMaintenanceApi.md#get_settings2) | **GET** /api/latest/logs/settings | Get debug logging and profiling
[**get_settings3**](SystemMaintenanceApi.md#get_settings3) | **GET** /api/latest/admin/rate-limit/settings | Get rate limit settings
[**get_support_zip**](SystemMaintenanceApi.md#get_support_zip) | **GET** /api/latest/admin/git/mesh/support-zips/{id} | Get support zip for node
[**get_support_zips**](SystemMaintenanceApi.md#get_support_zips) | **GET** /api/latest/admin/git/mesh/support-zips | Get support zips for all Mesh nodes
[**get_supported_key_types**](SystemMaintenanceApi.md#get_supported_key_types) | **GET** /admin/supported-key-types | Get supported SSH key algorithms and lengths
[**get_user**](SystemMaintenanceApi.md#get_user) | **GET** /api/latest/users/{userSlug} | Get user
[**get_user_settings**](SystemMaintenanceApi.md#get_user_settings) | **GET** /api/latest/users/{userSlug}/settings | Get user settings
[**get_users2**](SystemMaintenanceApi.md#get_users2) | **GET** /api/latest/users | Get all users
[**preview_export**](SystemMaintenanceApi.md#preview_export) | **POST** /api/latest/migration/exports/preview | Preview export
[**preview_mesh_migration**](SystemMaintenanceApi.md#preview_mesh_migration) | **POST** /api/latest/migration/mesh/preview | Preview Mesh migration
[**read**](SystemMaintenanceApi.md#read) | **GET** /api/latest/hook-scripts/{scriptId}/content | Get hook script content
[**register_new_mesh_node**](SystemMaintenanceApi.md#register_new_mesh_node) | **POST** /api/latest/admin/git/mesh/nodes | Register new Mesh node
[**search_mesh_migration_repos**](SystemMaintenanceApi.md#search_mesh_migration_repos) | **GET** /api/latest/migration/mesh/repos | Find repositories by Mesh migration state
[**set2**](SystemMaintenanceApi.md#set2) | **POST** /api/latest/admin/rate-limit/settings/users | Set rate limit settings for users
[**set3**](SystemMaintenanceApi.md#set3) | **PUT** /api/latest/admin/rate-limit/settings/users/{userSlug} | Set rate limit settings for user
[**set_banner**](SystemMaintenanceApi.md#set_banner) | **PUT** /api/latest/admin/banner | Update/Set announcement banner
[**set_default_branch**](SystemMaintenanceApi.md#set_default_branch) | **PUT** /api/latest/admin/default-branch | Update/Set default branch
[**set_level**](SystemMaintenanceApi.md#set_level) | **PUT** /api/latest/logs/logger/{loggerName}/{levelName} | Set log level
[**set_mail_config**](SystemMaintenanceApi.md#set_mail_config) | **PUT** /api/latest/admin/mail-server | Update mail configuration
[**set_repository_archive_policy**](SystemMaintenanceApi.md#set_repository_archive_policy) | **PUT** /policies/latest/admin/repos/archive | Update repository archive policy
[**set_repository_delete_policy**](SystemMaintenanceApi.md#set_repository_delete_policy) | **PUT** /policies/latest/admin/repos/delete | Update the repository delete policy
[**set_root_level**](SystemMaintenanceApi.md#set_root_level) | **PUT** /api/latest/logs/rootLogger/{levelName} | Set root log level
[**set_sender_address**](SystemMaintenanceApi.md#set_sender_address) | **PUT** /api/latest/admin/mail-server/sender-address | Update server mail address
[**set_settings2**](SystemMaintenanceApi.md#set_settings2) | **PUT** /api/latest/logs/settings | Set debug logging and profiling
[**set_settings3**](SystemMaintenanceApi.md#set_settings3) | **PUT** /api/latest/admin/rate-limit/settings | Set rate limit
[**start_export**](SystemMaintenanceApi.md#start_export) | **POST** /api/latest/migration/exports | Start export job
[**start_import**](SystemMaintenanceApi.md#start_import) | **POST** /api/latest/migration/imports | Start import job
[**start_mesh_migration**](SystemMaintenanceApi.md#start_mesh_migration) | **POST** /api/latest/migration/mesh | Start Mesh migration job
[**update_global_settings**](SystemMaintenanceApi.md#update_global_settings) | **PUT** /admin | Update global SSH key settings
[**update_hook_script**](SystemMaintenanceApi.md#update_hook_script) | **PUT** /api/latest/hook-scripts/{scriptId} | Update a hook script
[**update_license**](SystemMaintenanceApi.md#update_license) | **POST** /api/latest/admin/license | Update license
[**update_mesh_node**](SystemMaintenanceApi.md#update_mesh_node) | **PUT** /api/latest/admin/git/mesh/nodes/{id} | Update Mesh node
[**update_settings**](SystemMaintenanceApi.md#update_settings) | **POST** /api/latest/users/{userSlug}/settings | Update user settings
[**update_user_details1**](SystemMaintenanceApi.md#update_user_details1) | **PUT** /api/latest/users | Update user details
[**update_user_password1**](SystemMaintenanceApi.md#update_user_password1) | **PUT** /api/latest/users/credentials | Set password
[**upload_avatar1**](SystemMaintenanceApi.md#upload_avatar1) | **POST** /api/latest/users/{userSlug}/avatar.png | Update user avatar


# **cancel_export_job**
> cancel_export_job(job_id)

Cancel export job

Requests the cancellation of an export job.

The request to cancel a job will be processed successfully if the job is actually still running. If it has already finished (successfully or with errors) or if it has already been canceled before, then an error will be returned.

There might be a small delay between accepting the request and actually cancelling the job. In most cases, the delay will be close to instantaneously. In the unlikely case of communication issues across a cluster, it can however take a few seconds to cancel a job.

A client should always actively query the job status to confirm that a job has been successfully canceled.

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | the ID of the job to cancel

    try:
        # Cancel export job
        api_instance.cancel_export_job(job_id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->cancel_export_job: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| the ID of the job to cancel | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The job has successfully been marked for cancellation |  -  |
**401** | The currently authenticated user has insufficient permissions to cancel this job. |  -  |
**404** | The specified job does not exist. |  -  |
**409** | The job was in a state that does not allow cancellation, e.g. it has already finished. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **cancel_import_job**
> cancel_import_job(job_id)

Cancel import job

Requests the cancellation of an import job.

The request to cancel a job will be processed successfully if the job is actually still running. If it has already finished (successfully or with errors) or if it has already been canceled before, then an error will be returned.

Note that import jobs are not canceled as instantaneously as export jobs. Rather, once the request has been accepted, there are a number of checkpoints at which the job will actually apply it and stop. This is to keep the system in a reasonably consistent state:

- After the current fork hierarchy has been imported and verified.
- Before the next repository is imported.
- Before the next pull request is imported.

A client should always actively query the job status to confirm that a job has been successfully canceled.

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | the ID of the job to cancel

    try:
        # Cancel import job
        api_instance.cancel_import_job(job_id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->cancel_import_job: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| the ID of the job to cancel | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The job has successfully been marked for cancellation. |  -  |
**401** | The currently authenticated user has insufficient permissions to cancel this job. |  -  |
**404** | The specified job does not exist. |  -  |
**409** | The job was in a state that does not allow cancellation, e.g. it has already finished. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **cancel_mesh_migration_job**
> cancel_mesh_migration_job(job_id)

Cancel Mesh migration job

Requests the cancellation of a migration job. 

The request to cancel a job will be processed successfully if the job is actually still running. If it has already finished (successfully or with errors) or if it has already been canceled before, then an error will be returned. 

There might be a small delay between accepting the request and actually cancelling the job. In most cases, the delay will be close to instantaneously. In the unlikely case of communication issues across a cluster, it can however take a few seconds to cancel a job.

A client should always actively query the job status to confirm that a job has been successfully canceled.

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job to cancel

    try:
        # Cancel Mesh migration job
        api_instance.cancel_mesh_migration_job(job_id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->cancel_mesh_migration_job: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job to cancel | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The migration job was successfully marked for cancellation. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | The specified job ID does not exist. |  -  |
**409** | The migration job has already been canceled or finished. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **clear_default_branch**
> clear_default_branch()

Clear default branch

Clears the global default branch, which is used when creating new repositories if an explicit default branch is not specified, if one has been configured.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Clear default branch
        api_instance.clear_default_branch()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->clear_default_branch: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default branch has been cleared. |  -  |
**401** | The current user does not have sufficient permissions to clear the global default branch. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **clear_sender_address**
> clear_sender_address()

Update mail configuration

Clears the server email address.

The authenticated user must have the <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Update mail configuration
        api_instance.clear_sender_address()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->clear_sender_address: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | he server email address was successfully cleared. |  -  |
**401** | The currently authenticated user has insufficient permissions toclear the server email address. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **connectivity**
> RestMeshConnectivityReport connectivity()

Generate Mesh connectivity report

Generates a connectivity report between the Bitbucket node(s) and the Mesh node(s).

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_mesh_connectivity_report import RestMeshConnectivityReport
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Generate Mesh connectivity report
        api_response = api_instance.connectivity()
        print("The response of SystemMaintenanceApi->connectivity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->connectivity: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestMeshConnectivityReport**](RestMeshConnectivityReport.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The connectivity report between the Bitbucket node(s) and Mesh node(s). |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_hook_script**
> RestHookScript create_hook_script(content=content, description=description, name=name, type=type)

Create a new hook script

Create a new hook script.

This endpoint requires **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_hook_script import RestHookScript
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    content = 'content_example' # str | The hook script contents. (optional)
    description = 'description_example' # str | A description of the hook script (useful when querying registered hook scripts). (optional)
    name = 'name_example' # str | The name of the hook script (useful when querying registered hook scripts). (optional)
    type = 'type_example' # str | The type of hook script; supported values are \\\"PRE\\\" for pre-receive hooks and \\\"POST\\\" for post-receive hooks. (optional)

    try:
        # Create a new hook script
        api_response = api_instance.create_hook_script(content=content, description=description, name=name, type=type)
        print("The response of SystemMaintenanceApi->create_hook_script:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->create_hook_script: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **content** | **str**| The hook script contents. | [optional] 
 **description** | **str**| A description of the hook script (useful when querying registered hook scripts). | [optional] 
 **name** | **str**| The name of the hook script (useful when querying registered hook scripts). | [optional] 
 **type** | **str**| The type of hook script; supported values are \\\&quot;PRE\\\&quot; for pre-receive hooks and \\\&quot;POST\\\&quot; for post-receive hooks. | [optional] 

### Return type

[**RestHookScript**](RestHookScript.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly created hook script. |  -  |
**400** | The hook script was not created due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete2**
> delete2(id, force=force)

Delete Mesh node

Delete a Mesh node

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    id = 56 # int | 
    force = False # bool |  (optional) (default to False)

    try:
        # Delete Mesh node
        api_instance.delete2(id, force=force)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->delete2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **int**|  | 
 **force** | **bool**|  | [optional] [default to False]

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**0** | default response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete8**
> delete8(user_slug)

Delete user specific rate limit settings

Deletes the user-specific rate limit settings for the given user.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.

    try:
        # Delete user specific rate limit settings
        api_instance.delete8(user_slug)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->delete8: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the user settings have been deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve rate limit settings. |  -  |
**404** | The specified user does not exist, or has no settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_avatar**
> RestNamedLink delete_avatar(user_slug)

Delete user avatar

Delete the avatar associated to a user.


Users are always allowed to delete their own avatar. To delete someone else's avatar the authenticated user must
have global <strong>ADMIN</strong> permission, or global <strong>SYS_ADMIN</strong> permission to update a
<strong>SYS_ADMIN</strong> user's avatar.

### Example


```python
import bitbucketclient
from .models.rest_named_link import RestNamedLink
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug

    try:
        # Delete user avatar
        api_response = api_instance.delete_avatar(user_slug)
        print("The response of SystemMaintenanceApi->delete_avatar:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->delete_avatar: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug | 

### Return type

[**RestNamedLink**](RestNamedLink.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The new avatar URL if the local avatar was successfully deleted or did not exist |  -  |
**401** | The authenticated user has insufficient permissions to delete the specified avatar. |  -  |
**404** | The specified user does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_banner**
> delete_banner()

Delete announcement banner

Deletes a banner, if one is present in the database.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Delete announcement banner
        api_instance.delete_banner()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->delete_banner: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The query executed successfully, whether a banner was deleted or not |  -  |
**401** | The user does not have permission to access the banner service through this endpoint |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_hook_script**
> delete_hook_script(script_id)

Delete a hook script.

Deletes a registered hook script.

This endpoint requires **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    script_id = 'script_id_example' # str | The ID of the hook script to delete

    try:
        # Delete a hook script.
        api_instance.delete_hook_script(script_id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->delete_hook_script: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **script_id** | **str**| The ID of the hook script to delete | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The hook script was deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions. |  -  |
**404** | Unable to find the supplied hook script ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_mail_config**
> delete_mail_config()

Delete mail configuration

Deletes the current mail configuration.

The authenticated user must have the <strong>SYS_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Delete mail configuration
        api_instance.delete_mail_config()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->delete_mail_config: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The mail configuration was successfully deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the mail server configuration. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dismiss_retention_config_review_notification**
> dismiss_retention_config_review_notification()

Dismiss retention config notification

Dismisses the retention config review notification displayed by the audit plugin for the user that's currently logged in.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Dismiss retention config notification
        api_instance.dismiss_retention_config_review_notification()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->dismiss_retention_config_review_notification: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A blank response |  -  |
**401** | The currently authenticated user has insufficient permissions to dismiss the notification. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get2**
> RestBitbucketLicense get2()

Get license details

Retrieves details about the current license, as well as the current status of the system with regards to the installed license. The status includes the current number of users applied toward the license limit, as well as any status messages about the license (warnings about expiry or user counts exceeding license limits). 

The authenticated user must have <b>ADMIN</b> permission. Unauthenticated users, and non-administrators, are not permitted to access license details.

### Example


```python
import bitbucketclient
from .models.rest_bitbucket_license import RestBitbucketLicense
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get license details
        api_response = api_instance.get2()
        print("The response of SystemMaintenanceApi->get2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get2: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestBitbucketLicense**](RestBitbucketLicense.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The currently-installed license. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the license, or the request is anonymous. |  -  |
**404** | No license has been installed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get6**
> RestUserRateLimitSettings get6(user_slug)

Get user specific rate limit settings

Retrieves the user-specific rate limit settings for the given user.

To call this resource, the user must be authenticated and either have <strong>ADMIN</strong> permission or be the same user as the one whose settings are requested. A user with <strong>ADMIN</strong> permission cannot get the settings of a user with <strong>SYS_ADMIN</strong> permission.

### Example


```python
import bitbucketclient
from .models.rest_user_rate_limit_settings import RestUserRateLimitSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.

    try:
        # Get user specific rate limit settings
        api_response = api_instance.get6(user_slug)
        print("The response of SystemMaintenanceApi->get6:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get6: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 

### Return type

[**RestUserRateLimitSettings**](RestUserRateLimitSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the user-specific rate limit settings for the given user. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve rate limit settings. |  -  |
**404** | The specified user does not exist, or has no settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_active_mesh_migration_summary**
> RestMeshMigrationSummary get_active_mesh_migration_summary()

Get summary for Mesh migration job

Gets the summary, including the queue status and progress, of the currently active Mesh migration job.

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_mesh_migration_summary import RestMeshMigrationSummary
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get summary for Mesh migration job
        api_response = api_instance.get_active_mesh_migration_summary()
        print("The response of SystemMaintenanceApi->get_active_mesh_migration_summary:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_active_mesh_migration_summary: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestMeshMigrationSummary**](RestMeshMigrationSummary.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The summary of the currently active migration job. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | No active migration job found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_mesh_migration_summaries**
> GetAllMeshMigrationSummaries200Response get_all_mesh_migration_summaries(start=start, limit=limit)

Get all Mesh migration job summaries

Retrieve a page of Mesh migration job summaries. Jobs are ordered by when they were started, newest first. 

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_all_mesh_migration_summaries200_response import GetAllMeshMigrationSummaries200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get all Mesh migration job summaries
        api_response = api_instance.get_all_mesh_migration_summaries(start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_all_mesh_migration_summaries:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_all_mesh_migration_summaries: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetAllMeshMigrationSummaries200Response**](GetAllMeshMigrationSummaries200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The summary of the migration job. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_rate_limit_settings**
> GetAllRateLimitSettings200Response get_all_rate_limit_settings(filter=filter, start=start, limit=limit)

Get rate limit settings for user

Retrieves the user-specific rate limit settings for the given user.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_all_rate_limit_settings200_response import GetAllRateLimitSettings200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    filter = 'filter_example' # str | Optional filter (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get rate limit settings for user
        api_response = api_instance.get_all_rate_limit_settings(filter=filter, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_all_rate_limit_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_all_rate_limit_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **filter** | **str**| Optional filter | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetAllRateLimitSettings200Response**](GetAllRateLimitSettings200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing all the user-specific rate limit settings filtered by the optional filter. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve rate limit settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_registered_mesh_nodes**
> RestMeshNode get_all_registered_mesh_nodes()

Get all registered Mesh nodes

Get all the registered Mesh nodes.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_mesh_node import RestMeshNode
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get all registered Mesh nodes
        api_response = api_instance.get_all_registered_mesh_nodes()
        print("The response of SystemMaintenanceApi->get_all_registered_mesh_nodes:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_all_registered_mesh_nodes: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestMeshNode**](RestMeshNode.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The list of registered Mesh nodes. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_application_properties**
> RestApplicationProperties get_application_properties()

Get application properties

Retrieve version information and other application properties.

No authentication is required to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_application_properties import RestApplicationProperties
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get application properties
        api_response = api_instance.get_application_properties()
        print("The response of SystemMaintenanceApi->get_application_properties:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_application_properties: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestApplicationProperties**](RestApplicationProperties.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The application properties |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_banner**
> RestAnnouncementBanner get_banner()

Get announcement banner

Gets the announcement banner, if one exists and is available to the user

### Example


```python
import bitbucketclient
from .models.rest_announcement_banner import RestAnnouncementBanner
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get announcement banner
        api_response = api_instance.get_banner()
        print("The response of SystemMaintenanceApi->get_banner:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_banner: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestAnnouncementBanner**](RestAnnouncementBanner.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The requested banner |  -  |
**204** | There is no banner to display |  -  |
**401** | The user does not have permission to access the banner service through this endpoint |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_control_plane_public_key**
> get_control_plane_public_key()

Get the control plane PEM

Obtain the control plane PEM.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get the control plane PEM
        api_instance.get_control_plane_public_key()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_control_plane_public_key: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The control plane PEM. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_default_branch**
> get_default_branch()

Get the default branch

Retrieves the configured global default branch, which is used when creating new repositories if an explicit default branch is not specified.

The user must be authenticated to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get the default branch
        api_instance.get_default_branch()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_default_branch: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The configured global default branch. |  -  |
**404** | No global default branch has been configured. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_export_job**
> RestJob get_export_job(job_id)

Get export job details

Gets the details, including the current status and progress, of the export job identified by the given ID.

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_job import RestJob
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | the ID of the job

    try:
        # Get export job details
        api_response = api_instance.get_export_job(job_id)
        print("The response of SystemMaintenanceApi->get_export_job:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_export_job: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| the ID of the job | 

### Return type

[**RestJob**](RestJob.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The job, including status and progress information. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve information about this job. |  -  |
**404** | The specified job does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_export_job_messages**
> GetExportJobMessages200Response get_export_job_messages(job_id, severity=severity, subject=subject, start=start, limit=limit)

Get job messages

Gets the messages generated by the job.

Without any filter, all messages will be returned, but the response can optionally be filtered for the following severities. The severity parameter can be repeated to include multiple severities in one response.

- INFO
- WARN
- ERROR

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.get_export_job_messages200_response import GetExportJobMessages200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job
    severity = 'severity_example' # str | The severity to include in the results (optional)
    subject = 'subject_example' # str | The subject (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get job messages
        api_response = api_instance.get_export_job_messages(job_id, severity=severity, subject=subject, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_export_job_messages:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_export_job_messages: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job | 
 **severity** | **str**| The severity to include in the results | [optional] 
 **subject** | **str**| The subject | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetExportJobMessages200Response**](GetExportJobMessages200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The messages generated by this job. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve information about this job. |  -  |
**404** | The specified job does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_global_settings**
> RestSshKeySettings get_global_settings()

Get global SSH key settings

Gets the global settings that enforce the maximum expiry of SSH keys and restrictions on SSH key types.

### Example


```python
import bitbucketclient
from .models.rest_ssh_key_settings import RestSshKeySettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get global SSH key settings
        api_response = api_instance.get_global_settings()
        print("The response of SystemMaintenanceApi->get_global_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_global_settings: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestSshKeySettings**](RestSshKeySettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The global ssh key settings configuration. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the ssh keys global settings configuration. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_history**
> GetHistory200Response get_history(order=order, start=start, limit=limit)

Get rate limit history

Retrieves the recent rate limit history for the instance.

The authenticated user must have the <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_history200_response import GetHistory200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    order = 'order_example' # str | An optional sort category to arrange the results in descending order (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get rate limit history
        api_response = api_instance.get_history(order=order, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_history:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_history: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **order** | **str**| An optional sort category to arrange the results in descending order | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetHistory200Response**](GetHistory200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing a page of aggregated counters for users who have been recently rate limited. |  -  |
**400** | The sort query parameter is invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve rate limit history. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_hook_script**
> RestHookScript get_hook_script(script_id)

Get a hook script

Retrieves a hook script by ID.

### Example


```python
import bitbucketclient
from .models.rest_hook_script import RestHookScript
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    script_id = 'script_id_example' # str | The ID of the hook script to retrieve

    try:
        # Get a hook script
        api_response = api_instance.get_hook_script(script_id)
        print("The response of SystemMaintenanceApi->get_hook_script:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_hook_script: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **script_id** | **str**| The ID of the hook script to retrieve | 

### Return type

[**RestHookScript**](RestHookScript.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The hook script. |  -  |
**401** | The currently authenticated user has insufficient permissions. |  -  |
**404** | The hook script ID supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_import_job**
> RestJob get_import_job(job_id)

Get import job status

Gets the details, including the current status and progress, of the import job identified by the given ID.

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_job import RestJob
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job

    try:
        # Get import job status
        api_response = api_instance.get_import_job(job_id)
        print("The response of SystemMaintenanceApi->get_import_job:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_import_job: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job | 

### Return type

[**RestJob**](RestJob.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The job, including status and progress information. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve information about this job. |  -  |
**404** | The specified job does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_import_job_messages**
> GetExportJobMessages200Response get_import_job_messages(job_id, severity=severity, subject=subject, start=start, limit=limit)

Get import job messages

Gets the messages generated by the job.

Without any filter, all messages will be returned, but the response can optionally be filtered for the following severities. The severity parameter can be repeated to include multiple severities in one response.

- INFO
- WARN
- ERROR

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.get_export_job_messages200_response import GetExportJobMessages200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job
    severity = 'severity_example' # str | The severity to include in the results (optional)
    subject = 'subject_example' # str | The subject (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get import job messages
        api_response = api_instance.get_import_job_messages(job_id, severity=severity, subject=subject, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_import_job_messages:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_import_job_messages: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job | 
 **severity** | **str**| The severity to include in the results | [optional] 
 **subject** | **str**| The subject | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetExportJobMessages200Response**](GetExportJobMessages200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The messages generated by this job. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve information about this job. |  -  |
**404** | The specified job does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_information**
> RestClusterInformation get_information()

Get cluster node information

Gets information about the nodes that currently make up the stash cluster.

The authenticated user must have the <strong>SYS_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_cluster_information import RestClusterInformation
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get cluster node information
        api_response = api_instance.get_information()
        print("The response of SystemMaintenanceApi->get_information:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_information: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestClusterInformation**](RestClusterInformation.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing information about the cluster |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the cluster information. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_label**
> RestLabel get_label(label_name)

Get label

Returns a label.

The user needs to be authenticated to use this resource.

### Example


```python
import bitbucketclient
from .models.rest_label import RestLabel
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    label_name = 'label_name_example' # str | the label name

    try:
        # Get label
        api_response = api_instance.get_label(label_name)
        print("The response of SystemMaintenanceApi->get_label:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_label: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **label_name** | **str**| the label name | 

### Return type

[**RestLabel**](RestLabel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The label. |  -  |
**401** | The user is currently not authenticated. |  -  |
**404** | The specified label does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_labelables**
> GetLabelables200Response get_labelables(label_name, type=type, start=start, limit=limit)

Get labelables for label

Returns a page of labelables for a given label.

Only labelables that the authenticated user has view access to will be returned.

### Example


```python
import bitbucketclient
from .models.get_labelables200_response import GetLabelables200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    label_name = 'label_name_example' # str | The page of labelables.
    type = 'type_example' # str |  the type of labelables to be returned. Supported values: REPOSITORY (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get labelables for label
        api_response = api_instance.get_labelables(label_name, type=type, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_labelables:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_labelables: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **label_name** | **str**| The page of labelables. | 
 **type** | **str**|  the type of labelables to be returned. Supported values: REPOSITORY | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetLabelables200Response**](GetLabelables200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The page of labelables. |  -  |
**400** | The type of labelable is incorrect, correct values are REPOSITORY. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the labelables |  -  |
**404** | The specified label does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_labels**
> GetLabels200Response get_labels(prefix=prefix, start=start, limit=limit)

Get all labels

Returns a paged response of all the labels in the system.

The user needs to be authenticated to use this resource.

### Example


```python
import bitbucketclient
from .models.get_labels200_response import GetLabels200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    prefix = 'prefix_example' # str | (optional) prefix to filter the labels on. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get all labels
        api_response = api_instance.get_labels(prefix=prefix, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_labels:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_labels: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **prefix** | **str**| (optional) prefix to filter the labels on. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetLabels200Response**](GetLabels200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Page of returned labels. |  -  |
**401** | The user is currently not authenticated. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_level**
> RestLogLevel get_level(logger_name)

Get current log level

Retrieve the current log level for a given logger.

The authenticated user must have <strong>SYS_ADMIN</strong> permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_log_level import RestLogLevel
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    logger_name = 'logger_name_example' # str | The name of the logger.

    try:
        # Get current log level
        api_response = api_instance.get_level(logger_name)
        print("The response of SystemMaintenanceApi->get_level:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_level: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **logger_name** | **str**| The name of the logger. | 

### Return type

[**RestLogLevel**](RestLogLevel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The log level of the logger. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the log level. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mail_config**
> RestMailConfiguration get_mail_config()

Get mail configuration

Retrieves the current mail configuration. 

The authenticated user must have the <strong>SYS_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_mail_configuration import RestMailConfiguration
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get mail configuration
        api_response = api_instance.get_mail_config()
        print("The response of SystemMaintenanceApi->get_mail_config:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_mail_config: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestMailConfiguration**](RestMailConfiguration.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The mail configuration |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the mail configuration. |  -  |
**404** | The mail server hasn&#39;t been configured |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mesh_migration_job**
> get_mesh_migration_job(job_id)

Get Mesh migration job details

Gets the details, including the current status and progress, of the job identified by the given ID.

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job

    try:
        # Get Mesh migration job details
        api_instance.get_mesh_migration_job(job_id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_mesh_migration_job: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The details of the migration job. |  -  |
**400** | The job ID parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | The specified job ID does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mesh_migration_job_messages**
> GetExportJobMessages200Response get_mesh_migration_job_messages(job_id, severity=severity, subject=subject, start=start, limit=limit)

Get Mesh migration job messages

Gets the messages generated by the job. 

Without any filter, all messages will be returned, but the response can optionally be filtered for the following severities. The severity parameter can be repeated to include multiple severities in one response. 

     - INFO
     - WARN
     - ERROR


The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_export_job_messages200_response import GetExportJobMessages200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job
    severity = 'severity_example' # str | The severity to include in the results (optional)
    subject = 'subject_example' # str | The subject (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get Mesh migration job messages
        api_response = api_instance.get_mesh_migration_job_messages(job_id, severity=severity, subject=subject, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->get_mesh_migration_job_messages:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_mesh_migration_job_messages: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job | 
 **severity** | **str**| The severity to include in the results | [optional] 
 **subject** | **str**| The subject | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetExportJobMessages200Response**](GetExportJobMessages200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The details of the migration job. |  -  |
**400** | The job ID parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | The specified job ID does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mesh_migration_job_summary**
> RestMeshMigrationSummary get_mesh_migration_job_summary(job_id)

Get Mesh migration job summary

Gets the summary, including the queue status and progress, of a Mesh migration job. 

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_mesh_migration_summary import RestMeshMigrationSummary
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    job_id = 'job_id_example' # str | The ID of the job

    try:
        # Get Mesh migration job summary
        api_response = api_instance.get_mesh_migration_job_summary(job_id)
        print("The response of SystemMaintenanceApi->get_mesh_migration_job_summary:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_mesh_migration_job_summary: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **job_id** | **str**| The ID of the job | 

### Return type

[**RestMeshMigrationSummary**](RestMeshMigrationSummary.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The summary of the migration job. |  -  |
**400** | The job ID parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | The specified job ID does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_registered_mesh_node_by_id**
> RestMeshNode get_registered_mesh_node_by_id(id)

Get Mesh node

Get the registered Mesh node that matches the supplied ID.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_mesh_node import RestMeshNode
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    id = 'id_example' # str | The ID of the Mesh node.

    try:
        # Get Mesh node
        api_response = api_instance.get_registered_mesh_node_by_id(id)
        print("The response of SystemMaintenanceApi->get_registered_mesh_node_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_registered_mesh_node_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**| The ID of the Mesh node. | 

### Return type

[**RestMeshNode**](RestMeshNode.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The Mesh node that matches the ID. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | The Mesh node does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_archive_policy**
> RestRepositoryPolicy get_repository_archive_policy()

Get repository archive policy

Retrieves the repository archive policy for the instance.

The user must be authenticated to access this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository_policy import RestRepositoryPolicy
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get repository archive policy
        api_response = api_instance.get_repository_archive_policy()
        print("The response of SystemMaintenanceApi->get_repository_archive_policy:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_repository_archive_policy: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestRepositoryPolicy**](RestRepositoryPolicy.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the repository archive policy for the instance |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the repository archive policy |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_delete_policy**
> RestRepositoryPolicy get_repository_delete_policy()

Get repository delete policy

Retrieves the repository delete policy for the instance.

The user must be authenticated to access this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository_policy import RestRepositoryPolicy
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get repository delete policy
        api_response = api_instance.get_repository_delete_policy()
        print("The response of SystemMaintenanceApi->get_repository_delete_policy:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_repository_delete_policy: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestRepositoryPolicy**](RestRepositoryPolicy.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the repository delete policy for the instance |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the repository delete policy |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_root_level**
> RestLogLevel get_root_level()

Get root log level

Retrieve the current log level for the root logger.

The authenticated user must have <strong>SYS_ADMIN</strong> permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_log_level import RestLogLevel
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get root log level
        api_response = api_instance.get_root_level()
        print("The response of SystemMaintenanceApi->get_root_level:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_root_level: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestLogLevel**](RestLogLevel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The log level of the logger. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the log level. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_sender_address**
> get_sender_address()

Get server mail address

Retrieves the server email address

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get server mail address
        api_instance.get_sender_address()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_sender_address: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The server email address |  -  |
**401** | he currently authenticated user has insufficient permissions to retrieve the server email address. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_settings2**
> RestLoggingSettings get_settings2()

Get debug logging and profiling

Returns whether debug logging and profiling are enabled.

The authenticated user must have <strong>SYS_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_logging_settings import RestLoggingSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get debug logging and profiling
        api_response = api_instance.get_settings2()
        print("The response of SystemMaintenanceApi->get_settings2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_settings2: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestLoggingSettings**](RestLoggingSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Whether debug logging and profiling are enabled. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve these settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_settings3**
> RestRateLimitSettings get_settings3()

Get rate limit settings

Retrieves the rate limit settings for the instance.

The user must be authenticated to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_rate_limit_settings import RestRateLimitSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get rate limit settings
        api_response = api_instance.get_settings3()
        print("The response of SystemMaintenanceApi->get_settings3:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_settings3: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestRateLimitSettings**](RestRateLimitSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the rate limit plugin settings for the instance. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve rate limit settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_support_zip**
> get_support_zip(id)

Get support zip for node

Get the support zip for the Mesh node that matches the specified ID.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    id = 'id_example' # str | The ID of the Mesh node.

    try:
        # Get support zip for node
        api_instance.get_support_zip(id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_support_zip: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**| The ID of the Mesh node. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/octet-stream, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The support zip for the Mesh node that matches the ID. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | The Mesh node does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_support_zips**
> get_support_zips()

Get support zips for all Mesh nodes

Get the support zips for all the Mesh nodes.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get support zips for all Mesh nodes
        api_instance.get_support_zips()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_support_zips: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/octet-stream, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The support zips for all the Mesh nodes. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_supported_key_types**
> get_supported_key_types()

Get supported SSH key algorithms and lengths

Retrieves a list of all supported SSH key algorithms and lengths.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)

    try:
        # Get supported SSH key algorithms and lengths
        api_instance.get_supported_key_types()
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_supported_key_types: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A list of supported SSH key algorithms and lengths. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve this list. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_user**
> RestApplicationUser get_user(user_slug)

Get user

Retrieve the user matching the supplied <strong>userSlug</strong>.

### Example


```python
import bitbucketclient
from .models.rest_application_user import RestApplicationUser
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug

    try:
        # Get user
        api_response = api_instance.get_user(user_slug)
        print("The response of SystemMaintenanceApi->get_user:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug | 

### Return type

[**RestApplicationUser**](RestApplicationUser.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The user matching the supplied &lt;strong&gt;userSlug&lt;/strong&gt;. Note, this may &lt;i&gt;not&lt;/i&gt; be the user&#39;s username, always use the &lt;strong&gt;user.slug&lt;/strong&gt; property. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the user. |  -  |
**404** | The specified user does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_user_settings**
> ExampleSettingsMap get_user_settings(user_slug)

Get user settings

Retrieve a map of user setting key values for a specific user identified by the user slug.

### Example


```python
import bitbucketclient
from .models.example_settings_map import ExampleSettingsMap
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.

    try:
        # Get user settings
        api_response = api_instance.get_user_settings(user_slug)
        print("The response of SystemMaintenanceApi->get_user_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_user_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 

### Return type

[**ExampleSettingsMap**](ExampleSettingsMap.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The user settings for the specified user slug. |  -  |
**401** | The currently authenticated user does not have permission to view the settings of this user. |  -  |
**404** | The specified project, repository, commit, or report does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_users2**
> RestApplicationUser get_users2(filter=filter, permission_n=permission_n, permission=permission, group=group)

Get all users

Retrieve a page of users, optionally run through provided filters.


Only authenticated users may call this resource.
### Permission Filters


The following three sub-sections list parameters supported for permission filters (where <code>[root]</code> is
the root permission filter name, e.g. <code>permission</code>, <code>permission.1</code> etc.) depending on the
permission resource. The system determines which filter to apply (Global, Project or Repository permission)
based on the `[root]` permission value. E.g. <code>ADMIN</code> is a global permission,
<code>PROJECT_ADMIN</code> is a project permission and <code>REPO_ADMIN</code> is a repository permission. Note
that the parameters for a given resource will be looked up in the order as they are listed below, that is e.g.
for a project resource, if both <code>projectId</code> and <code>projectKey</code> are provided, the system will
use <code>projectId</code> for the lookup.
<h4>Global permissions</h4>


The permission value under <code>[root]</code> is the only required and recognized parameter, as global
permissions do not apply to a specific resource.


Example valid filter: <code>permission=ADMIN</code>.
<h4>Project permissions</h4>


- <code>[root]</code>- specifies the project permission
- <code>[root].projectId</code> - specifies the project ID to lookup the project by
- <code>[root].projectKey</code> - specifies the project key to lookup the project by


Example valid filter: <code>permission.1=PROJECT_ADMIN&amp;permission.1.projectKey=TEST_PROJECT</code>.
#### Repository permissions


- <code>[root]</code>- specifies the repository permission
- <code>[root].projectId</code> - specifies the repository ID to lookup the repository by
- <code>[root].projectKey</code> and <code>[root].repositorySlug</code>- specifies the project key and     repository slug to lookup the repository by; both values <i>need to</i> be provided for this look up to be     triggered


Example valid filter: <code>permission.2=REPO_ADMIN&amp;permission.2.projectKey=TEST_PROJECT&amp;permission.2.repositorySlug=test_repo</code>.

### Example


```python
import bitbucketclient
from .models.rest_application_user import RestApplicationUser
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    filter = 'filter_example' # str | Return only users, whose username, name or email address <i>contain</i> the <code> filter</code> value (optional)
    permission_n = 'permission_n_example' # str | The \"root\" of a single permission filter, similar to the <code>permission</code> parameter, where \"N\" is a natural number starting from 1. This allows clients to specify multiple permission filters, by providing consecutive filters as <code>permission.1</code>, <code>permission.2</code> etc. Note that the filters numbering has to start with 1 and be continuous for all filters to be processed. The total allowed number of permission filters is 50 and all filters exceeding that limit will be dropped. See the section \"Permission Filters\" above for more details on how the permission filters are processed. (optional)
    permission = 'permission_example' # str | The \"root\" of a permission filter, whose value must be a valid global, project, or repository permission. Additional filter parameters referring to this filter that specify the resource (project or repository) to apply the filter to must be prefixed with <code>permission.</code>. See the section \"Permission Filters\" above for more details. (optional)
    group = 'group_example' # str | return only users who are members of the given group (optional)

    try:
        # Get all users
        api_response = api_instance.get_users2(filter=filter, permission_n=permission_n, permission=permission, group=group)
        print("The response of SystemMaintenanceApi->get_users2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->get_users2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **filter** | **str**| Return only users, whose username, name or email address &lt;i&gt;contain&lt;/i&gt; the &lt;code&gt; filter&lt;/code&gt; value | [optional] 
 **permission_n** | **str**| The \&quot;root\&quot; of a single permission filter, similar to the &lt;code&gt;permission&lt;/code&gt; parameter, where \&quot;N\&quot; is a natural number starting from 1. This allows clients to specify multiple permission filters, by providing consecutive filters as &lt;code&gt;permission.1&lt;/code&gt;, &lt;code&gt;permission.2&lt;/code&gt; etc. Note that the filters numbering has to start with 1 and be continuous for all filters to be processed. The total allowed number of permission filters is 50 and all filters exceeding that limit will be dropped. See the section \&quot;Permission Filters\&quot; above for more details on how the permission filters are processed. | [optional] 
 **permission** | **str**| The \&quot;root\&quot; of a permission filter, whose value must be a valid global, project, or repository permission. Additional filter parameters referring to this filter that specify the resource (project or repository) to apply the filter to must be prefixed with &lt;code&gt;permission.&lt;/code&gt;. See the section \&quot;Permission Filters\&quot; above for more details. | [optional] 
 **group** | **str**| return only users who are members of the given group | [optional] 

### Return type

[**RestApplicationUser**](RestApplicationUser.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of users. |  -  |
**400** | The search request was invalid, which may happen for multiple reasons, among others:   - permission filter for project/repository permission with no parameters specifying the project or     repository to apply the filter to - invalid permission name - permission filter for a project/repository permission pointing to a non-existent project or repository   The exact reason for the error and - in most cases - the request parameter name that had invalid value - will be provided in the error message. |  -  |
**401** | Authentication failed or was not attempted. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **preview_export**
> RestScopesExample preview_export(rest_export_request=rest_export_request)

Preview export

Enumerates the projects and repositories that would be exported for a given export request.

All affected repositories will be enumerated explicitly, and while projects are listed as individual items in responses from this endpoint, their presence does not imply that all their repositories are included.

While this endpoint can be used to verify that all selectors in the request apply as intended, it should be noted that a subsequent, actual export might contain a different set of repositories, as they might have been added or deleted in the meantime.

Note that the overall response from this endpoint can become very large when a lot of repositories end up in the selection. This is why the server is streaming the response while it is being generated (as opposed to creating it in memory and then sending it all at once) and it can be consumed in a streaming way, too.

Also, due to the potential size of the response, projects and repositories are listed with fewer details than in other REST responses.

For a more detailed description of selectors, see the endpoint documentation for starting an export.

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_export_request import RestExportRequest
from .models.rest_scopes_example import RestScopesExample
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_export_request = .RestExportRequest() # RestExportRequest | the export request (optional)

    try:
        # Preview export
        api_response = api_instance.preview_export(rest_export_request=rest_export_request)
        print("The response of SystemMaintenanceApi->preview_export:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->preview_export: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_export_request** | [**RestExportRequest**](RestExportRequest.md)| the export request | [optional] 

### Return type

[**RestScopesExample**](RestScopesExample.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The effectively selected projects and repositories. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to generate a preview. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **preview_mesh_migration**
> ExamplePreviewMigration preview_mesh_migration(rest_mesh_migration_request=rest_mesh_migration_request)

Preview Mesh migration

Enumerates the projects and repositories that would be migrated for a given request.

All affected repositories will be enumerated explicitly, and while projects are listed as individual items in responses from this endpoint, their presence does not imply that all their repositories are included.

While this endpoint can be used to verify that all selectors in the request apply as intended, it should be noted that a subsequent, actual export might contain a different set of repositories, as they might have been added or deleted in the meantime.

Note that the overall response from this endpoint can become very large when a lot of repositories end up in the selection. This is why the server is streaming the response while it is being generated (as opposed to creating it in memory and then sending it all at once) and it can be consumed in a streaming way, too.

Also, due to the potential size of the response, projects and repositories are listed with fewer details than in other REST responses.

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.example_preview_migration import ExamplePreviewMigration
from .models.rest_mesh_migration_request import RestMeshMigrationRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_mesh_migration_request = .RestMeshMigrationRequest() # RestMeshMigrationRequest | The export request (optional)

    try:
        # Preview Mesh migration
        api_response = api_instance.preview_mesh_migration(rest_mesh_migration_request=rest_mesh_migration_request)
        print("The response of SystemMaintenanceApi->preview_mesh_migration:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->preview_mesh_migration: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_mesh_migration_request** | [**RestMeshMigrationRequest**](RestMeshMigrationRequest.md)| The export request | [optional] 

### Return type

[**ExamplePreviewMigration**](ExamplePreviewMigration.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Enumeration of projects and repositories that would be migrated for a given request. |  -  |
**400** | The request was invalid or missing. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **read**
> read(script_id)

Get hook script content

Retrieves the hook script content.

This endpoint requires **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    script_id = 'script_id_example' # str | The ID of the hook script

    try:
        # Get hook script content
        api_instance.read(script_id)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->read: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **script_id** | **str**| The ID of the hook script | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/octet-stream, text/plain;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The hook script content. |  -  |
**401** | The currently authenticated user has insufficient permissions. |  -  |
**404** | The hook script ID supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **register_new_mesh_node**
> RestMeshNode register_new_mesh_node(rest_mesh_node=rest_mesh_node)

Register new Mesh node

Register a new Mesh node.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_mesh_node import RestMeshNode
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_mesh_node = .RestMeshNode() # RestMeshNode | The request specifying the new Mesh node. (optional)

    try:
        # Register new Mesh node
        api_response = api_instance.register_new_mesh_node(rest_mesh_node=rest_mesh_node)
        print("The response of SystemMaintenanceApi->register_new_mesh_node:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->register_new_mesh_node: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_mesh_node** | [**RestMeshNode**](RestMeshNode.md)| The request specifying the new Mesh node. | [optional] 

### Return type

[**RestMeshNode**](RestMeshNode.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly registered Mesh node. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **search_mesh_migration_repos**
> SearchMeshMigrationRepos200Response search_mesh_migration_repos(migration_id=migration_id, project_key=project_key, name=name, state=state, remote=remote, start=start, limit=limit)

Find repositories by Mesh migration state

Searches for repositories in the system matching the specified criteria and enriches their MeshMigrationQueueState migration state if a migration is currently in progress. 

The currently active migration can optionally be specified by passing a migrationId, if known. If this isn't passed, an attempt is made to locate the active migration and its ID is used. 

If a migration is currently active, only repositories that are a part of the migration are filtered and returned. Otherwise, all repositories in the systems are filtered and returned. 

Filtering by state is ignored when no migration is currently in progress. In such a case, results are not enriched with their MeshMigrationQueueState migration state. 

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.search_mesh_migration_repos200_response import SearchMeshMigrationRepos200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    migration_id = 'migration_id_example' # str | (optional) The currently active migration job. If not passed, this is looked up internally. (optional)
    project_key = 'project_key_example' # str | (optional) The project key. Can be specified more than once to filter by more than one project. (optional)
    name = 'name_example' # str | (optional) The repository name (optional)
    state = 'state_example' # str | (optional) If a migration is active, the MeshMigrationQueueState state to filter results by. Can be specified more than once to filter by more than one state. (optional)
    remote = 'remote_example' # str | (optional) Whether the repository has been fully migrated to Mesh. If not present, all repositories are considered regardless of where they're located. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Find repositories by Mesh migration state
        api_response = api_instance.search_mesh_migration_repos(migration_id=migration_id, project_key=project_key, name=name, state=state, remote=remote, start=start, limit=limit)
        print("The response of SystemMaintenanceApi->search_mesh_migration_repos:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->search_mesh_migration_repos: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **migration_id** | **str**| (optional) The currently active migration job. If not passed, this is looked up internally. | [optional] 
 **project_key** | **str**| (optional) The project key. Can be specified more than once to filter by more than one project. | [optional] 
 **name** | **str**| (optional) The repository name | [optional] 
 **state** | **str**| (optional) If a migration is active, the MeshMigrationQueueState state to filter results by. Can be specified more than once to filter by more than one state. | [optional] 
 **remote** | **str**| (optional) Whether the repository has been fully migrated to Mesh. If not present, all repositories are considered regardless of where they&#39;re located. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**SearchMeshMigrationRepos200Response**](SearchMeshMigrationRepos200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of repositories matching the specified criteria. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**404** | No migration job with the given ID exists. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set2**
> RestUserRateLimitSettings set2(rest_bulk_user_rate_limit_settings_update_request=rest_bulk_user_rate_limit_settings_update_request)

Set rate limit settings for users

Sets the given rate limit settings for the given users.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_bulk_user_rate_limit_settings_update_request import RestBulkUserRateLimitSettingsUpdateRequest
from .models.rest_user_rate_limit_settings import RestUserRateLimitSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_bulk_user_rate_limit_settings_update_request = .RestBulkUserRateLimitSettingsUpdateRequest() # RestBulkUserRateLimitSettingsUpdateRequest |  (optional)

    try:
        # Set rate limit settings for users
        api_response = api_instance.set2(rest_bulk_user_rate_limit_settings_update_request=rest_bulk_user_rate_limit_settings_update_request)
        print("The response of SystemMaintenanceApi->set2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_bulk_user_rate_limit_settings_update_request** | [**RestBulkUserRateLimitSettingsUpdateRequest**](RestBulkUserRateLimitSettingsUpdateRequest.md)|  | [optional] 

### Return type

[**RestUserRateLimitSettings**](RestUserRateLimitSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the updated user settings. |  -  |
**400** | One of the following valid state error cases occurred (check the error message for more details):  - The request is empty - No users are provided in the request - One or more of the users are invalid - Whitelisted is false or not provided, and no settings are provided either - Whitelisted is false or not provided, settings are provided,   but do not contain both capacity and fillRate - Whitelisted is false or not provided, settings are provided,   but capacity and fillRate are not positive integers - Whitelisted is true, and settings are provided (only one must be provided)  |  -  |
**401** | The currently authenticated user has insufficient permissions to set user settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set3**
> RestUserRateLimitSettings set3(user_slug, rest_user_rate_limit_settings_update_request=rest_user_rate_limit_settings_update_request)

Set rate limit settings for user

Sets the given rate limit settings for the given user.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_user_rate_limit_settings import RestUserRateLimitSettings
from .models.rest_user_rate_limit_settings_update_request import RestUserRateLimitSettingsUpdateRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.
    rest_user_rate_limit_settings_update_request = .RestUserRateLimitSettingsUpdateRequest() # RestUserRateLimitSettingsUpdateRequest |  (optional)

    try:
        # Set rate limit settings for user
        api_response = api_instance.set3(user_slug, rest_user_rate_limit_settings_update_request=rest_user_rate_limit_settings_update_request)
        print("The response of SystemMaintenanceApi->set3:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set3: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 
 **rest_user_rate_limit_settings_update_request** | [**RestUserRateLimitSettingsUpdateRequest**](RestUserRateLimitSettingsUpdateRequest.md)|  | [optional] 

### Return type

[**RestUserRateLimitSettings**](RestUserRateLimitSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the updated user settings |  -  |
**400** | One of the following valid state error cases occurred (check the error message for more details):  - The request is empty - Whitelisted is false or not provided, and no settings are provided either - Whitelisted is false or not provided, settings are provided,   but do not contain both capacity and fillRate - Whitelisted is false or not provided, settings are provided,   but capacity and fillRate are not positive integers - Whitelisted is true, and settings are provided (only one must be provided)    |  -  |
**401** | The currently authenticated user has insufficient permissions to set user settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_banner**
> set_banner(set_banner_request=set_banner_request)

Update/Set announcement banner

Sets the announcement banner with the provided JSON.
Only users authenticated as Admins may call this resource

### Example


```python
import bitbucketclient
from .models.set_banner_request import SetBannerRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    set_banner_request = .SetBannerRequest() # SetBannerRequest |  (optional)

    try:
        # Update/Set announcement banner
        api_instance.set_banner(set_banner_request=set_banner_request)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_banner: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **set_banner_request** | [**SetBannerRequest**](SetBannerRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The banner was set successfully |  -  |
**400** | There was malformed or incorrect data in the provided JSON |  -  |
**401** | The user does not have permission to access the banner service through this endpoint |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_default_branch**
> set_default_branch(set_default_branch_request=set_default_branch_request)

Update/Set default branch

Configures the global default branch, which is used when creating new repositories if an explicit default branch is not specified.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.set_default_branch_request import SetDefaultBranchRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    set_default_branch_request = .SetDefaultBranchRequest() # SetDefaultBranchRequest |  (optional)

    try:
        # Update/Set default branch
        api_instance.set_default_branch(set_default_branch_request=set_default_branch_request)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_default_branch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **set_default_branch_request** | [**SetDefaultBranchRequest**](SetDefaultBranchRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default branch has been set. |  -  |
**401** | The current user does not have sufficient permissions to configure the global default branch. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_level**
> set_level(level_name, logger_name)

Set log level

Set the current log level for a given logger.

The authenticated user must have <strong>SYS_ADMIN</strong> permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    level_name = 'level_name_example' # str | The level to set the logger to. Either TRACE, DEBUG, INFO, WARN or ERROR
    logger_name = 'logger_name_example' # str | The name of the logger.

    try:
        # Set log level
        api_instance.set_level(level_name, logger_name)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_level: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **level_name** | **str**| The level to set the logger to. Either TRACE, DEBUG, INFO, WARN or ERROR | 
 **logger_name** | **str**| The name of the logger. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The log level was successfully changed. |  -  |
**400** | The log level was invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to set the log level. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_mail_config**
> RestMailConfiguration set_mail_config(set_mail_config_request=set_mail_config_request)

Update mail configuration

Updates the mail configuration. 

The authenticated user must have the <strong>SYS_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_mail_configuration import RestMailConfiguration
from .models.set_mail_config_request import SetMailConfigRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    set_mail_config_request = .SetMailConfigRequest() # SetMailConfigRequest |  (optional)

    try:
        # Update mail configuration
        api_response = api_instance.set_mail_config(set_mail_config_request=set_mail_config_request)
        print("The response of SystemMaintenanceApi->set_mail_config:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_mail_config: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **set_mail_config_request** | [**SetMailConfigRequest**](SetMailConfigRequest.md)|  | [optional] 

### Return type

[**RestMailConfiguration**](RestMailConfiguration.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated mail configuration. |  -  |
**400** | The mail configuration was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to update themail configuration. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_repository_archive_policy**
> RestRepositoryPolicy set_repository_archive_policy(rest_repository_policy=rest_repository_policy)

Update repository archive policy

Sets the repository archive policy for the instance.

The authenticated user must have <b>SYS_ADMIN</b> permission.

### Example


```python
import bitbucketclient
from .models.rest_repository_policy import RestRepositoryPolicy
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_repository_policy = .RestRepositoryPolicy() # RestRepositoryPolicy | The request containing the details of the policy. (optional)

    try:
        # Update repository archive policy
        api_response = api_instance.set_repository_archive_policy(rest_repository_policy=rest_repository_policy)
        print("The response of SystemMaintenanceApi->set_repository_archive_policy:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_repository_archive_policy: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_repository_policy** | [**RestRepositoryPolicy**](RestRepositoryPolicy.md)| The request containing the details of the policy. | [optional] 

### Return type

[**RestRepositoryPolicy**](RestRepositoryPolicy.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the repository archive policy for the instance |  -  |
**400** | The permission was invalid or does not exist |  -  |
**401** | The currently authenticated user has insufficient permissions to set the repository archive policy |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_repository_delete_policy**
> RestRepositoryPolicy set_repository_delete_policy(rest_repository_policy=rest_repository_policy)

Update the repository delete policy

Sets the repository delete policy for the instance.

The authenticated user must have <b>SYS_ADMIN</b> permission.

### Example


```python
import bitbucketclient
from .models.rest_repository_policy import RestRepositoryPolicy
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_repository_policy = .RestRepositoryPolicy() # RestRepositoryPolicy | The request containing the details of the policy. (optional)

    try:
        # Update the repository delete policy
        api_response = api_instance.set_repository_delete_policy(rest_repository_policy=rest_repository_policy)
        print("The response of SystemMaintenanceApi->set_repository_delete_policy:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_repository_delete_policy: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_repository_policy** | [**RestRepositoryPolicy**](RestRepositoryPolicy.md)| The request containing the details of the policy. | [optional] 

### Return type

[**RestRepositoryPolicy**](RestRepositoryPolicy.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the repository delete policy for the instance |  -  |
**400** | The permission was invalid or does not exist |  -  |
**401** | The currently authenticated user has insufficient permissions to set the repository delete policy |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_root_level**
> set_root_level(level_name)

Set root log level

Set the current log level for the root logger.

The authenticated user must have <strong>SYS_ADMIN</strong> permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    level_name = 'level_name_example' # str | the level to set the logger to. Either TRACE, DEBUG, INFO, WARN or ERROR

    try:
        # Set root log level
        api_instance.set_root_level(level_name)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_root_level: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **level_name** | **str**| the level to set the logger to. Either TRACE, DEBUG, INFO, WARN or ERROR | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The log level was successfully changed. |  -  |
**400** | The log level was invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to set the log level. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_sender_address**
> set_sender_address(body=body)

Update server mail address

Updates the server email address 

The authenticated user must have the <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    body = 'body_example' # str |  (optional)

    try:
        # Update server mail address
        api_instance.set_sender_address(body=body)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_sender_address: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | **str**|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The from address used in notification emails |  -  |
**400** | The server email address was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the server email address. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_settings2**
> RestLoggingSettings set_settings2(set_settings2_request=set_settings2_request)

Set debug logging and profiling

Set whether debug logging and profiling should be enabled. This setting persists on restarts.

The authenticated user must have <strong>SYS_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_logging_settings import RestLoggingSettings
from .models.set_settings2_request import SetSettings2Request
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    set_settings2_request = .SetSettings2Request() # SetSettings2Request |  (optional)

    try:
        # Set debug logging and profiling
        api_response = api_instance.set_settings2(set_settings2_request=set_settings2_request)
        print("The response of SystemMaintenanceApi->set_settings2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_settings2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **set_settings2_request** | [**SetSettings2Request**](SetSettings2Request.md)|  | [optional] 

### Return type

[**RestLoggingSettings**](RestLoggingSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Debug logging and profiling were successfully updated. |  -  |
**400** | The settings were not correctly specified. |  -  |
**401** | The currently authenticated user has insufficient permissions to update these settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_settings3**
> RestRateLimitSettings set_settings3(rest_rate_limit_settings=rest_rate_limit_settings)

Set rate limit

Sets the rate limit settings for the instance.

The authenticated user must have <strong>ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_rate_limit_settings import RestRateLimitSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_rate_limit_settings = .RestRateLimitSettings() # RestRateLimitSettings | Sets the rate limit settings for the instance.  The authenticated user must have <strong>ADMIN</strong> permission to call this resource. (optional)

    try:
        # Set rate limit
        api_response = api_instance.set_settings3(rest_rate_limit_settings=rest_rate_limit_settings)
        print("The response of SystemMaintenanceApi->set_settings3:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->set_settings3: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_rate_limit_settings** | [**RestRateLimitSettings**](RestRateLimitSettings.md)| Sets the rate limit settings for the instance.  The authenticated user must have &lt;strong&gt;ADMIN&lt;/strong&gt; permission to call this resource. | [optional] 

### Return type

[**RestRateLimitSettings**](RestRateLimitSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the updated rate limit plugin settings for the instance. |  -  |
**400** | One of the following error cases occurred (check the error message for more details):  - The request is empty - The enabled field of the request is not a boolean - The defaultSettings field of the request does not contain both capacity and fillRate - The capacity and fillRate are not positive integers    |  -  |
**401** | The currently authenticated user has insufficient permissions to set rate limit settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_export**
> RestJob start_export(rest_export_request=rest_export_request)

Start export job

Starts a background job that exports the selected repositories.

Only 2 concurrent exports are supported _per cluster node_. If a request ends up on a node that is already running that many export jobs, the request will be rejected and an error returned.

The response includes a description of the job that has been started, and its ID can be used to query these details again, including the current progress, warnings and errors that occurred while processing the job, and to interrupt and cancel the execution of this job.

The request to start an export is similar to the one for previewing an export. Additionally, it accepts an optional parameter, `exportLocation`, which can be used to specify a _relative_ path within `data/migration/export` in the shared home directory. No locations outside of that directory will be accepted for exports.

There are essentially three ways to select repositories for export. Regardless of which you use, a few general rules apply:

- You can supply a list of selectors. The selection will be additive.
- Repositories that are selected more than once due to overlapping selectors will be de-duplicated and effectively exported only once.
- For every selected repository, its full fork hierarchy will be considered selected, even if parts of that hierarchy would otherwise not be matched by the provided selectors. For example, when you explicitly select a single repository only, but that repository is a fork, then its origin will be exported (and eventually imported), too.

Now, a single repository can be selected like this:

```



{
      "projectKey": "PRJ",
      "slug": "my-repo"
}

```

Second, all repositories in a specific project can be selected like this:

```



{
      "projectKey": "PRJ",
      "slug": *"
}

```

And third, all projects and repositories in the system would be selected like this:

```



{
      "projectKey": "*",
      "slug": *"
}

```

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_export_request import RestExportRequest
from .models.rest_job import RestJob
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_export_request = .RestExportRequest() # RestExportRequest | The request (optional)

    try:
        # Start export job
        api_response = api_instance.start_export(rest_export_request=rest_export_request)
        print("The response of SystemMaintenanceApi->start_export:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->start_export: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_export_request** | [**RestExportRequest**](RestExportRequest.md)| The request | [optional] 

### Return type

[**RestJob**](RestJob.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details about the export job. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to start anexport |  -  |
**503** | The export could not be started because the limit of concurrent migration jobs has been reached. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_import**
> RestJob start_import(rest_import_request=rest_import_request)

Start import job

Starts a background job that imports the specified archive.

Only 1 import at a time is supported _per cluster_. If another request is made while an import is already running, the request will be rejected and an error returned.

The path in the request must point to a valid archive file. The file must be located within the `data/migration/import` directory in the shared home directory.

The authenticated user must have **ADMIN** permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_import_request import RestImportRequest
from .models.rest_job import RestJob
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_import_request = .RestImportRequest() # RestImportRequest | The request (optional)

    try:
        # Start import job
        api_response = api_instance.start_import(rest_import_request=rest_import_request)
        print("The response of SystemMaintenanceApi->start_import:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->start_import: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_import_request** | [**RestImportRequest**](RestImportRequest.md)| The request | [optional] 

### Return type

[**RestJob**](RestJob.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Details about the export job. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to start an import. |  -  |
**503** | The import could not be started because the limit of concurrent migration jobs has been reached. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_mesh_migration**
> RestJob start_mesh_migration(start_mesh_migration_request=start_mesh_migration_request)

Start Mesh migration job

Starts a background job that migrates selected projects/repositories to Mesh. 

Only 1 job is supported _per cluster_.

The response includes a description of the job that has been started, and its ID can be used to query these details again, including the current progress, and to interrupt and cancel the execution of this job. 

The request to start a migration is similar to the one for previewing a migration. 

There are essentially three ways to select repositories for migration. Regardless of which you use, a few general rules apply: 

    - You can supply a list of repository IDs and project IDs. The selection will be additive. All repositories     in the system are migrated if both lists are empty.     - Repositories that are selected more than once due to overlapping IDs will be de-duplicated and     effectively migrated only once.     - For every selected repository, its full fork hierarchy will be considered selected, even if parts of that     hierarchy would otherwise not be matched by the provided IDs. For example, when you explicitly     select a single repository only, but that repository is a fork, then its origin will be migrated too. 

Now, a single repository can be selected like this: 

```

     {
     "repositoryIds": [1]
     }
```

Multiple repositories can be selected like this:



```

     {
     "repositoryIds": [1, 2]
     }
```

Second, all repositories in a specific project can be selected like this:



```

     {
     "projectIds": [1]
     }
```

And third, all projects and repositories in the system would be selected like this:



```

     {
     "projectIds": [],
     "repositoryIds": []
     }
```

The authenticated user must have **SYS_ADMIN** permission to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_job import RestJob
from .models.start_mesh_migration_request import StartMeshMigrationRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    start_mesh_migration_request = .StartMeshMigrationRequest() # StartMeshMigrationRequest |  (optional)

    try:
        # Start Mesh migration job
        api_response = api_instance.start_mesh_migration(start_mesh_migration_request=start_mesh_migration_request)
        print("The response of SystemMaintenanceApi->start_mesh_migration:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->start_mesh_migration: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **start_mesh_migration_request** | [**StartMeshMigrationRequest**](StartMeshMigrationRequest.md)|  | [optional] 

### Return type

[**RestJob**](RestJob.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The started job |  -  |
**400** | The migration request failed one/more validation checks. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |
**503** | A migration job is already in progress |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_global_settings**
> update_global_settings(rest_ssh_key_settings=rest_ssh_key_settings)

Update global SSH key settings

Updates the global settings that enforces the maximum expiry of SSH keys and restrictions on SSH key types.

### Example


```python
import bitbucketclient
from .models.rest_ssh_key_settings import RestSshKeySettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_ssh_key_settings = .RestSshKeySettings() # RestSshKeySettings | A request containing expiry length to be set for SSH keys and a list of SSH key type restrictions. (optional)

    try:
        # Update global SSH key settings
        api_instance.update_global_settings(rest_ssh_key_settings=rest_ssh_key_settings)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_global_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_ssh_key_settings** | [**RestSshKeySettings**](RestSshKeySettings.md)| A request containing expiry length to be set for SSH keys and a list of SSH key type restrictions. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The ssh key global settings were updated. |  -  |
**400** | The request was invalid, which may be due to:   - attempted to set expiry to less than 1 day - attempted to set expiry using partial days - attempted to set a restriction on a key type which was invalid   The exact reason for the error will be provided in the error message. |  -  |
**401** | The currently authenticated user has insufficient permissions to update these settings. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_hook_script**
> RestHookScript update_hook_script(script_id, example_put_multipart_form_data=example_put_multipart_form_data)

Update a hook script

Updates a hook script.

This endpoint requires **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.example_put_multipart_form_data import ExamplePutMultipartFormData
from .models.rest_hook_script import RestHookScript
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    script_id = 'script_id_example' # str | The ID of the hook script
    example_put_multipart_form_data = .ExamplePutMultipartFormData() # ExamplePutMultipartFormData | The multipart form data containing the hook script (optional)

    try:
        # Update a hook script
        api_response = api_instance.update_hook_script(script_id, example_put_multipart_form_data=example_put_multipart_form_data)
        print("The response of SystemMaintenanceApi->update_hook_script:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_hook_script: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **script_id** | **str**| The ID of the hook script | 
 **example_put_multipart_form_data** | [**ExamplePutMultipartFormData**](ExamplePutMultipartFormData.md)| The multipart form data containing the hook script | [optional] 

### Return type

[**RestHookScript**](RestHookScript.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated hook script. |  -  |
**401** | The currently authenticated user has insufficient permissions. |  -  |
**404** | The hook script ID supplied does not exist. |  -  |
**409** | A hook script with the same name already exists. |  -  |
**422** | One or more fields to update must be specified: content, description and/or name. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_license**
> RestBitbucketLicense update_license(rest_bitbucket_license=rest_bitbucket_license)

Update license

Decodes the provided encoded license and sets it as the active license. If no license was provided, a 400 is returned. If the license cannot be decoded, or cannot be applied, a 409 is returned. Some possible reasons a license may not be applied include: 

- It is for a different product
- It is already expired


Otherwise, if the license is updated successfully, details for the new license are returned with a 200 response.

<b>Warning</b>: It is possible to downgrade the license during update, applying a license with a lower number of permitted users. If the number of currently-licensed users exceeds the limits of the new license, pushing will be disabled until the licensed user count is brought into compliance with the new license.

The authenticated user must have <b>SYS_ADMIN</b> permission. <b>ADMIN</b> users may <i>view</i> the current license details, but they may not <i>update</i> the license.

### Example


```python
import bitbucketclient
from .models.rest_bitbucket_license import RestBitbucketLicense
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    rest_bitbucket_license = .RestBitbucketLicense() # RestBitbucketLicense | a JSON payload containing the encoded license to apply (optional)

    try:
        # Update license
        api_response = api_instance.update_license(rest_bitbucket_license=rest_bitbucket_license)
        print("The response of SystemMaintenanceApi->update_license:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_license: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_bitbucket_license** | [**RestBitbucketLicense**](RestBitbucketLicense.md)| a JSON payload containing the encoded license to apply | [optional] 

### Return type

[**RestBitbucketLicense**](RestBitbucketLicense.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly-installed license. |  -  |
**400** | No encoded license was provided in the JSON body for the POST. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the license. |  -  |
**409** | The encoded license could not be decoded, or it is not valid for use on this server. Some possible reasons a license may not be applied include: it may be for a different product, it may have already expired, or this Bitbucket version doesn&#39;t support Server licenses. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_mesh_node**
> RestMeshNode update_mesh_node(id, rest_mesh_node=rest_mesh_node)

Update Mesh node

Update a Mesh node.

The authenticated user must have **SYS_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_mesh_node import RestMeshNode
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    id = 'id_example' # str | The ID of the Mesh node to update.
    rest_mesh_node = .RestMeshNode() # RestMeshNode | The request specifying the updated Mesh node. (optional)

    try:
        # Update Mesh node
        api_response = api_instance.update_mesh_node(id, rest_mesh_node=rest_mesh_node)
        print("The response of SystemMaintenanceApi->update_mesh_node:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_mesh_node: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**| The ID of the Mesh node to update. | 
 **rest_mesh_node** | [**RestMeshNode**](RestMeshNode.md)| The request specifying the updated Mesh node. | [optional] 

### Return type

[**RestMeshNode**](RestMeshNode.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated Mesh node. |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_settings**
> update_settings(user_slug, example_settings_map=example_settings_map)

Update user settings

Update the entries of a map of user setting key/values for a specific user identified by the user slug.

### Example


```python
import bitbucketclient
from .models.example_settings_map import ExampleSettingsMap
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.
    example_settings_map = .ExampleSettingsMap() # ExampleSettingsMap | A map with the UserSettings entries which must be updated. (optional)

    try:
        # Update user settings
        api_instance.update_settings(user_slug, example_settings_map=example_settings_map)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 
 **example_settings_map** | [**ExampleSettingsMap**](ExampleSettingsMap.md)| A map with the UserSettings entries which must be updated. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The UserSettings were updated successfully |  -  |
**401** | The currently authenticated user is not a project administrator. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_user_details1**
> RestApplicationUser update_user_details1(user_update_with_credentials=user_update_with_credentials)

Update user details

Update the currently authenticated user's details. The update will always be applied to the currently authenticated user.

### Example


```python
import bitbucketclient
from .models.rest_application_user import RestApplicationUser
from .models.user_update_with_credentials import UserUpdateWithCredentials
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_update_with_credentials = .UserUpdateWithCredentials() # UserUpdateWithCredentials | The user update details (optional)

    try:
        # Update user details
        api_response = api_instance.update_user_details1(user_update_with_credentials=user_update_with_credentials)
        print("The response of SystemMaintenanceApi->update_user_details1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_user_details1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_update_with_credentials** | [**UserUpdateWithCredentials**](UserUpdateWithCredentials.md)| The user update details | [optional] 

### Return type

[**RestApplicationUser**](RestApplicationUser.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated user. |  -  |
**400** | The request was malformed. |  -  |
**401** | Authentication failed or was not attempted. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_user_password1**
> update_user_password1(user_password_update=user_password_update)

Set password

Update the currently authenticated user's password.

### Example


```python
import bitbucketclient
from .models.user_password_update import UserPasswordUpdate
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_password_update = .UserPasswordUpdate() # UserPasswordUpdate | The password update details (optional)

    try:
        # Set password
        api_instance.update_user_password1(user_password_update=user_password_update)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->update_user_password1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_password_update** | [**UserPasswordUpdate**](UserPasswordUpdate.md)| The password update details | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user&#39;s password was successfully updated. |  -  |
**400** | The request was malformed or the old password was incorrect. |  -  |
**401** | Authentication failed or was not attempted. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **upload_avatar1**
> upload_avatar1(user_slug, x_atlassian_token=x_atlassian_token, avatar=avatar)

Update user avatar

Update the avatar for the user with the supplied <strong>slug</strong>.


This resource accepts POST multipart form data, containing a single image in a form-field named 'avatar'.


There are configurable server limits on both the dimensions (1024x1024 pixels by default) and uploaded
file size (1MB by default). Several different image formats are supported, but <strong>PNG</strong> and
<strong>JPEG</strong> are preferred due to the file size limit.


This resource has Cross-Site Request Forgery (XSRF) protection. To allow the request to
pass the XSRF check the caller needs to send an <code>X-Atlassian-Token</code> HTTP header with the
value <code>no-check</code>.


An example <a href="http://curl.haxx.se/">curl</a> request to upload an image name 'avatar.png' would be:
```
curl -X POST -u username:password -H "X-Atlassian-Token: no-check" http://example.com/rest/api/latest/users/jdoe/avatar.png -F avatar=@avatar.png
```


Users are always allowed to update their own avatar. To update someone else's avatar the authenticated user must
have global <strong>ADMIN</strong> permission, or global <strong>SYS_ADMIN</strong> permission to update a
<strong>SYS_ADMIN</strong> user's avatar.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .SystemMaintenanceApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug
    x_atlassian_token = 'no-check' # str | This resource has Cross-Site Request Forgery (XSRF) protection. To allow the request to pass the XSRF check the caller needs to send an <code>X-Atlassian-Token</code> HTTP header with the value <code>no-check</code>. (optional)
    avatar = None # bytearray | The avatar file to upload. (optional)

    try:
        # Update user avatar
        api_instance.upload_avatar1(user_slug, x_atlassian_token=x_atlassian_token, avatar=avatar)
    except Exception as e:
        print("Exception when calling SystemMaintenanceApi->upload_avatar1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug | 
 **x_atlassian_token** | **str**| This resource has Cross-Site Request Forgery (XSRF) protection. To allow the request to pass the XSRF check the caller needs to send an &lt;code&gt;X-Atlassian-Token&lt;/code&gt; HTTP header with the value &lt;code&gt;no-check&lt;/code&gt;. | [optional] 
 **avatar** | **bytearray**| The avatar file to upload. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The avatar was uploaded successfully. |  * Location - The Location response header which indicates the URL of the avatar. <br>  |
**401** | The currently authenticated user has insufficient permissions to update the avatar. |  -  |
**404** | The specified user does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

