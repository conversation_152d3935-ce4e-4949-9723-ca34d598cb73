# RestSyncProgress


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**discovering** | **bool** |  | [optional] 
**synced_repos** | **int** |  | [optional] 
**total_repos** | **int** |  | [optional] 

## Example

```python
from .models.rest_sync_progress import RestSyncProgress

# TODO update the JSON string below
json = "{}"
# create an instance of RestSyncProgress from a JSON string
rest_sync_progress_instance = RestSyncProgress.from_json(json)
# print the JSON string representation of the object
print(RestSyncProgress.to_json())

# convert the object into a dict
rest_sync_progress_dict = rest_sync_progress_instance.to_dict()
# create an instance of RestSyncProgress from a dict
rest_sync_progress_from_dict = RestSyncProgress.from_dict(rest_sync_progress_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


