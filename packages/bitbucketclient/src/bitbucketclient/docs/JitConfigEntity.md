# JitConfigEntity


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**additional_openid_scopes** | **List[str]** |  | [optional] 
**mapping_display_name** | **str** |  | [optional] 
**mapping_email** | **str** |  | [optional] 
**mapping_groups** | **str** |  | [optional] 
**user_provisioning_enabled** | **bool** |  | [optional] 

## Example

```python
from .models.jit_config_entity import JitConfigEntity

# TODO update the JSON string below
json = "{}"
# create an instance of JitConfigEntity from a JSON string
jit_config_entity_instance = JitConfigEntity.from_json(json)
# print the JSON string representation of the object
print(JitConfigEntity.to_json())

# convert the object into a dict
jit_config_entity_dict = jit_config_entity_instance.to_dict()
# create an instance of JitConfigEntity from a dict
jit_config_entity_from_dict = JitConfigEntity.from_dict(jit_config_entity_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


