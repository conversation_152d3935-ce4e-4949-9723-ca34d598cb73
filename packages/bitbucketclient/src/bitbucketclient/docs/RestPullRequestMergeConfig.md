# RestPullRequestMergeConfig


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**commit_message_template** | [**RestPullRequestMergeConfigCommitMessageTemplate**](RestPullRequestMergeConfigCommitMessageTemplate.md) |  | [optional] 
**commit_summaries** | **int** |  | [optional] 
**default_strategy** | [**RestPullRequestMergeConfigDefaultStrategy**](RestPullRequestMergeConfigDefaultStrategy.md) |  | [optional] 
**strategies** | [**List[RestPullRequestMergeStrategy]**](RestPullRequestMergeStrategy.md) |  | [optional] 
**type** | **str** |  | [optional] [readonly] 

## Example

```python
from .models.rest_pull_request_merge_config import RestPullRequestMergeConfig

# TODO update the JSON string below
json = "{}"
# create an instance of RestPullRequestMergeConfig from a JSON string
rest_pull_request_merge_config_instance = RestPullRequestMergeConfig.from_json(json)
# print the JSON string representation of the object
print(RestPullRequestMergeConfig.to_json())

# convert the object into a dict
rest_pull_request_merge_config_dict = rest_pull_request_merge_config_instance.to_dict()
# create an instance of RestPullRequestMergeConfig from a dict
rest_pull_request_merge_config_from_dict = RestPullRequestMergeConfig.from_dict(rest_pull_request_merge_config_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


