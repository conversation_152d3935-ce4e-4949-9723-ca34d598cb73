# RestCommentAuthor


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**active** | **bool** |  | [optional] 
**avatar_url** | **str** |  | [optional] 
**display_name** | **str** |  | [optional] 
**email_address** | **str** |  | [optional] 
**id** | **int** |  | [optional] [readonly] 
**links** | **object** |  | [optional] 
**name** | **str** |  | [optional] 
**slug** | **str** |  | [optional] 
**type** | **str** |  | [optional] 

## Example

```python
from .models.rest_comment_author import RestCommentAuthor

# TODO update the JSON string below
json = "{}"
# create an instance of RestCommentAuthor from a JSON string
rest_comment_author_instance = RestCommentAuthor.from_json(json)
# print the JSON string representation of the object
print(RestCommentAuthor.to_json())

# convert the object into a dict
rest_comment_author_dict = rest_comment_author_instance.to_dict()
# create an instance of RestCommentAuthor from a dict
rest_comment_author_from_dict = RestCommentAuthor.from_dict(rest_comment_author_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


