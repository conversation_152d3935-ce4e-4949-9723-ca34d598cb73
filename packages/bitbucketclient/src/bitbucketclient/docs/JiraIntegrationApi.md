# .JiraIntegrationApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**create_issue**](JiraIntegrationApi.md#create_issue) | **POST** /jira/latest/comments/{commentId}/issues | Create Jira Issue
[**get_commits_by_issue_key**](JiraIntegrationApi.md#get_commits_by_issue_key) | **GET** /jira/latest/issues/{issueKey}/commits | Get changesets for issue key
[**get_enhanced_entity_link_for_project**](JiraIntegrationApi.md#get_enhanced_entity_link_for_project) | **GET** /jira/latest/projects/{projectKey}/primary-enhanced-entitylink | Get entity link
[**get_issue_keys_for_pull_request**](JiraIntegrationApi.md#get_issue_keys_for_pull_request) | **GET** /jira/latest/projects/{projectKey}/repos/{repositorySlug}/pull-requests/{pullRequestId}/issues | Get issues for a pull request


# **create_issue**
> RestCommentJiraIssue create_issue(comment_id, application_id=application_id, body=body)

Create Jira Issue

Create a Jira issue and associate it with a comment on a pull request.

This resource can only be used with comments on a pull request. Attempting to call this resource with a different type of comment (for example, a comment on a commit) will result in an error. 

 The authenticated user must have <strong>REPO_READ</strong> permission for the repository containing the comment to call this resource.

The JSON structure for the create issue format is specified by Jira's REST v2 API.

### Example


```python
import bitbucketclient
from .models.rest_comment_jira_issue import RestCommentJiraIssue
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .JiraIntegrationApi(api_client)
    comment_id = 'comment_id_example' # str | the comment to associate the created Jira issue to
    application_id = 'application_id_example' # str | id of the Jira server (optional)
    body = 'body_example' # str | A String representation of the JSON format Jira create issue request see: <a href=\"https://docs.atlassian.com/jira/REST/server/#api/2/issue-createIssue\">Jira REST API</a> (optional)

    try:
        # Create Jira Issue
        api_response = api_instance.create_issue(comment_id, application_id=application_id, body=body)
        print("The response of JiraIntegrationApi->create_issue:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling JiraIntegrationApi->create_issue: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **comment_id** | **str**| the comment to associate the created Jira issue to | 
 **application_id** | **str**| id of the Jira server | [optional] 
 **body** | **str**| A String representation of the JSON format Jira create issue request see: &lt;a href&#x3D;\&quot;https://docs.atlassian.com/jira/REST/server/#api/2/issue-createIssue\&quot;&gt;Jira REST API&lt;/a&gt; | [optional] 

### Return type

[**RestCommentJiraIssue**](RestCommentJiraIssue.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The created Jira issue key and the associated comment ID |  -  |
**400** | The specified application link ID does not match any linked Jira instance. |  -  |
**401** | Authentication with the Jira instance is required. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_commits_by_issue_key**
> GetCommitsByIssueKey200Response get_commits_by_issue_key(issue_key, max_changes=max_changes, start=start, limit=limit)

Get changesets for issue key

Retrieve a page of changesets associated with the given issue key.

### Example


```python
import bitbucketclient
from .models.get_commits_by_issue_key200_response import GetCommitsByIssueKey200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .JiraIntegrationApi(api_client)
    issue_key = 'issue_key_example' # str | The issue key to search by
    max_changes = 'max_changes_example' # str | The maximum number of changes to retrieve for each changeset (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get changesets for issue key
        api_response = api_instance.get_commits_by_issue_key(issue_key, max_changes=max_changes, start=start, limit=limit)
        print("The response of JiraIntegrationApi->get_commits_by_issue_key:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling JiraIntegrationApi->get_commits_by_issue_key: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **issue_key** | **str**| The issue key to search by | 
 **max_changes** | **str**| The maximum number of changes to retrieve for each changeset | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetCommitsByIssueKey200Response**](GetCommitsByIssueKey200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of detailed changesets |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_enhanced_entity_link_for_project**
> RestEnhancedEntityLink get_enhanced_entity_link_for_project(project_key)

Get entity link

Retrieves the enchanced primary entitylink 

The authenticated user must have <strong>PROJECT_READ</strong> permission for the project having the primary enhanced entitylink. 



### Example


```python
import bitbucketclient
from .models.rest_enhanced_entity_link import RestEnhancedEntityLink
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .JiraIntegrationApi(api_client)
    project_key = 'project_key_example' # str | The project key

    try:
        # Get entity link
        api_response = api_instance.get_enhanced_entity_link_for_project(project_key)
        print("The response of JiraIntegrationApi->get_enhanced_entity_link_for_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling JiraIntegrationApi->get_enhanced_entity_link_for_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 

### Return type

[**RestEnhancedEntityLink**](RestEnhancedEntityLink.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The primary enhanced entitylink. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_issue_keys_for_pull_request**
> List[RestJiraIssue] get_issue_keys_for_pull_request(project_key, pull_request_id, repository_slug)

Get issues for a pull request

Retrieves Jira issue keys that are associated with the commits in the specified pull request. The number of commits checked for issues is limited to a default of 100.

### Example


```python
import bitbucketclient
from .models.rest_jira_issue import RestJiraIssue
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .JiraIntegrationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    pull_request_id = 'pull_request_id_example' # str | The pull request id
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get issues for a pull request
        api_response = api_instance.get_issue_keys_for_pull_request(project_key, pull_request_id, repository_slug)
        print("The response of JiraIntegrationApi->get_issue_keys_for_pull_request:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling JiraIntegrationApi->get_issue_keys_for_pull_request: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **pull_request_id** | **str**| The pull request id | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**List[RestJiraIssue]**](RestJiraIssue.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A list of Jira issues keys for the pull request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

