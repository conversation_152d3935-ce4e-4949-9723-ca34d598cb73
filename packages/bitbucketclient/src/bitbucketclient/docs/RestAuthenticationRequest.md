# RestAuthenticationRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**credentials** | [**Credentials**](Credentials.md) |  | 
**repository_id** | **int** |  | [optional] 

## Example

```python
from .models.rest_authentication_request import RestAuthenticationRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RestAuthenticationRequest from a JSON string
rest_authentication_request_instance = RestAuthenticationRequest.from_json(json)
# print the JSON string representation of the object
print(RestAuthenticationRequest.to_json())

# convert the object into a dict
rest_authentication_request_dict = rest_authentication_request_instance.to_dict()
# create an instance of RestAuthenticationRequest from a dict
rest_authentication_request_from_dict = RestAuthenticationRequest.from_dict(rest_authentication_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


