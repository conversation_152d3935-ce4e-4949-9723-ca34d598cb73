# RestLoggingSettings


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**debug_logging_enabled** | **bool** |  | [optional] 
**profiling_enabled** | **bool** |  | [optional] 

## Example

```python
from .models.rest_logging_settings import RestLoggingSettings

# TODO update the JSON string below
json = "{}"
# create an instance of RestLoggingSettings from a JSON string
rest_logging_settings_instance = RestLoggingSettings.from_json(json)
# print the JSON string representation of the object
print(RestLoggingSettings.to_json())

# convert the object into a dict
rest_logging_settings_dict = rest_logging_settings_instance.to_dict()
# create an instance of RestLoggingSettings from a dict
rest_logging_settings_from_dict = RestLoggingSettings.from_dict(rest_logging_settings_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


