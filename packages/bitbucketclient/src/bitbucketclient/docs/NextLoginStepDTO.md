# NextLoginStepDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**conversation_id** | **str** |  | [optional] 
**next_login_step** | **str** |  | [optional] 

## Example

```python
from .models.next_login_step_dto import NextLoginStepDTO

# TODO update the JSON string below
json = "{}"
# create an instance of NextLoginStepDTO from a JSON string
next_login_step_dto_instance = NextLoginStepDTO.from_json(json)
# print the JSON string representation of the object
print(NextLoginStepDTO.to_json())

# convert the object into a dict
next_login_step_dto_dict = next_login_step_dto_instance.to_dict()
# create an instance of NextLoginStepDTO from a dict
next_login_step_dto_from_dict = NextLoginStepDTO.from_dict(next_login_step_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


