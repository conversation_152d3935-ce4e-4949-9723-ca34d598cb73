# RestDetailedInvocation


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**duration** | **int** |  | [optional] 
**event** | **str** |  | [optional] 
**event_scope** | [**RestDetailedInvocationEventScope**](RestDetailedInvocationEventScope.md) |  | [optional] 
**finish** | **int** |  | [optional] 
**id** | **int** |  | [optional] 
**request** | **object** |  | [optional] 
**result** | **object** |  | [optional] 
**start** | **int** |  | [optional] 

## Example

```python
from .models.rest_detailed_invocation import RestDetailedInvocation

# TODO update the JSON string below
json = "{}"
# create an instance of RestDetailedInvocation from a JSON string
rest_detailed_invocation_instance = RestDetailedInvocation.from_json(json)
# print the JSON string representation of the object
print(RestDetailedInvocation.to_json())

# convert the object into a dict
rest_detailed_invocation_dict = rest_detailed_invocation_instance.to_dict()
# create an instance of RestDetailedInvocation from a dict
rest_detailed_invocation_from_dict = RestDetailedInvocation.from_dict(rest_detailed_invocation_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


