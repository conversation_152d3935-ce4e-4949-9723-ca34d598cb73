# RestSecretScanningRule


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | The ID of the rule | [optional] 
**line_regex** | **str** | If present, regular expression for matching a secret on a code line | [optional] 
**name** | **str** | Human readable name for the rule | [optional] 
**path_regex** | **str** | If present, regular expression matching file names | [optional] 
**scope** | [**RestSecretScanningRuleScope**](RestSecretScanningRuleScope.md) |  | [optional] 

## Example

```python
from .models.rest_secret_scanning_rule import RestSecretScanningRule

# TODO update the JSON string below
json = "{}"
# create an instance of RestSecretScanningRule from a JSON string
rest_secret_scanning_rule_instance = RestSecretScanningRule.from_json(json)
# print the JSON string representation of the object
print(RestSecretScanningRule.to_json())

# convert the object into a dict
rest_secret_scanning_rule_dict = rest_secret_scanning_rule_instance.to_dict()
# create an instance of RestSecretScanningRule from a dict
rest_secret_scanning_rule_from_dict = RestSecretScanningRule.from_dict(rest_secret_scanning_rule_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


