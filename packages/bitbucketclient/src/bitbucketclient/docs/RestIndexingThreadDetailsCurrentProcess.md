# RestIndexingThreadDetailsCurrentProcess

A snapshot of the current process being executed by the indexing worker.

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**current_task** | **str** | The current task description that the indexing worker is executing. | 
**event** | [**RestIndexingProcessEvent**](RestIndexingProcessEvent.md) |  | 

## Example

```python
from .models.rest_indexing_thread_details_current_process import RestIndexingThreadDetailsCurrentProcess

# TODO update the JSON string below
json = "{}"
# create an instance of RestIndexingThreadDetailsCurrentProcess from a JSON string
rest_indexing_thread_details_current_process_instance = RestIndexingThreadDetailsCurrentProcess.from_json(json)
# print the JSON string representation of the object
print(RestIndexingThreadDetailsCurrentProcess.to_json())

# convert the object into a dict
rest_indexing_thread_details_current_process_dict = rest_indexing_thread_details_current_process_instance.to_dict()
# create an instance of RestIndexingThreadDetailsCurrentProcess from a dict
rest_indexing_thread_details_current_process_from_dict = RestIndexingThreadDetailsCurrentProcess.from_dict(rest_indexing_thread_details_current_process_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


