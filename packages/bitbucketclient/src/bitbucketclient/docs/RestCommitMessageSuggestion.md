# RestCommitMessageSuggestion


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**body** | **str** |  | [optional] 
**title** | **str** |  | [optional] 

## Example

```python
from .models.rest_commit_message_suggestion import RestCommitMessageSuggestion

# TODO update the JSON string below
json = "{}"
# create an instance of RestCommitMessageSuggestion from a JSON string
rest_commit_message_suggestion_instance = RestCommitMessageSuggestion.from_json(json)
# print the JSON string representation of the object
print(RestCommitMessageSuggestion.to_json())

# convert the object into a dict
rest_commit_message_suggestion_dict = rest_commit_message_suggestion_instance.to_dict()
# create an instance of RestCommitMessageSuggestion from a dict
rest_commit_message_suggestion_from_dict = RestCommitMessageSuggestion.from_dict(rest_commit_message_suggestion_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


