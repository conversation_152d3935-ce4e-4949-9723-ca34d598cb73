# RestSshAccessKeyLocations


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**projects** | [**RestProject**](RestProject.md) |  | [optional] 
**repositories** | [**RestRepository**](RestRepository.md) |  | [optional] 

## Example

```python
from .models.rest_ssh_access_key_locations import RestSshAccessKeyLocations

# TODO update the JSON string below
json = "{}"
# create an instance of RestSshAccessKeyLocations from a JSON string
rest_ssh_access_key_locations_instance = RestSshAccessKeyLocations.from_json(json)
# print the JSON string representation of the object
print(RestSshAccessKeyLocations.to_json())

# convert the object into a dict
rest_ssh_access_key_locations_dict = rest_ssh_access_key_locations_instance.to_dict()
# create an instance of RestSshAccessKeyLocations from a dict
rest_ssh_access_key_locations_from_dict = RestSshAccessKeyLocations.from_dict(rest_ssh_access_key_locations_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


