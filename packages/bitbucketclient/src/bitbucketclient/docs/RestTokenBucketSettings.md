# RestTokenBucketSettings


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**capacity** | **int** |  | [optional] 
**fill_rate** | **int** |  | [optional] 

## Example

```python
from .models.rest_token_bucket_settings import RestTokenBucketSettings

# TODO update the JSON string below
json = "{}"
# create an instance of RestTokenBucketSettings from a JSON string
rest_token_bucket_settings_instance = RestTokenBucketSettings.from_json(json)
# print the JSON string representation of the object
print(RestTokenBucketSettings.to_json())

# convert the object into a dict
rest_token_bucket_settings_dict = rest_token_bucket_settings_instance.to_dict()
# create an instance of RestTokenBucketSettings from a dict
rest_token_bucket_settings_from_dict = RestTokenBucketSettings.from_dict(rest_token_bucket_settings_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


