# PullRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**author** | [**PullRequestParticipant**](PullRequestParticipant.md) |  | [optional] 
**closed** | **bool** |  | [optional] 
**closed_date** | **datetime** |  | [optional] 
**created_date** | **datetime** |  | [optional] 
**cross_repository** | **bool** |  | [optional] 
**description** | **str** |  | [optional] 
**draft** | **bool** |  | [optional] 
**from_ref** | [**PullRequestRef**](PullRequestRef.md) |  | [optional] 
**id** | **int** |  | [optional] 
**locked** | **bool** |  | [optional] 
**open** | **bool** |  | [optional] 
**participants** | [**List[PullRequestParticipant]**](PullRequestParticipant.md) |  | [optional] 
**properties** | **object** |  | [optional] 
**reviewers** | [**List[PullRequestParticipant]**](PullRequestParticipant.md) |  | [optional] 
**state** | **str** |  | [optional] 
**title** | **str** |  | [optional] 
**to_ref** | [**PullRequestRef**](PullRequestRef.md) |  | [optional] 
**updated_date** | **datetime** |  | [optional] 
**version** | **int** |  | [optional] 

## Example

```python
from .models.pull_request import PullRequest

# TODO update the JSON string below
json = "{}"
# create an instance of PullRequest from a JSON string
pull_request_instance = PullRequest.from_json(json)
# print the JSON string representation of the object
print(PullRequest.to_json())

# convert the object into a dict
pull_request_dict = pull_request_instance.to_dict()
# create an instance of PullRequest from a dict
pull_request_from_dict = PullRequest.from_dict(pull_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


