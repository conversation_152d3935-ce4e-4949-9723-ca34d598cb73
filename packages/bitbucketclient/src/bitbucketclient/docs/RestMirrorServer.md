# RestMirrorServer


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**base_url** | **str** |  | [optional] 
**enabled** | **bool** |  | [optional] 
**id** | **str** |  | [optional] 
**last_seen_date** | **datetime** |  | [optional] 
**mirror_type** | **str** |  | [optional] 
**name** | **str** |  | [optional] 
**product_version** | **str** |  | [optional] 

## Example

```python
from .models.rest_mirror_server import RestMirrorServer

# TODO update the JSON string below
json = "{}"
# create an instance of RestMirrorServer from a JSON string
rest_mirror_server_instance = RestMirrorServer.from_json(json)
# print the JSON string representation of the object
print(RestMirrorServer.to_json())

# convert the object into a dict
rest_mirror_server_dict = rest_mirror_server_instance.to_dict()
# create an instance of RestMirrorServer from a dict
rest_mirror_server_from_dict = RestMirrorServer.from_dict(rest_mirror_server_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


