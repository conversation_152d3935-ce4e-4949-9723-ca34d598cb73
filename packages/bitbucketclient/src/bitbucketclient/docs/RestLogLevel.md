# RestLogLevel


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**log_level** | **str** |  | [optional] 

## Example

```python
from .models.rest_log_level import RestLogLevel

# TODO update the JSON string below
json = "{}"
# create an instance of RestLogLevel from a JSON string
rest_log_level_instance = RestLogLevel.from_json(json)
# print the JSON string representation of the object
print(RestLogLevel.to_json())

# convert the object into a dict
rest_log_level_dict = rest_log_level_instance.to_dict()
# create an instance of RestLogLevel from a dict
rest_log_level_from_dict = RestLogLevel.from_dict(rest_log_level_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


