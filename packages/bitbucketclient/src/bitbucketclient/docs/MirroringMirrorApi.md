# .MirroringMirrorApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**end_rolling_upgrade**](MirroringMirrorApi.md#end_rolling_upgrade) | **POST** /mirroring/latest/zdu/end | End ZDU upgrade on mirror farm
[**get_delayed_sync_repositories**](MirroringMirrorApi.md#get_delayed_sync_repositories) | **GET** /mirroring/latest/mirrorRepos/delayed-sync | Get delayed sync repositories
[**get_farm_nodes**](MirroringMirrorApi.md#get_farm_nodes) | **GET** /mirroring/latest/farmNodes | Get farm nodes
[**get_mirror_mode**](MirroringMirrorApi.md#get_mirror_mode) | **GET** /mirroring/latest/syncSettings/mode | Get mirror mode
[**get_mirror_settings**](MirroringMirrorApi.md#get_mirror_settings) | **GET** /mirroring/latest/syncSettings | Get upstream settings
[**get_mirrored_projects**](MirroringMirrorApi.md#get_mirrored_projects) | **GET** /mirroring/latest/syncSettings/projects | Get mirrored project IDs
[**get_mirrored_repository**](MirroringMirrorApi.md#get_mirrored_repository) | **GET** /mirroring/latest/mirrorRepos/{externalRepositoryId} | Get clone URLs
[**get_ref_changes_queue**](MirroringMirrorApi.md#get_ref_changes_queue) | **GET** /mirroring/latest/supportInfo/refChangesQueue | Get items in ref changes queue
[**get_ref_changes_queue_count**](MirroringMirrorApi.md#get_ref_changes_queue_count) | **GET** /mirroring/latest/supportInfo/refChangesQueue/count | Get total number of items in ref changes queue
[**get_repo_sync_status**](MirroringMirrorApi.md#get_repo_sync_status) | **GET** /mirroring/latest/supportInfo/repoSyncStatus | Get sync status of repositories
[**get_repo_sync_status1**](MirroringMirrorApi.md#get_repo_sync_status1) | **GET** /mirroring/latest/supportInfo/projects/{projectKey}/repos/{repositorySlug}/repoSyncStatus | Gets information about the mirrored repository
[**get_repository_lock_owner**](MirroringMirrorApi.md#get_repository_lock_owner) | **GET** /mirroring/latest/supportInfo/projects/{projectKey}/repos/{repositorySlug}/repo-lock-owner | Get the repository lock owner for the syncing process
[**get_repository_lock_owners**](MirroringMirrorApi.md#get_repository_lock_owners) | **GET** /mirroring/latest/supportInfo/repo-lock-owners | Get all the repository lock owners for the syncing process
[**get_synchronization_progress**](MirroringMirrorApi.md#get_synchronization_progress) | **GET** /mirroring/latest/progress | Get synchronization progress state
[**get_upstream_server**](MirroringMirrorApi.md#get_upstream_server) | **GET** /mirroring/latest/upstreamServer | Get upstream server
[**set_mirror_mode**](MirroringMirrorApi.md#set_mirror_mode) | **PUT** /mirroring/latest/syncSettings/mode | Update mirror mode
[**set_mirror_settings**](MirroringMirrorApi.md#set_mirror_settings) | **PUT** /mirroring/latest/syncSettings | Update upstream settings
[**start_mirroring_project**](MirroringMirrorApi.md#start_mirroring_project) | **POST** /mirroring/latest/syncSettings/projects/{projectId} | Add project to be mirrored
[**start_mirroring_projects**](MirroringMirrorApi.md#start_mirroring_projects) | **POST** /mirroring/latest/syncSettings/projects | Add multiple projects to be mirrored
[**start_rolling_upgrade**](MirroringMirrorApi.md#start_rolling_upgrade) | **POST** /mirroring/latest/zdu/start | Start ZDU upgrade on mirror farm
[**stop_mirroring_project**](MirroringMirrorApi.md#stop_mirroring_project) | **DELETE** /mirroring/latest/syncSettings/projects/{projectId} | Stop mirroring project


# **end_rolling_upgrade**
> RestRollingUpgradeState end_rolling_upgrade()

End ZDU upgrade on mirror farm

Finalizes the ZDU upgrade on the mirror farm denying heterogeneous cluster formation

### Example


```python
import bitbucketclient
from .models.rest_rolling_upgrade_state import RestRollingUpgradeState
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # End ZDU upgrade on mirror farm
        api_response = api_instance.end_rolling_upgrade()
        print("The response of MirroringMirrorApi->end_rolling_upgrade:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->end_rolling_upgrade: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestRollingUpgradeState**](RestRollingUpgradeState.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The state of the rolling upgrade which includes the current version on all the nodes in the farm. |  -  |
**401** | When the user doesn&#39;t have SYS_ADMIN permission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_delayed_sync_repositories**
> List[RestDelayedSyncRepository] get_delayed_sync_repositories(delay_threshold=delay_threshold, limit=limit)

Get delayed sync repositories

Retrieves a list of repositories which have not synced on one or more mirror nodes for at least the threshold time limit after the content was changed in the corresponding upstream repositories. The threshold time limit is defined by a configuration property <code>plugin.mirroring.repository.diagnostics.sync.tolerance</code>. The detection of out of sync repositories is dependent on the timing of a scheduled job which is controlled by a configuration property <code>plugin.mirroring.synchronization.interval</code> which means in worst case it can take upto <code>plugin.mirroring.repository.diagnostics.sync.tolerance</code> + <code>plugin.mirroring.synchronization.interval</code> time to detect an out-of-sync repository.<p>Note: If <code>plugin.mirroring.repository.diagnostics.sync.enabled=false</code> is set on any of the mirror farm nodes, results will not be reported from that node.

### Example


```python
import bitbucketclient
from .models.rest_delayed_sync_repository import RestDelayedSyncRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    delay_threshold = 'delay_threshold_example' # str | Returns only those repositories that are delayed for the given duration. The minimum allowed value is the configured value for the property <code>plugin.mirroring.synchronization.interval</code> (optional)
    limit = 'limit_example' # str | Limit the number of delayed sync repositories returned, the maximum allowed value is 100 (optional)

    try:
        # Get delayed sync repositories
        api_response = api_instance.get_delayed_sync_repositories(delay_threshold=delay_threshold, limit=limit)
        print("The response of MirroringMirrorApi->get_delayed_sync_repositories:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_delayed_sync_repositories: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **delay_threshold** | **str**| Returns only those repositories that are delayed for the given duration. The minimum allowed value is the configured value for the property &lt;code&gt;plugin.mirroring.synchronization.interval&lt;/code&gt; | [optional] 
 **limit** | **str**| Limit the number of delayed sync repositories returned, the maximum allowed value is 100 | [optional] 

### Return type

[**List[RestDelayedSyncRepository]**](RestDelayedSyncRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The upstream ID, project key and repository slug of the delayed sync repositories |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_farm_nodes**
> List[RestClusterNode] get_farm_nodes()

Get farm nodes

Retrieves the list of farm nodes in this cluster

### Example


```python
import bitbucketclient
from .models.rest_cluster_node import RestClusterNode
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get farm nodes
        api_response = api_instance.get_farm_nodes()
        print("The response of MirroringMirrorApi->get_farm_nodes:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_farm_nodes: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**List[RestClusterNode]**](RestClusterNode.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The list of farm nodes |  -  |
**404** | The upstream server could not be found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mirror_mode**
> get_mirror_mode()

Get mirror mode

Gets the current mirror mode

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get mirror mode
        api_instance.get_mirror_mode()
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_mirror_mode: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the current mirror mode |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mirror_settings**
> RestUpstreamSettings get_mirror_settings()

Get upstream settings

Retrieves upstream settings

### Example


```python
import bitbucketclient
from .models.rest_upstream_settings import RestUpstreamSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get upstream settings
        api_response = api_instance.get_mirror_settings()
        print("The response of MirroringMirrorApi->get_mirror_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_mirror_settings: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestUpstreamSettings**](RestUpstreamSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the mirror settings |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mirrored_projects**
> get_mirrored_projects()

Get mirrored project IDs

Returns the IDs of the projects that the mirror is configured to mirror

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get mirrored project IDs
        api_instance.get_mirrored_projects()
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_mirrored_projects: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the currently mirrored project IDs |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_mirrored_repository**
> RestMirroredRepository get_mirrored_repository(external_repository_id)

Get clone URLs

Retrieves all available clone urls for the specified repository.

### Example


```python
import bitbucketclient
from .models.rest_mirrored_repository import RestMirroredRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    external_repository_id = 'external_repository_id_example' # str | the repository ID

    try:
        # Get clone URLs
        api_response = api_instance.get_mirrored_repository(external_repository_id)
        print("The response of MirroringMirrorApi->get_mirrored_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_mirrored_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **external_repository_id** | **str**| the repository ID | 

### Return type

[**RestMirroredRepository**](RestMirroredRepository.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The mirrored repository&#39;s information. |  -  |
**404** | The upstream server or the repository could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_ref_changes_queue**
> RestRefSyncQueue get_ref_changes_queue()

Get items in ref changes queue

Retrieves a list of up to <code>plugin.mirroring.farm.max.ref.change.queue.dump.size</code> items currently in the ref changes queue. The ref changes queue is an internal component of every mirror farm, and is shared between all nodes. When the contents of an upstream repository changes, an item is added to this queue so that the mirror farm nodes know to synchronize. The mirror farm constantly polls and removes items from this queue for processing, so it is empty most of the time.

### Example


```python
import bitbucketclient
from .models.rest_ref_sync_queue import RestRefSyncQueue
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get items in ref changes queue
        api_response = api_instance.get_ref_changes_queue()
        print("The response of MirroringMirrorApi->get_ref_changes_queue:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_ref_changes_queue: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestRefSyncQueue**](RestRefSyncQueue.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The contents of the ref changes queue |  -  |
**401** | The currently authenticated user has insufficient permissions to call this resource. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_ref_changes_queue_count**
> get_ref_changes_queue_count()

Get total number of items in ref changes queue

Retrieves the total number of items currently in the ref changes queue

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get total number of items in ref changes queue
        api_instance.get_ref_changes_queue_count()
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_ref_changes_queue_count: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The total number of items currently in the ref changes queue |  -  |
**401** | When the user doesn&#39;t have ADMIN permission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repo_sync_status**
> GetRepoSyncStatus200Response get_repo_sync_status(start=start, limit=limit)

Get sync status of repositories

Retrieves a page of sync statuses of the repositories on this mirror node

### Example


```python
import bitbucketclient
from .models.get_repo_sync_status200_response import GetRepoSyncStatus200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get sync status of repositories
        api_response = api_instance.get_repo_sync_status(start=start, limit=limit)
        print("The response of MirroringMirrorApi->get_repo_sync_status:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_repo_sync_status: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepoSyncStatus200Response**](GetRepoSyncStatus200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The sync status of the repositories on this node |  -  |
**401** | When the user doesn&#39;t have ADMIN permission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repo_sync_status1**
> RestMirrorRepositorySynchronizationStatus get_repo_sync_status1(project_key, repository_slug)

Gets information about the mirrored repository

Retrieves information about an external repository mirrored by the mirror server. Particularly the local ID & external ID of the repository

### Example


```python
import bitbucketclient
from .models.rest_mirror_repository_synchronization_status import RestMirrorRepositorySynchronizationStatus
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Gets information about the mirrored repository
        api_response = api_instance.get_repo_sync_status1(project_key, repository_slug)
        print("The response of MirroringMirrorApi->get_repo_sync_status1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_repo_sync_status1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestMirrorRepositorySynchronizationStatus**](RestMirrorRepositorySynchronizationStatus.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The sync status of the repository on this node |  -  |
**401** | When the user doesn&#39;t have ADMIN permission |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_lock_owner**
> RestRepositoryLockOwner get_repository_lock_owner(project_key, repository_slug)

Get the repository lock owner for the syncing process

Retrieves the information about the process owning the sync lock for this repository. The process owning the lock could be running on any of the nodes in the mirror farm

### Example


```python
import bitbucketclient
from .models.rest_repository_lock_owner import RestRepositoryLockOwner
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get the repository lock owner for the syncing process
        api_response = api_instance.get_repository_lock_owner(project_key, repository_slug)
        print("The response of MirroringMirrorApi->get_repository_lock_owner:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_repository_lock_owner: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestRepositoryLockOwner**](RestRepositoryLockOwner.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The information about the repository lock owner for the syncing process, if the lock is currently being held, otherwise an empty response |  -  |
**401** | When the user doesn&#39;t have ADMIN permission |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_lock_owners**
> List[RestRepositoryLockOwner] get_repository_lock_owners()

Get all the repository lock owners for the syncing process

Retrieves the information about all the processes from the all the nodes in the mirror farm owning sync lock for any repository

### Example


```python
import bitbucketclient
from .models.rest_repository_lock_owner import RestRepositoryLockOwner
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get all the repository lock owners for the syncing process
        api_response = api_instance.get_repository_lock_owners()
        print("The response of MirroringMirrorApi->get_repository_lock_owners:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_repository_lock_owners: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**List[RestRepositoryLockOwner]**](RestRepositoryLockOwner.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A list of all the repository lock owners for the syncing process |  -  |
**401** | When the user doesn&#39;t have ADMIN permission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_synchronization_progress**
> RestSyncProgress get_synchronization_progress()

Get synchronization progress state

 Retrieves synchronization progress state.If there's no progress to report, this resource will return <pre><code> {"discovering":false,"syncedRepos":0,"totalRepos":0}</code></pre> If there are repositories in the process of synchronizing, but the precise number hasn't been discovered yet, this resource will return: <pre><code> {"discovering":true,"syncedRepos":3,"totalRepos":100}</code></pre> If there is progress to report and the total number of repositories is known, this resource will return: <pre> <code> {"discovering":false,"syncedRepos":242,"totalRepos":1071}</code> </pre>

### Example


```python
import bitbucketclient
from .models.rest_sync_progress import RestSyncProgress
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get synchronization progress state
        api_response = api_instance.get_synchronization_progress()
        print("The response of MirroringMirrorApi->get_synchronization_progress:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_synchronization_progress: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestSyncProgress**](RestSyncProgress.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the synchronization progress state |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_upstream_server**
> RestUpstreamServer get_upstream_server()

Get upstream server

Retrieves upstream server details.

### Example


```python
import bitbucketclient
from .models.rest_upstream_server import RestUpstreamServer
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Get upstream server
        api_response = api_instance.get_upstream_server()
        print("The response of MirroringMirrorApi->get_upstream_server:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->get_upstream_server: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestUpstreamServer**](RestUpstreamServer.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The upstream server. |  -  |
**404** | The upstream server could not be found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_mirror_mode**
> set_mirror_mode(body=body)

Update mirror mode

Sets the mirror mode for the specified upstream

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    body = 'body_example' # str |  (optional)

    try:
        # Update mirror mode
        api_instance.set_mirror_mode(body=body)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->set_mirror_mode: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **body** | **str**|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the mode to set |  -  |
**400** | The provided mode is invalid |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_mirror_settings**
> RestUpstreamSettings set_mirror_settings(rest_upstream_settings=rest_upstream_settings)

Update upstream settings

Sets the settings for the specified upstream

### Example


```python
import bitbucketclient
from .models.rest_upstream_settings import RestUpstreamSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    rest_upstream_settings = .RestUpstreamSettings() # RestUpstreamSettings | the mirror settings to update to (optional)

    try:
        # Update upstream settings
        api_response = api_instance.set_mirror_settings(rest_upstream_settings=rest_upstream_settings)
        print("The response of MirroringMirrorApi->set_mirror_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->set_mirror_settings: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **rest_upstream_settings** | [**RestUpstreamSettings**](RestUpstreamSettings.md)| the mirror settings to update to | [optional] 

### Return type

[**RestUpstreamSettings**](RestUpstreamSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the updated mirror settings |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_mirroring_project**
> start_mirroring_project(project_id)

Add project to be mirrored

Configures the mirror to mirror the provided project

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    project_id = 'project_id_example' # str | 

    try:
        # Add project to be mirrored
        api_instance.start_mirroring_project(project_id)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->start_mirroring_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the currently mirrored project IDs |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_mirroring_projects**
> start_mirroring_projects(request_body=request_body)

Add multiple projects to be mirrored

Configures the mirror to mirror the provided projects

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    request_body = ['request_body_example'] # List[str] |  (optional)

    try:
        # Add multiple projects to be mirrored
        api_instance.start_mirroring_projects(request_body=request_body)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->start_mirroring_projects: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **request_body** | [**List[str]**](str.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the currently mirrored project IDs |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_rolling_upgrade**
> RestRollingUpgradeState start_rolling_upgrade()

Start ZDU upgrade on mirror farm

Enables upgrading of individual nodes within the cluster, allowing a heterogeneous cluster formation

### Example


```python
import bitbucketclient
from .models.rest_rolling_upgrade_state import RestRollingUpgradeState
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)

    try:
        # Start ZDU upgrade on mirror farm
        api_response = api_instance.start_rolling_upgrade()
        print("The response of MirroringMirrorApi->start_rolling_upgrade:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->start_rolling_upgrade: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestRollingUpgradeState**](RestRollingUpgradeState.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The state of the rolling upgrade which includes the minimum version of all the nodes in the farm. |  -  |
**401** | When the user doesn&#39;t have SYS_ADMIN permission |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stop_mirroring_project**
> stop_mirroring_project(project_id)

Stop mirroring project

Configures the mirror to no longer mirror the provided project

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .MirroringMirrorApi(api_client)
    project_id = 'project_id_example' # str | the project ID to stop mirroring

    try:
        # Stop mirroring project
        api_instance.stop_mirroring_project(project_id)
    except Exception as e:
        print("Exception when calling MirroringMirrorApi->stop_mirroring_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_id** | **str**| the project ID to stop mirroring | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | the request has been processed |  -  |
**401** | When the user is not a service user for the currently registered upstream or doesn&#39;t have ADMIN permission |  -  |
**404** | The upstream server could not be found. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

