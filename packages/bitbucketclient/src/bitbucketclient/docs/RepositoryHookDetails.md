# RepositoryHookDetails


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**config_form_key** | **str** |  | [optional] 
**description** | **str** |  | [optional] 
**key** | **str** |  | [optional] 
**name** | **str** |  | [optional] 
**supported_scopes** | **List[str]** |  | [optional] 
**type** | **str** |  | [optional] 
**version** | **str** |  | [optional] 

## Example

```python
from .models.repository_hook_details import RepositoryHookDetails

# TODO update the JSON string below
json = "{}"
# create an instance of RepositoryHookDetails from a JSON string
repository_hook_details_instance = RepositoryHookDetails.from_json(json)
# print the JSON string representation of the object
print(RepositoryHookDetails.to_json())

# convert the object into a dict
repository_hook_details_dict = repository_hook_details_instance.to_dict()
# create an instance of RepositoryHookDetails from a dict
repository_hook_details_from_dict = RepositoryHookDetails.from_dict(repository_hook_details_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


