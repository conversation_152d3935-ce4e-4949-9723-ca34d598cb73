# SetSettings2Request


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**debug_logging_enabled** | **bool** |  | 
**profiling_enabled** | **bool** |  | 

## Example

```python
from .models.set_settings2_request import SetSettings2Request

# TODO update the JSON string below
json = "{}"
# create an instance of SetSettings2Request from a JSON string
set_settings2_request_instance = SetSettings2Request.from_json(json)
# print the JSON string representation of the object
print(SetSettings2Request.to_json())

# convert the object into a dict
set_settings2_request_dict = set_settings2_request_instance.to_dict()
# create an instance of SetSettings2Request from a dict
set_settings2_request_from_dict = SetSettings2Request.from_dict(set_settings2_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


