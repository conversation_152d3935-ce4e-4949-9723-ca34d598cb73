# RestBuildStats


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**cancelled** | **int** |  | [optional] 
**failed** | **int** |  | [optional] 
**in_progress** | **int** |  | [optional] 
**successful** | **int** |  | [optional] 
**unknown** | **int** |  | [optional] 

## Example

```python
from .models.rest_build_stats import RestBuildStats

# TODO update the JSON string below
json = "{}"
# create an instance of RestBuildStats from a JSON string
rest_build_stats_instance = RestBuildStats.from_json(json)
# print the JSON string representation of the object
print(RestBuildStats.to_json())

# convert the object into a dict
rest_build_stats_dict = rest_build_stats_instance.to_dict()
# create an instance of RestBuildStats from a dict
rest_build_stats_from_dict = RestBuildStats.from_dict(rest_build_stats_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


