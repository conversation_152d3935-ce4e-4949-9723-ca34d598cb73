# .AuthenticationApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_for_project**](AuthenticationApi.md#add_for_project) | **POST** /keys/latest/projects/{projectKey}/ssh | Add project SSH key
[**add_for_repository**](AuthenticationApi.md#add_for_repository) | **POST** /keys/latest/projects/{projectKey}/repos/{repositorySlug}/ssh | Add repository SSH key
[**add_idp**](AuthenticationApi.md#add_idp) | **POST** /authconfig/latest/idps | Create IdP configuration
[**add_ssh_key**](AuthenticationApi.md#add_ssh_key) | **POST** /ssh/latest/keys | Add SSH key for user
[**authenticate**](AuthenticationApi.md#authenticate) | **POST** /tsv/latest/authenticate | Authenticate with 2SV
[**authenticate_with_recovery_code**](AuthenticationApi.md#authenticate_with_recovery_code) | **POST** /tsv/latest/authenticate/recovery-code | Authenticate using recovery code
[**complete_authentication_change**](AuthenticationApi.md#complete_authentication_change) | **POST** /tsv/latest/totp/complete-enrollment-update | Complete authentication app update for 2SV
[**complete_enforced_enrollment**](AuthenticationApi.md#complete_enforced_enrollment) | **POST** /tsv/latest/totp/complete-enforced-enrollment | Complete enforced enrollment in 2SV
[**complete_voluntary_enrollment**](AuthenticationApi.md#complete_voluntary_enrollment) | **POST** /tsv/latest/totp/complete-voluntary-enrollment | Complete voluntary enrollment in 2SV
[**create_access_token**](AuthenticationApi.md#create_access_token) | **PUT** /access-tokens/latest/projects/{projectKey} | Create project HTTP token
[**create_access_token1**](AuthenticationApi.md#create_access_token1) | **PUT** /access-tokens/latest/projects/{projectKey}/repos/{repositorySlug} | Create repository HTTP token
[**create_access_token2**](AuthenticationApi.md#create_access_token2) | **PUT** /access-tokens/latest/users/{userSlug} | Create personal HTTP token
[**delete_by_id**](AuthenticationApi.md#delete_by_id) | **DELETE** /access-tokens/latest/projects/{projectKey}/{tokenId} | Delete a HTTP token
[**delete_by_id1**](AuthenticationApi.md#delete_by_id1) | **DELETE** /access-tokens/latest/projects/{projectKey}/repos/{repositorySlug}/{tokenId} | Delete a HTTP token
[**delete_by_id2**](AuthenticationApi.md#delete_by_id2) | **DELETE** /access-tokens/latest/users/{userSlug}/{tokenId} | Delete a HTTP token
[**delete_ssh_key**](AuthenticationApi.md#delete_ssh_key) | **DELETE** /ssh/latest/keys/{keyId} | Remove SSH key
[**delete_ssh_keys**](AuthenticationApi.md#delete_ssh_keys) | **DELETE** /ssh/latest/keys | Delete all user SSH key
[**elevate_permissions_with_password**](AuthenticationApi.md#elevate_permissions_with_password) | **POST** /tsv/latest/elevate-permissions/password | Create elevated session with password
[**elevate_permissions_with_recovery_code**](AuthenticationApi.md#elevate_permissions_with_recovery_code) | **POST** /tsv/latest/elevate-permissions/recovery-code | Create elevated session with recovery code
[**elevate_permissions_with_totp**](AuthenticationApi.md#elevate_permissions_with_totp) | **POST** /tsv/latest/elevate-permissions/totp | Create elevated session with TOTP
[**get**](AuthenticationApi.md#get) | **GET** /basicauth/latest/config | Get basic auth configuration
[**get_all_access_tokens**](AuthenticationApi.md#get_all_access_tokens) | **GET** /access-tokens/latest/projects/{projectKey} | Get project HTTP tokens
[**get_all_access_tokens1**](AuthenticationApi.md#get_all_access_tokens1) | **GET** /access-tokens/latest/projects/{projectKey}/repos/{repositorySlug} | Get repository HTTP tokens
[**get_all_access_tokens2**](AuthenticationApi.md#get_all_access_tokens2) | **GET** /access-tokens/latest/users/{userSlug} | Get personal HTTP tokens
[**get_by_id**](AuthenticationApi.md#get_by_id) | **GET** /access-tokens/latest/projects/{projectKey}/{tokenId} | Get HTTP token by ID
[**get_by_id1**](AuthenticationApi.md#get_by_id1) | **GET** /access-tokens/latest/projects/{projectKey}/repos/{repositorySlug}/{tokenId} | Get HTTP token by ID
[**get_by_id2**](AuthenticationApi.md#get_by_id2) | **GET** /access-tokens/latest/users/{userSlug}/{tokenId} | Get HTTP token by ID
[**get_captcha_data**](AuthenticationApi.md#get_captcha_data) | **GET** /tsv/latest/authenticate/captcha | Get CAPTCHA challenge
[**get_config**](AuthenticationApi.md#get_config) | **GET** /authconfig/latest/sso | Get SSO configuration
[**get_elevated_permission_status**](AuthenticationApi.md#get_elevated_permission_status) | **GET** /tsv/latest/elevate-permissions | Get elevated session status
[**get_for_project**](AuthenticationApi.md#get_for_project) | **GET** /keys/latest/projects/{projectKey}/ssh/{keyId} | Get project SSH key
[**get_for_projects**](AuthenticationApi.md#get_for_projects) | **GET** /keys/latest/ssh/{keyId}/projects | Get project SSH keys
[**get_for_repositories**](AuthenticationApi.md#get_for_repositories) | **GET** /keys/latest/ssh/{keyId}/repos | Get repository SSH key
[**get_for_repository**](AuthenticationApi.md#get_for_repository) | **GET** /keys/latest/projects/{projectKey}/repos/{repositorySlug}/ssh/{keyId} | Get repository SSH key
[**get_for_repository1**](AuthenticationApi.md#get_for_repository1) | **GET** /keys/latest/projects/{projectKey}/repos/{repositorySlug}/ssh | Get repository SSH keys
[**get_idp**](AuthenticationApi.md#get_idp) | **GET** /authconfig/latest/idps/{id} | Get IdP configuration
[**get_idps**](AuthenticationApi.md#get_idps) | **GET** /authconfig/latest/idps | Get all configured IdPs
[**get_jit_provisioned_users**](AuthenticationApi.md#get_jit_provisioned_users) | **GET** /authconfig/latest/jit-users | Get all JIT provisioned users
[**get_login_options**](AuthenticationApi.md#get_login_options) | **GET** /authconfig/latest/login-options | Get available login options
[**get_ssh_key**](AuthenticationApi.md#get_ssh_key) | **GET** /ssh/latest/keys/{keyId} | Get SSH key for user by keyId
[**get_ssh_keys**](AuthenticationApi.md#get_ssh_keys) | **GET** /ssh/latest/keys | Get SSH keys for user
[**get_ssh_keys_for_project**](AuthenticationApi.md#get_ssh_keys_for_project) | **GET** /keys/latest/projects/{projectKey}/ssh | Get SSH key
[**get_sso_management_status**](AuthenticationApi.md#get_sso_management_status) | **GET** /tsv/latest/sso-management-status | Get SSO management status
[**get_status**](AuthenticationApi.md#get_status) | **GET** /tsv/latest/status | Get two-step verification status
[**put**](AuthenticationApi.md#put) | **PUT** /basicauth/latest/config | Update basic auth configuration
[**remove_idp**](AuthenticationApi.md#remove_idp) | **DELETE** /authconfig/latest/idps/{id} | Delete IdP configuration
[**revoke_for_project**](AuthenticationApi.md#revoke_for_project) | **DELETE** /keys/latest/projects/{projectKey}/ssh/{keyId} | Revoke project SSH key
[**revoke_for_repository**](AuthenticationApi.md#revoke_for_repository) | **DELETE** /keys/latest/projects/{projectKey}/repos/{repositorySlug}/ssh/{keyId} | Revoke repository SSH key
[**revoke_many**](AuthenticationApi.md#revoke_many) | **DELETE** /keys/latest/ssh/{keyId} | Revoke project SSH key
[**rotate_recover_code**](AuthenticationApi.md#rotate_recover_code) | **POST** /tsv/latest/totp/recovery-code/rotate | Rotate recovery code
[**ssh_settings**](AuthenticationApi.md#ssh_settings) | **GET** /ssh/latest/settings | Get SSH settings
[**start_enforced_enrollment**](AuthenticationApi.md#start_enforced_enrollment) | **POST** /tsv/latest/totp/start-enforced-enrollment | Start enforced enrollment in 2SV
[**start_enrollment_update**](AuthenticationApi.md#start_enrollment_update) | **POST** /tsv/latest/totp/start-enrollment-update | Start authentication app update for 2SV
[**start_voluntary_enrollment**](AuthenticationApi.md#start_voluntary_enrollment) | **POST** /tsv/latest/totp/start-voluntary-enrollment | Start voluntary enrollment in 2SV
[**unenroll**](AuthenticationApi.md#unenroll) | **DELETE** /tsv/latest/totp/unenroll | Uneroll current user from two-step verification
[**unenroll_user**](AuthenticationApi.md#unenroll_user) | **DELETE** /tsv/latest/totp/unenroll/user/{userName} | Unenroll specific user from two-step verification
[**update_access_token**](AuthenticationApi.md#update_access_token) | **POST** /access-tokens/latest/projects/{projectKey}/{tokenId} | Update HTTP token
[**update_access_token1**](AuthenticationApi.md#update_access_token1) | **POST** /access-tokens/latest/projects/{projectKey}/repos/{repositorySlug}/{tokenId} | Update HTTP token
[**update_access_token2**](AuthenticationApi.md#update_access_token2) | **POST** /access-tokens/latest/users/{userSlug}/{tokenId} | Update HTTP token
[**update_config**](AuthenticationApi.md#update_config) | **PATCH** /authconfig/latest/sso | Update SSO configuration
[**update_idp**](AuthenticationApi.md#update_idp) | **PATCH** /authconfig/latest/idps/{id} | Update IdP configuration
[**update_permission**](AuthenticationApi.md#update_permission) | **PUT** /keys/latest/projects/{projectKey}/ssh/{keyId}/permission/{permission} | Update project SSH key permission
[**update_permission1**](AuthenticationApi.md#update_permission1) | **PUT** /keys/latest/projects/{projectKey}/repos/{repositorySlug}/ssh/{keyId}/permission/{permission} | Update repository SSH key permission
[**verify_code**](AuthenticationApi.md#verify_code) | **POST** /tsv/latest/authenticate/totp-code | Authenticate using TOTP code


# **add_for_project**
> RestSshAccessKey add_for_project(project_key, rest_ssh_access_key=rest_ssh_access_key)

Add project SSH key

Register a new SSH key and grants access to the project identified in the URL.

### Example


```python
import bitbucketclient
from .models.rest_ssh_access_key import RestSshAccessKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    rest_ssh_access_key = .RestSshAccessKey() # RestSshAccessKey |  (optional)

    try:
        # Add project SSH key
        api_response = api_instance.add_for_project(project_key, rest_ssh_access_key=rest_ssh_access_key)
        print("The response of AuthenticationApi->add_for_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->add_for_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **rest_ssh_access_key** | [**RestSshAccessKey**](RestSshAccessKey.md)|  | [optional] 

### Return type

[**RestSshAccessKey**](RestSshAccessKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created access key. |  -  |
**400** | The current request contains invalid or missing values. |  -  |
**401** | The currently authenticated user has insufficient permissions to add an access key to the project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_for_repository**
> RestSshAccessKey add_for_repository(project_key, repository_slug, rest_ssh_access_key=rest_ssh_access_key)

Add repository SSH key

Register a new SSH key and grants access to the repository identified in the URL.

### Example


```python
import bitbucketclient
from .models.rest_ssh_access_key import RestSshAccessKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug
    rest_ssh_access_key = .RestSshAccessKey() # RestSshAccessKey |  (optional)

    try:
        # Add repository SSH key
        api_response = api_instance.add_for_repository(project_key, repository_slug, rest_ssh_access_key=rest_ssh_access_key)
        print("The response of AuthenticationApi->add_for_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->add_for_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 
 **rest_ssh_access_key** | [**RestSshAccessKey**](RestSshAccessKey.md)|  | [optional] 

### Return type

[**RestSshAccessKey**](RestSshAccessKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created access key. |  -  |
**400** | The current request contains invalid or missing values. |  -  |
**401** | The currently authenticated user has insufficient permissions to add an access key to the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_idp**
> IdpConfigEntity add_idp(idp_config_entity=idp_config_entity)

Create IdP configuration

Creates a new IdP configuration.

### Example


```python
import bitbucketclient
from .models.idp_config_entity import IdpConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    idp_config_entity = .IdpConfigEntity() # IdpConfigEntity | The configuration of the new IdP to add. The ID must be null. (optional)

    try:
        # Create IdP configuration
        api_response = api_instance.add_idp(idp_config_entity=idp_config_entity)
        print("The response of AuthenticationApi->add_idp:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->add_idp: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **idp_config_entity** | [**IdpConfigEntity**](IdpConfigEntity.md)| The configuration of the new IdP to add. The ID must be null. | [optional] 

### Return type

[**IdpConfigEntity**](IdpConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly created IdP configuration. |  -  |
**400** | The provided IdP configuration was either incorrect or invalid. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_ssh_key**
> RestSshKey add_ssh_key(user=user, add_ssh_key_request=add_ssh_key_request)

Add SSH key for user

Add a new SSH key to a supplied user.

### Example


```python
import bitbucketclient
from .models.add_ssh_key_request import AddSshKeyRequest
from .models.rest_ssh_key import RestSshKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    user = .RestSshKey() # RestSshKey | the username of the user to add the SSH key for. If no username is specified, the SSH key will be added for the current authenticated user. (optional)
    add_ssh_key_request = .AddSshKeyRequest() # AddSshKeyRequest |  (optional)

    try:
        # Add SSH key for user
        api_response = api_instance.add_ssh_key(user=user, add_ssh_key_request=add_ssh_key_request)
        print("The response of AuthenticationApi->add_ssh_key:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->add_ssh_key: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user** | [**RestSshKey**](.md)| the username of the user to add the SSH key for. If no username is specified, the SSH key will be added for the current authenticated user. | [optional] 
 **add_ssh_key_request** | [**AddSshKeyRequest**](AddSshKeyRequest.md)|  | [optional] 

### Return type

[**RestSshKey**](RestSshKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created SSH key. |  -  |
**400** | The SSH key was not created because the key was not a valid RSA/DSA/ECDSA/Ed25519 key of a supported length. |  -  |
**401** | Either there is no authenticated user or the currently authenticated user has insufficient permissions to add an SSH key. The latter is only possible when a &lt;strong&gt;user&lt;/strong&gt; is explicitly supplied. |  -  |
**404** | No user matches the supplied &lt;strong&gt;user&lt;/strong&gt; |  -  |
**409** | The SSH key already exists on the system. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authenticate**
> AuthenticationResponse authenticate(authentication_entity=authentication_entity)

Authenticate with 2SV

Authenticates as the given user. This endpoint <strong>may</strong>:

- Ask for two-step verification if the user has enrolled; or
- Enforce enrollment in two-step verification if two-step verification enforcement is configured for the instance and the user is not yet enrolled.

### Example


```python
import bitbucketclient
from .models.authentication_entity import AuthenticationEntity
from .models.authentication_response import AuthenticationResponse
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    authentication_entity = .AuthenticationEntity() # AuthenticationEntity |  (optional)

    try:
        # Authenticate with 2SV
        api_response = api_instance.authenticate(authentication_entity=authentication_entity)
        print("The response of AuthenticationApi->authenticate:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->authenticate: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **authentication_entity** | [**AuthenticationEntity**](AuthenticationEntity.md)|  | [optional] 

### Return type

[**AuthenticationResponse**](AuthenticationResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The user has successfully authenticated. |  -  |
**401** | The given user failed authentication. |  -  |
**412** | The user must undergo additional verification or enroll in two-step verification in order to authenticate |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **authenticate_with_recovery_code**
> AuthenticationResponse authenticate_with_recovery_code(totp_recovery_code_authentication_dto=totp_recovery_code_authentication_dto)

Authenticate using recovery code

Authenticate as the given user using a recovery code.

### Example


```python
import bitbucketclient
from .models.authentication_response import AuthenticationResponse
from .models.totp_recovery_code_authentication_dto import TotpRecoveryCodeAuthenticationDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    totp_recovery_code_authentication_dto = .TotpRecoveryCodeAuthenticationDTO() # TotpRecoveryCodeAuthenticationDTO | A request containing a recovery code for the specified user. (optional)

    try:
        # Authenticate using recovery code
        api_response = api_instance.authenticate_with_recovery_code(totp_recovery_code_authentication_dto=totp_recovery_code_authentication_dto)
        print("The response of AuthenticationApi->authenticate_with_recovery_code:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->authenticate_with_recovery_code: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **totp_recovery_code_authentication_dto** | [**TotpRecoveryCodeAuthenticationDTO**](TotpRecoveryCodeAuthenticationDTO.md)| A request containing a recovery code for the specified user. | [optional] 

### Return type

[**AuthenticationResponse**](AuthenticationResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The user was successfully logged in. |  -  |
**400** | The requested conversation cannot be found or is not valid in the requested context. |  -  |
**401** | The recovery code provided was incorrect. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **complete_authentication_change**
> TotpUserEnrollmentDTO complete_authentication_change(totp_code_verification_dto=totp_code_verification_dto)

Complete authentication app update for 2SV

Complete update of the authentication app used for two-step verification by verifying the provided TOTP code.

### Example


```python
import bitbucketclient
from .models.totp_code_verification_dto import TotpCodeVerificationDTO
from .models.totp_user_enrollment_dto import TotpUserEnrollmentDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    totp_code_verification_dto = .TotpCodeVerificationDTO() # TotpCodeVerificationDTO | A request containing a TOTP code for the given user. (optional)

    try:
        # Complete authentication app update for 2SV
        api_response = api_instance.complete_authentication_change(totp_code_verification_dto=totp_code_verification_dto)
        print("The response of AuthenticationApi->complete_authentication_change:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->complete_authentication_change: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **totp_code_verification_dto** | [**TotpCodeVerificationDTO**](TotpCodeVerificationDTO.md)| A request containing a TOTP code for the given user. | [optional] 

### Return type

[**TotpUserEnrollmentDTO**](TotpUserEnrollmentDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Authentication app successfully updated. |  -  |
**400** | The requested authentication app conversation cannot be found or the user is not enrolled in two-step verification |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **complete_enforced_enrollment**
> TotpRecoveryCodeDTO complete_enforced_enrollment(totp_code_verification_dto=totp_code_verification_dto)

Complete enforced enrollment in 2SV

Complete enforced enrollment in two-step verification by verifying the provided TOTP code and creating a new session for the given user.

### Example


```python
import bitbucketclient
from .models.totp_code_verification_dto import TotpCodeVerificationDTO
from .models.totp_recovery_code_dto import TotpRecoveryCodeDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    totp_code_verification_dto = .TotpCodeVerificationDTO() # TotpCodeVerificationDTO | A request containing a TOTP code for the given user. (optional)

    try:
        # Complete enforced enrollment in 2SV
        api_response = api_instance.complete_enforced_enrollment(totp_code_verification_dto=totp_code_verification_dto)
        print("The response of AuthenticationApi->complete_enforced_enrollment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->complete_enforced_enrollment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **totp_code_verification_dto** | [**TotpCodeVerificationDTO**](TotpCodeVerificationDTO.md)| A request containing a TOTP code for the given user. | [optional] 

### Return type

[**TotpRecoveryCodeDTO**](TotpRecoveryCodeDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Enrollment successfully completed. |  -  |
**400** | The requested enrollment conversation cannot be found or the user is already enrolled in two-step verification. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **complete_voluntary_enrollment**
> TotpUserEnrollmentDTO complete_voluntary_enrollment(totp_code_verification_dto=totp_code_verification_dto)

Complete voluntary enrollment in 2SV

Complete voluntary enrollment in two-step verification by verifying the provided TOTP code and creating a new session for the given user.

### Example


```python
import bitbucketclient
from .models.totp_code_verification_dto import TotpCodeVerificationDTO
from .models.totp_user_enrollment_dto import TotpUserEnrollmentDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    totp_code_verification_dto = .TotpCodeVerificationDTO() # TotpCodeVerificationDTO | A request containing a TOTP code for the given user. (optional)

    try:
        # Complete voluntary enrollment in 2SV
        api_response = api_instance.complete_voluntary_enrollment(totp_code_verification_dto=totp_code_verification_dto)
        print("The response of AuthenticationApi->complete_voluntary_enrollment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->complete_voluntary_enrollment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **totp_code_verification_dto** | [**TotpCodeVerificationDTO**](TotpCodeVerificationDTO.md)| A request containing a TOTP code for the given user. | [optional] 

### Return type

[**TotpUserEnrollmentDTO**](TotpUserEnrollmentDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Enrollment successfully completed. |  -  |
**400** | The requested enrollment conversation cannot be found or the user is already enrolled in two-step verification. |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_access_token**
> RestRawAccessToken create_access_token(project_key, rest_access_token_request=rest_access_token_request)

Create project HTTP token

Create an access token for the project according to the given request.

### Example


```python
import bitbucketclient
from .models.rest_access_token_request import RestAccessTokenRequest
from .models.rest_raw_access_token import RestRawAccessToken
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    rest_access_token_request = .RestAccessTokenRequest() # RestAccessTokenRequest | The request containing the details of the access token to create. (optional)

    try:
        # Create project HTTP token
        api_response = api_instance.create_access_token(project_key, rest_access_token_request=rest_access_token_request)
        print("The response of AuthenticationApi->create_access_token:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->create_access_token: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **rest_access_token_request** | [**RestAccessTokenRequest**](RestAccessTokenRequest.md)| The request containing the details of the access token to create. | [optional] 

### Return type

[**RestRawAccessToken**](RestRawAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the raw access token and associated details. |  -  |
**400** | One of the following error cases occurred (check the error message for more details).  - The request does not contain a token name - The request does not contain a list of permissions, or the list of permissions is empty - One of the provided permission levels are unknown - The project already has the maximum number of tokens  |  -  |
**401** | The currently authenticated user is not permitted to create an access token for this project or authentication failed. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_access_token1**
> RestRawAccessToken create_access_token1(project_key, repository_slug, rest_access_token_request=rest_access_token_request)

Create repository HTTP token

Create an access token for the repository according to the given request.

### Example


```python
import bitbucketclient
from .models.rest_access_token_request import RestAccessTokenRequest
from .models.rest_raw_access_token import RestRawAccessToken
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_access_token_request = .RestAccessTokenRequest() # RestAccessTokenRequest | The request containing the details of the access token to create. (optional)

    try:
        # Create repository HTTP token
        api_response = api_instance.create_access_token1(project_key, repository_slug, rest_access_token_request=rest_access_token_request)
        print("The response of AuthenticationApi->create_access_token1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->create_access_token1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_access_token_request** | [**RestAccessTokenRequest**](RestAccessTokenRequest.md)| The request containing the details of the access token to create. | [optional] 

### Return type

[**RestRawAccessToken**](RestRawAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the raw access token and associated details. |  -  |
**400** | One of the following error cases occurred (check the error message for more details).  - The request does not contain a token name- The request does not contain a list of permissions, or the list of permissions is empty- One of the provided permission levels are unknown- The repository already has the maximum number of tokens |  -  |
**401** | The currently authenticated user is not permitted to create an access token for this repository or authentication failed. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_access_token2**
> RestRawAccessToken create_access_token2(user_slug, rest_access_token_request=rest_access_token_request)

Create personal HTTP token

Create an access token for the user according to the given request.

### Example


```python
import bitbucketclient
from .models.rest_access_token_request import RestAccessTokenRequest
from .models.rest_raw_access_token import RestRawAccessToken
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.
    rest_access_token_request = .RestAccessTokenRequest() # RestAccessTokenRequest | The request containing the details of the access token to create. (optional)

    try:
        # Create personal HTTP token
        api_response = api_instance.create_access_token2(user_slug, rest_access_token_request=rest_access_token_request)
        print("The response of AuthenticationApi->create_access_token2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->create_access_token2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 
 **rest_access_token_request** | [**RestAccessTokenRequest**](RestAccessTokenRequest.md)| The request containing the details of the access token to create. | [optional] 

### Return type

[**RestRawAccessToken**](RestRawAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the raw access token and associated details. |  -  |
**400** | One of the following error cases occurred (check the error message for more details).  - The request does not contain a token name - The request does not contain a list of permissions, or the list of permissions is empty - One of the provided permission levels are unknown - The user already has their maximum number of tokens  |  -  |
**401** | The currently authenticated user is not permitted to create an access token on behalf of this user or authentication failed |  -  |
**404** | The specified user does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_by_id**
> delete_by_id(project_key, token_id)

Delete a HTTP token

Delete the access token identified by the given ID.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    token_id = 'token_id_example' # str | The token id.

    try:
        # Delete a HTTP token
        api_instance.delete_by_id(project_key, token_id)
    except Exception as e:
        print("Exception when calling AuthenticationApi->delete_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **token_id** | **str**| The token id. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the token has been deleted. |  -  |
**401** | The currently authenticated user is not permitted to delete an access token on behalf of this user or authentication failed. |  -  |
**404** | The specified user or token does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_by_id1**
> delete_by_id1(project_key, token_id, repository_slug)

Delete a HTTP token

Delete the access token identified by the given ID.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    token_id = 'token_id_example' # str | The token id.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete a HTTP token
        api_instance.delete_by_id1(project_key, token_id, repository_slug)
    except Exception as e:
        print("Exception when calling AuthenticationApi->delete_by_id1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **token_id** | **str**| The token id. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the token has been deleted. |  -  |
**401** | The currently authenticated user is not permitted to delete an access token on behalf of this user or authentication failed. |  -  |
**404** | The specified user or token does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_by_id2**
> delete_by_id2(token_id, user_slug)

Delete a HTTP token

Delete the access token identified by the given ID.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    token_id = 'token_id_example' # str | The token id.
    user_slug = 'user_slug_example' # str | The user slug.

    try:
        # Delete a HTTP token
        api_instance.delete_by_id2(token_id, user_slug)
    except Exception as e:
        print("Exception when calling AuthenticationApi->delete_by_id2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **token_id** | **str**| The token id. | 
 **user_slug** | **str**| The user slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the token has been deleted. |  -  |
**401** | The currently authenticated user is not permitted to delete an access token on behalf of this user or authentication failed. |  -  |
**404** | The specified user or token does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_ssh_key**
> delete_ssh_key(key_id)

Remove SSH key

Delete an SSH key.

The authenticated user must have <strong>ADMIN</strong> permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    key_id = 'key_id_example' # str | the id of the key to delete.

    try:
        # Remove SSH key
        api_instance.delete_ssh_key(key_id)
    except Exception as e:
        print("Exception when calling AuthenticationApi->delete_ssh_key: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **key_id** | **str**| the id of the key to delete. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The SSH key matching the supplied &lt;strong&gt;id&lt;/strong&gt; was deleted or did not exist. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the SSH key. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_ssh_keys**
> delete_ssh_keys(user_name=user_name, user=user)

Delete all user SSH key

Delete all SSH keys for a supplied user.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    user_name = 'user_name_example' # str | the username of the user to delete the keys for. If no username is specified, the SSH keys will be deleted for the current authenticated user. (optional)
    user = 'user_example' # str |  (optional)

    try:
        # Delete all user SSH key
        api_instance.delete_ssh_keys(user_name=user_name, user=user)
    except Exception as e:
        print("Exception when calling AuthenticationApi->delete_ssh_keys: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_name** | **str**| the username of the user to delete the keys for. If no username is specified, the SSH keys will be deleted for the current authenticated user. | [optional] 
 **user** | **str**|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The SSH keys matching the supplied &lt;strong&gt;user&lt;/strong&gt; were deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the SSH keys. This is only possible when a &lt;strong&gt;user&lt;/strong&gt; is explicitly supplied. |  -  |
**404** | No user matches the supplied &lt;strong&gt;user&lt;/strong&gt; |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **elevate_permissions_with_password**
> elevate_permissions_with_password(action_type=action_type, totp_elevation_rest_dto=totp_elevation_rest_dto)

Create elevated session with password

Elevate permissions by providing the password for the currently authenticated user. This will create an elevated session.

### Example


```python
import bitbucketclient
from .models.totp_elevation_rest_dto import TotpElevationRestDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    action_type = 'action_type_example' # str | The type of action being performed. (optional)
    totp_elevation_rest_dto = .TotpElevationRestDTO() # TotpElevationRestDTO | A request containing the password for the currently authenticated user. (optional)

    try:
        # Create elevated session with password
        api_instance.elevate_permissions_with_password(action_type=action_type, totp_elevation_rest_dto=totp_elevation_rest_dto)
    except Exception as e:
        print("Exception when calling AuthenticationApi->elevate_permissions_with_password: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **action_type** | **str**| The type of action being performed. | [optional] 
 **totp_elevation_rest_dto** | [**TotpElevationRestDTO**](TotpElevationRestDTO.md)| A request containing the password for the currently authenticated user. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | Permissions were successfully elevated. |  -  |
**400** | The user has entered an incorrect password or the requested action cannot be confirmed with a password. |  -  |
**403** | The user cannot perform the requested action. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **elevate_permissions_with_recovery_code**
> TotpRecoveryCodeDTO elevate_permissions_with_recovery_code(action_type=action_type, totp_recovery_code_dto=totp_recovery_code_dto)

Create elevated session with recovery code

Elevate permissions by providing a recovery code for the currently authenticated user. This will create an elevated session.

### Example


```python
import bitbucketclient
from .models.totp_recovery_code_dto import TotpRecoveryCodeDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    action_type = 'action_type_example' # str | The type of action being performed. (optional)
    totp_recovery_code_dto = .TotpRecoveryCodeDTO() # TotpRecoveryCodeDTO | A request containing a recovery code for the currently authenticated user. (optional)

    try:
        # Create elevated session with recovery code
        api_response = api_instance.elevate_permissions_with_recovery_code(action_type=action_type, totp_recovery_code_dto=totp_recovery_code_dto)
        print("The response of AuthenticationApi->elevate_permissions_with_recovery_code:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->elevate_permissions_with_recovery_code: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **action_type** | **str**| The type of action being performed. | [optional] 
 **totp_recovery_code_dto** | [**TotpRecoveryCodeDTO**](TotpRecoveryCodeDTO.md)| A request containing a recovery code for the currently authenticated user. | [optional] 

### Return type

[**TotpRecoveryCodeDTO**](TotpRecoveryCodeDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Permissions were successfully elevated. |  -  |
**400** | The recovery code provided was incorrect. |  -  |
**403** | The user cannot perform the requested action. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **elevate_permissions_with_totp**
> elevate_permissions_with_totp(action_type=action_type, totp_elevation_rest_dto=totp_elevation_rest_dto)

Create elevated session with TOTP

Elevate permissions by providing a TOTP code for the currently authenticated user. This will create an elevated session.

### Example


```python
import bitbucketclient
from .models.totp_elevation_rest_dto import TotpElevationRestDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    action_type = 'action_type_example' # str | The type of action being performed. (optional)
    totp_elevation_rest_dto = .TotpElevationRestDTO() # TotpElevationRestDTO | A request containing a TOTP code for the given user. (optional)

    try:
        # Create elevated session with TOTP
        api_instance.elevate_permissions_with_totp(action_type=action_type, totp_elevation_rest_dto=totp_elevation_rest_dto)
    except Exception as e:
        print("Exception when calling AuthenticationApi->elevate_permissions_with_totp: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **action_type** | **str**| The type of action being performed. | [optional] 
 **totp_elevation_rest_dto** | [**TotpElevationRestDTO**](TotpElevationRestDTO.md)| A request containing a TOTP code for the given user. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | Permissions were successfully elevated. |  -  |
**400** | One of the following error cases occurred (check the error message for more details).  - The user has entered an incorrect TOTP code - The requested action cannot be confirmed with a TOTP code - The user is not enrolled in two-step verification  |  -  |
**403** | The user cannot perform the requested action. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get**
> BasicAuthConfigEntity get()

Get basic auth configuration

Get the current configuration for blocking basic authentication requests.

### Example


```python
import bitbucketclient
from .models.basic_auth_config_entity import BasicAuthConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get basic auth configuration
        api_response = api_instance.get()
        print("The response of AuthenticationApi->get:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**BasicAuthConfigEntity**](BasicAuthConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The blocking basic authentication configuration. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_access_tokens**
> GetAllAccessTokens200Response get_all_access_tokens(project_key, start=start, limit=limit)

Get project HTTP tokens

Get all access tokens associated with the given project.

### Example


```python
import bitbucketclient
from .models.get_all_access_tokens200_response import GetAllAccessTokens200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get project HTTP tokens
        api_response = api_instance.get_all_access_tokens(project_key, start=start, limit=limit)
        print("The response of AuthenticationApi->get_all_access_tokens:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_all_access_tokens: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetAllAccessTokens200Response**](GetAllAccessTokens200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing a page of access tokens and associated details. |  -  |
**401** | The currently authenticated user is not permitted to get access tokens for this project or authentication failed. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_access_tokens1**
> GetAllAccessTokens200Response get_all_access_tokens1(project_key, repository_slug, start=start, limit=limit)

Get repository HTTP tokens

Get all access tokens associated with the given repository.

### Example


```python
import bitbucketclient
from .models.get_all_access_tokens200_response import GetAllAccessTokens200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repository HTTP tokens
        api_response = api_instance.get_all_access_tokens1(project_key, repository_slug, start=start, limit=limit)
        print("The response of AuthenticationApi->get_all_access_tokens1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_all_access_tokens1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetAllAccessTokens200Response**](GetAllAccessTokens200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing a page of access tokens and associated details. |  -  |
**401** | The currently authenticated user is not permitted to get access tokens for this repository or authentication failed. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_access_tokens2**
> GetAllAccessTokens200Response get_all_access_tokens2(user_slug, start=start, limit=limit)

Get personal HTTP tokens

Get all access tokens associated with the given user.

### Example


```python
import bitbucketclient
from .models.get_all_access_tokens200_response import GetAllAccessTokens200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    user_slug = 'user_slug_example' # str | The user slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get personal HTTP tokens
        api_response = api_instance.get_all_access_tokens2(user_slug, start=start, limit=limit)
        print("The response of AuthenticationApi->get_all_access_tokens2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_all_access_tokens2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_slug** | **str**| The user slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetAllAccessTokens200Response**](GetAllAccessTokens200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing a page of access tokens and associated details. |  -  |
**401** | The currently authenticated user is not permitted to get access tokens on behalf of this user or authentication failed. |  -  |
**404** | The specified user does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_by_id**
> RestAccessToken get_by_id(project_key, token_id)

Get HTTP token by ID

Get the access token identified by the given ID.

### Example


```python
import bitbucketclient
from .models.rest_access_token import RestAccessToken
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    token_id = 'token_id_example' # str | The token id.

    try:
        # Get HTTP token by ID
        api_response = api_instance.get_by_id(project_key, token_id)
        print("The response of AuthenticationApi->get_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **token_id** | **str**| The token id. | 

### Return type

[**RestAccessToken**](RestAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the access token and associated details. |  -  |
**401** | The currently authenticated user is not permitted to get access tokens on behalf of this user or authentication failed. |  -  |
**404** | The specified user or token does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_by_id1**
> RestAccessToken get_by_id1(project_key, token_id, repository_slug)

Get HTTP token by ID

Get the access token identified by the given ID.

### Example


```python
import bitbucketclient
from .models.rest_access_token import RestAccessToken
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    token_id = 'token_id_example' # str | The token id.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get HTTP token by ID
        api_response = api_instance.get_by_id1(project_key, token_id, repository_slug)
        print("The response of AuthenticationApi->get_by_id1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_by_id1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **token_id** | **str**| The token id. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestAccessToken**](RestAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the access token and associated details. |  -  |
**401** | The currently authenticated user is not permitted to get access tokens on behalf of this user or authentication failed. |  -  |
**404** | The specified user or token does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_by_id2**
> RestAccessToken get_by_id2(token_id, user_slug)

Get HTTP token by ID

Get the access token identified by the given ID.

### Example


```python
import bitbucketclient
from .models.rest_access_token import RestAccessToken
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    token_id = 'token_id_example' # str | The token id.
    user_slug = 'user_slug_example' # str | The user slug.

    try:
        # Get HTTP token by ID
        api_response = api_instance.get_by_id2(token_id, user_slug)
        print("The response of AuthenticationApi->get_by_id2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_by_id2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **token_id** | **str**| The token id. | 
 **user_slug** | **str**| The user slug. | 

### Return type

[**RestAccessToken**](RestAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the access token and associated details. |  -  |
**401** | The currently authenticated user is not permitted to get access tokens on behalf of this user or authentication failed. |  -  |
**404** | The specified user or token does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_captcha_data**
> CaptchaDataEntity get_captcha_data()

Get CAPTCHA challenge

Provides data for a CAPTCHA challenge.

### Example


```python
import bitbucketclient
from .models.captcha_data_entity import CaptchaDataEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get CAPTCHA challenge
        api_response = api_instance.get_captcha_data()
        print("The response of AuthenticationApi->get_captcha_data:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_captcha_data: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**CaptchaDataEntity**](CaptchaDataEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The CAPTCHA challenge |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_config**
> SsoConfigEntity get_config()

Get SSO configuration

Returns the SSO configuration.

### Example


```python
import bitbucketclient
from .models.sso_config_entity import SsoConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get SSO configuration
        api_response = api_instance.get_config()
        print("The response of AuthenticationApi->get_config:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_config: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**SsoConfigEntity**](SsoConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The SSO configuration |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_elevated_permission_status**
> get_elevated_permission_status(action_type=action_type)

Get elevated session status

Checks the state of an elevated session for the currently authenticated user.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    action_type = 'action_type_example' # str | The type of action being performed. (optional)

    try:
        # Get elevated session status
        api_instance.get_elevated_permission_status(action_type=action_type)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_elevated_permission_status: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **action_type** | **str**| The type of action being performed. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An elevated session exists for the currently authenticated user. |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_for_project**
> RestSshAccessKey get_for_project(project_key, key_id)

Get project SSH key

Retrieves the access key for the SSH key with id <code>keyId</code> on the project identified in the URL.

### Example


```python
import bitbucketclient
from .models.rest_ssh_access_key import RestSshAccessKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    key_id = 'key_id_example' # str | The key id

    try:
        # Get project SSH key
        api_response = api_instance.get_for_project(project_key, key_id)
        print("The response of AuthenticationApi->get_for_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_for_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **key_id** | **str**| The key id | 

### Return type

[**RestSshAccessKey**](RestSshAccessKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The access key for the repository and SSH key with ID &lt;code&gt;keyId&lt;/code&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the access keys for this repository. |  -  |
**404** | The specified repository or key does not exist or the key does not have access on the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_for_projects**
> get_for_projects(key_id)

Get project SSH keys

Retrieves all project-related access keys for the SSH key with id <code>keyId</code>. If the current user is not an admin any of the projects the key provides access to, none are returned.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    key_id = 56 # int | 

    try:
        # Get project SSH keys
        api_instance.get_for_projects(key_id)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_for_projects: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **key_id** | **int**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The SSH key with ID &lt;code&gt;keyId&lt;/code&gt;. |  -  |
**404** | The specified key does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_for_repositories**
> get_for_repositories(key_id, with_restrictions=with_restrictions)

Get repository SSH key

Retrieves all repository-related access keys for the SSH key with id <code>keyId</code>. If the current user is not an admin of any of the projects the key provides access to, none are returned.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    key_id = 'key_id_example' # str | The key id
    with_restrictions = 'with_restrictions_example' # str | Include the readOnly field. The `readOnly` field is contextual for the user making the request. `readOnly` returns true if there is a restriction and the user does not have`PROJECT_ADMIN` access for the repository the key is associated with. (optional)

    try:
        # Get repository SSH key
        api_instance.get_for_repositories(key_id, with_restrictions=with_restrictions)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_for_repositories: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **key_id** | **str**| The key id | 
 **with_restrictions** | **str**| Include the readOnly field. The &#x60;readOnly&#x60; field is contextual for the user making the request. &#x60;readOnly&#x60; returns true if there is a restriction and the user does not have&#x60;PROJECT_ADMIN&#x60; access for the repository the key is associated with. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The SSH key with ID &lt;code&gt;keyId&lt;/code&gt;. |  -  |
**404** | The specified key does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_for_repository**
> RestSshAccessKey get_for_repository(project_key, key_id, repository_slug)

Get repository SSH key

Retrieves the access key for the SSH key with id <code>keyId</code> on the repository identified in the URL.

### Example


```python
import bitbucketclient
from .models.rest_ssh_access_key import RestSshAccessKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    key_id = 'key_id_example' # str | The key id
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get repository SSH key
        api_response = api_instance.get_for_repository(project_key, key_id, repository_slug)
        print("The response of AuthenticationApi->get_for_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_for_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **key_id** | **str**| The key id | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestSshAccessKey**](RestSshAccessKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The access key for the repository and SSH key with ID &lt;code&gt;keyId&lt;/code&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the access keys for this repository. |  -  |
**404** | The specified repository or key does not exist or the key does not have access on the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_for_repository1**
> GetForRepository1200Response get_for_repository1(project_key, repository_slug, filter=filter, effective=effective, minimum_permission=minimum_permission, permission=permission, start=start, limit=limit)

Get repository SSH keys

Retrieves the access keys for the repository identified in the URL.

### Example


```python
import bitbucketclient
from .models.get_for_repository1200_response import GetForRepository1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    filter = 'filter_example' # str | If specified only SSH access keys with a label prefixed with the supplied string will be returned (optional)
    effective = 'effective_example' # str | Controls whether SSH access keys configured at the project level should be included in the results or not. When set to <code>true</code> all keys that have <em>access</em> to the repository (including project level keys) are included in the results. When set to <code>false</code>, only access keys configured for the specified <code>repository</code> are considered. Default is <code>false</code>. (optional)
    minimum_permission = 'minimum_permission_example' # str | If specified only SSH access keys with at least the supplied permission will be returned. Default is <code>Permission.REPO_READ</code>. (optional)
    permission = 'permission_example' # str |  (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repository SSH keys
        api_response = api_instance.get_for_repository1(project_key, repository_slug, filter=filter, effective=effective, minimum_permission=minimum_permission, permission=permission, start=start, limit=limit)
        print("The response of AuthenticationApi->get_for_repository1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_for_repository1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **filter** | **str**| If specified only SSH access keys with a label prefixed with the supplied string will be returned | [optional] 
 **effective** | **str**| Controls whether SSH access keys configured at the project level should be included in the results or not. When set to &lt;code&gt;true&lt;/code&gt; all keys that have &lt;em&gt;access&lt;/em&gt; to the repository (including project level keys) are included in the results. When set to &lt;code&gt;false&lt;/code&gt;, only access keys configured for the specified &lt;code&gt;repository&lt;/code&gt; are considered. Default is &lt;code&gt;false&lt;/code&gt;. | [optional] 
 **minimum_permission** | **str**| If specified only SSH access keys with at least the supplied permission will be returned. Default is &lt;code&gt;Permission.REPO_READ&lt;/code&gt;. | [optional] 
 **permission** | **str**|  | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetForRepository1200Response**](GetForRepository1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A single page of access keys for the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the access keys for this repository |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_idp**
> IdpConfigEntity get_idp(id)

Get IdP configuration

Returns the configuration for the IdP that matches the given ID.

### Example


```python
import bitbucketclient
from .models.idp_config_entity import IdpConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    id = 'id_example' # str | The ID of the IdP

    try:
        # Get IdP configuration
        api_response = api_instance.get_idp(id)
        print("The response of AuthenticationApi->get_idp:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_idp: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**| The ID of the IdP | 

### Return type

[**IdpConfigEntity**](IdpConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The configuration for the given IdP. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_idps**
> GetIdps200Response get_idps(start=start, limit=limit)

Get all configured IdPs

Returns a page of configured IdPs. 

This endpoint makes no guarantees to ordering besides the ordering being consistent.

### Example


```python
import bitbucketclient
from .models.get_idps200_response import GetIdps200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 50 # float | Number of items to return. If not passed, a page size of 50 is used. A limit of -1 means that the request will fetch all results. (optional)

    try:
        # Get all configured IdPs
        api_response = api_instance.get_idps(start=start, limit=limit)
        print("The response of AuthenticationApi->get_idps:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_idps: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 50 is used. A limit of -1 means that the request will fetch all results. | [optional] 

### Return type

[**GetIdps200Response**](GetIdps200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of configured IdPs. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_jit_provisioned_users**
> JitUserEntity get_jit_provisioned_users()

Get all JIT provisioned users

Returns a list of all the users created by JIT (Just-in-time) provisioning.

Just-in-time user provisioning (JIT provisioning) allows users to be created and updated automatically when they log in through SAML SSO or OpenID Connect (OIDC) SSO.

### Example


```python
import bitbucketclient
from .models.jit_user_entity import JitUserEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get all JIT provisioned users
        api_response = api_instance.get_jit_provisioned_users()
        print("The response of AuthenticationApi->get_jit_provisioned_users:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_jit_provisioned_users: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**JitUserEntity**](JitUserEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A list of JIT users |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_login_options**
> GetLoginOptions200Response get_login_options(start=start, limit=limit)

Get available login options

Returns a list of available login options, which contains details about the metadata required for the login page.

Only enabled login options will be returned. Login options can either be the native login form or the configured IdPs.

### Example


```python
import bitbucketclient
from .models.get_login_options200_response import GetLoginOptions200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 50 # float | Number of items to return. If not passed, a page size of 50 is used. A limit of -1 means that the request will fetch all results. (optional)

    try:
        # Get available login options
        api_response = api_instance.get_login_options(start=start, limit=limit)
        print("The response of AuthenticationApi->get_login_options:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_login_options: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 50 is used. A limit of -1 means that the request will fetch all results. | [optional] 

### Return type

[**GetLoginOptions200Response**](GetLoginOptions200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A list of login options |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_ssh_key**
> RestSshKey get_ssh_key(key_id)

Get SSH key for user by keyId

Retrieve an SSH key by keyId

The authenticated user must have <strong>ADMIN</strong> permission or higher to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_ssh_key import RestSshKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    key_id = 'key_id_example' # str | the ID of the key to retrieve.

    try:
        # Get SSH key for user by keyId
        api_response = api_instance.get_ssh_key(key_id)
        print("The response of AuthenticationApi->get_ssh_key:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_ssh_key: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **key_id** | **str**| the ID of the key to retrieve. | 

### Return type

[**RestSshKey**](RestSshKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | An SSH key. |  -  |
**401** | The currently authenticated user has insufficient permissionsto retrieve the SSH key. This is only possible when a&lt;strong&gt;keyId&lt;/strong&gt; is explicitly supplied. |  -  |
**404** | No SSH key matches the supplied &lt;strong&gt;keyId&lt;/strong&gt; |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_ssh_keys**
> GetSshKeys200Response get_ssh_keys(user_name=user_name, user=user, start=start, limit=limit)

Get SSH keys for user

Retrieve a page of SSH keys.

### Example


```python
import bitbucketclient
from .models.get_ssh_keys200_response import GetSshKeys200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    user_name = 'user_name_example' # str | the username of the user to retrieve the keys for. If no username is specified, the SSH keys will be retrieved for the current authenticated user. (optional)
    user = 'user_example' # str |  (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get SSH keys for user
        api_response = api_instance.get_ssh_keys(user_name=user_name, user=user, start=start, limit=limit)
        print("The response of AuthenticationApi->get_ssh_keys:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_ssh_keys: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_name** | **str**| the username of the user to retrieve the keys for. If no username is specified, the SSH keys will be retrieved for the current authenticated user. | [optional] 
 **user** | **str**|  | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetSshKeys200Response**](GetSshKeys200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of SSH keys. |  -  |
**401** | The currently authenticated user has insufficient permissionsto retrieve the SSH keys. This is only possible when a&lt;strong&gt;user&lt;/strong&gt; is explicitly supplied. |  -  |
**404** | No user matches the supplied &lt;strong&gt;user&lt;/strong&gt; |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_ssh_keys_for_project**
> GetForRepository1200Response get_ssh_keys_for_project(project_key, filter=filter, permission=permission, start=start, limit=limit)

Get SSH key

Retrieves the access keys for the project identified in the URL.

### Example


```python
import bitbucketclient
from .models.get_for_repository1200_response import GetForRepository1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    filter = 'filter_example' # str | If specified only SSH access keys with a label prefixed with the supplied string will be returned. (optional)
    permission = 'permission_example' # str | If specified only SSH access keys with at least the supplied permission will be returned Default is PROJECT_READ. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get SSH key
        api_response = api_instance.get_ssh_keys_for_project(project_key, filter=filter, permission=permission, start=start, limit=limit)
        print("The response of AuthenticationApi->get_ssh_keys_for_project:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_ssh_keys_for_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **filter** | **str**| If specified only SSH access keys with a label prefixed with the supplied string will be returned. | [optional] 
 **permission** | **str**| If specified only SSH access keys with at least the supplied permission will be returned Default is PROJECT_READ. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetForRepository1200Response**](GetForRepository1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A single page of access keys associated with the project. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the access keys for this project. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_sso_management_status**
> SsoManagementStatusDTO get_sso_management_status()

Get SSO management status

Retrieves the status of the SSO management for the currently authenticated user.

### Example


```python
import bitbucketclient
from .models.sso_management_status_dto import SsoManagementStatusDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get SSO management status
        api_response = api_instance.get_sso_management_status()
        print("The response of AuthenticationApi->get_sso_management_status:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_sso_management_status: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**SsoManagementStatusDTO**](SsoManagementStatusDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successfully retrieved SSO management status |  -  |
**401** | Failed to retrieve SSO management status due to unauthenticated user |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_status**
> StatusDTO get_status()

Get two-step verification status

Retrieves the status of two-step verification for the currently authenticated user.

### Example


```python
import bitbucketclient
from .models.status_dto import StatusDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get two-step verification status
        api_response = api_instance.get_status()
        print("The response of AuthenticationApi->get_status:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->get_status: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**StatusDTO**](StatusDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Successfully retrieved the two-step verification status. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **put**
> put(basic_auth_config_entity=basic_auth_config_entity)

Update basic auth configuration

Store a new configuration for blocking basic authentication requests.

### Example


```python
import bitbucketclient
from .models.basic_auth_config_entity import BasicAuthConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    basic_auth_config_entity = .BasicAuthConfigEntity() # BasicAuthConfigEntity | A request containing the new basic authentication configuration. (optional)

    try:
        # Update basic auth configuration
        api_instance.put(basic_auth_config_entity=basic_auth_config_entity)
    except Exception as e:
        print("Exception when calling AuthenticationApi->put: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **basic_auth_config_entity** | [**BasicAuthConfigEntity**](BasicAuthConfigEntity.md)| A request containing the new basic authentication configuration. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The basic authentication blocking configuration was successfully created or updated. |  -  |
**409** | Unable to update the basic authentication blocking configuration as another update is currently being performed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_idp**
> IdpConfigEntity remove_idp(id)

Delete IdP configuration

Removes the configuration for the IdP that matches the given ID.

### Example


```python
import bitbucketclient
from .models.idp_config_entity import IdpConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    id = 'id_example' # str | The ID of the IdP

    try:
        # Delete IdP configuration
        api_response = api_instance.remove_idp(id)
        print("The response of AuthenticationApi->remove_idp:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->remove_idp: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**| The ID of the IdP | 

### Return type

[**IdpConfigEntity**](IdpConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The IdP configuration was successfully deleted. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **revoke_for_project**
> revoke_for_project(project_key, key_id)

Revoke project SSH key

Remove an existing access key for the project identified in the URL. If the same SSH key is used as an access key for multiple projects or repositories, only the access to the project identified in the URL will be revoked.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    key_id = 'key_id_example' # str | The key id

    try:
        # Revoke project SSH key
        api_instance.revoke_for_project(project_key, key_id)
    except Exception as e:
        print("Exception when calling AuthenticationApi->revoke_for_project: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **key_id** | **str**| The key id | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The access key was deleted (or none was found matching the given id). |  -  |
**401** | The currently authenticated user has insufficient permissions to remove access keys for this project. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **revoke_for_repository**
> revoke_for_repository(project_key, key_id, repository_slug)

Revoke repository SSH key

Remove an existing access key for the repository identified in the URL. If the same SSH key is used as an access key for multiple projects or repositories, only the access to the repository identified in the URL will be revoked.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    key_id = 'key_id_example' # str | The key id
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Revoke repository SSH key
        api_instance.revoke_for_repository(project_key, key_id, repository_slug)
    except Exception as e:
        print("Exception when calling AuthenticationApi->revoke_for_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **key_id** | **str**| The key id | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The access key was deleted (or none was found matching the given id). |  -  |
**401** | The currently authenticated user has insufficient permissions to remove access keys for this repository |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **revoke_many**
> revoke_many(key_id, revoke_many_request=revoke_many_request)

Revoke project SSH key

Remove an existing access key for the projects and repositories in the submitted entity. If the same SSH key is used as an access key for multiple projects or repositories not supplied, only the access to the projects or repositories identified will be revoked.

### Example


```python
import bitbucketclient
from .models.revoke_many_request import RevokeManyRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    key_id = 'key_id_example' # str | The identifier of the SSH key
    revoke_many_request = .RevokeManyRequest() # RevokeManyRequest |  (optional)

    try:
        # Revoke project SSH key
        api_instance.revoke_many(key_id, revoke_many_request=revoke_many_request)
    except Exception as e:
        print("Exception when calling AuthenticationApi->revoke_many: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **key_id** | **str**| The identifier of the SSH key | 
 **revoke_many_request** | [**RevokeManyRequest**](RevokeManyRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The access keys were deleted (or none was found matching the given id and repositories or projects). |  -  |
**401** | The currently authenticated user has insufficient permissions to remove access keys for one or more of the specified projects or repositories. |  -  |
**404** | On or more of the specified repositories or projects does not exist or the key itself does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **rotate_recover_code**
> TotpRecoveryCodeDTO rotate_recover_code()

Rotate recovery code

Rotates the recovery code for the currently authentication user.

### Example


```python
import bitbucketclient
from .models.totp_recovery_code_dto import TotpRecoveryCodeDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Rotate recovery code
        api_response = api_instance.rotate_recover_code()
        print("The response of AuthenticationApi->rotate_recover_code:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->rotate_recover_code: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**TotpRecoveryCodeDTO**](TotpRecoveryCodeDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The recovery code has been successfully rotated. |  -  |
**400** | The request has failed due to the user not being enrolled in two-step verification. |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **ssh_settings**
> RestSshSettings ssh_settings()

Get SSH settings

Gets the SSH settings from the upstream.

### Example


```python
import bitbucketclient
from .models.rest_ssh_settings import RestSshSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Get SSH settings
        api_response = api_instance.ssh_settings()
        print("The response of AuthenticationApi->ssh_settings:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->ssh_settings: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**RestSshSettings**](RestSshSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The ssh settings from upstream |  -  |
**401** | The request was not authenticated |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_enforced_enrollment**
> TotpUserEnrollmentDTO start_enforced_enrollment(conversation_dto=conversation_dto)

Start enforced enrollment in 2SV

Start or resume enforced enrollment in two-step verification by returning the conversation details.

There are two ways to enroll in two-step verification: voluntary and enforced. Enrollment is a two-step process. First, the user starts the enrollment process via <code>/start-voluntary-enrollment</code> or <code>/start-enforced-enrollment</code>. Second and final step is to complete the enrollment via <code>/complete-voluntary-enrollment</code> or <code>/complete-enforced-enrollment</code>. In the case of enforced enrollment, the conversation is started at the time of login via <code>/authenticate</code>.

### Example


```python
import bitbucketclient
from .models.conversation_dto import ConversationDTO
from .models.totp_user_enrollment_dto import TotpUserEnrollmentDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    conversation_dto = .ConversationDTO() # ConversationDTO |  (optional)

    try:
        # Start enforced enrollment in 2SV
        api_response = api_instance.start_enforced_enrollment(conversation_dto=conversation_dto)
        print("The response of AuthenticationApi->start_enforced_enrollment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->start_enforced_enrollment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **conversation_dto** | [**ConversationDTO**](ConversationDTO.md)|  | [optional] 

### Return type

[**TotpUserEnrollmentDTO**](TotpUserEnrollmentDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A conversation has successfully started. |  -  |
**400** | The requested enrollment conversation cannot be found or is not valid in the requested context. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_enrollment_update**
> TotpUserEnrollmentDTO start_enrollment_update()

Start authentication app update for 2SV

Start the process of changing the authentication app used for two-step verification by creating a conversation.

### Example


```python
import bitbucketclient
from .models.totp_user_enrollment_dto import TotpUserEnrollmentDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Start authentication app update for 2SV
        api_response = api_instance.start_enrollment_update()
        print("The response of AuthenticationApi->start_enrollment_update:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->start_enrollment_update: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**TotpUserEnrollmentDTO**](TotpUserEnrollmentDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A conversation has successfully started. |  -  |
**400** | The user is not enrolled in two-step verification using TOTP. |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **start_voluntary_enrollment**
> TotpUserEnrollmentDTO start_voluntary_enrollment()

Start voluntary enrollment in 2SV

Start voluntary enrollment in two-step verification by creating a conversation.

There are two ways to enroll in two-step verification: voluntary and enforced. Enrollment is a two-step process. First, the user starts the enrollment process via <code>/start-voluntary-enrollment</code> or <code>/start-enforced-enrollment</code>. Second and final step is to complete the enrollment via <code>/complete-voluntary-enrollment</code> or <code>/complete-enforced-enrollment</code>. In the case of enforced enrollment, the conversation is started at the time of login via <code>/authenticate</code>.

### Example


```python
import bitbucketclient
from .models.totp_user_enrollment_dto import TotpUserEnrollmentDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Start voluntary enrollment in 2SV
        api_response = api_instance.start_voluntary_enrollment()
        print("The response of AuthenticationApi->start_voluntary_enrollment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->start_voluntary_enrollment: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**TotpUserEnrollmentDTO**](TotpUserEnrollmentDTO.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A conversation has successfully started. |  -  |
**400** | The user is already enrolled in two-step verification using TOTP. |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unenroll**
> unenroll()

Uneroll current user from two-step verification

Unenroll the currently authenticated user from two-step verification.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)

    try:
        # Uneroll current user from two-step verification
        api_instance.unenroll()
    except Exception as e:
        print("Exception when calling AuthenticationApi->unenroll: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | User successfully unenrolled from two-step verification. |  -  |
**400** | No enrollment found for the currently authenticated user. |  -  |
**401** | The currently authenticated user requires an elevated session to perform this request. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unenroll_user**
> unenroll_user(user_name, totp_elevation_rest_dto=totp_elevation_rest_dto)

Unenroll specific user from two-step verification

Unenroll a user from two-step verification specified by the given username.

### Example


```python
import bitbucketclient
from .models.totp_elevation_rest_dto import TotpElevationRestDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    user_name = 'user_name_example' # str | username
    totp_elevation_rest_dto = .TotpElevationRestDTO() # TotpElevationRestDTO | A request containing a TOTP code for the given user. (optional)

    try:
        # Unenroll specific user from two-step verification
        api_instance.unenroll_user(user_name, totp_elevation_rest_dto=totp_elevation_rest_dto)
    except Exception as e:
        print("Exception when calling AuthenticationApi->unenroll_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_name** | **str**| username | 
 **totp_elevation_rest_dto** | [**TotpElevationRestDTO**](TotpElevationRestDTO.md)| A request containing a TOTP code for the given user. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | User successfully unenrolled from two-step verification. |  -  |
**400** | No enrollment found for the specified user. |  -  |
**401** | The user has entered an invalid TOTP code. |  -  |
**403** | The user cannot unenroll themselves. |  -  |
**404** | No user matches the supplied &lt;strong&gt;username&lt;/strong&gt;. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_access_token**
> RestAccessToken update_access_token(project_key, token_id, rest_access_token_request=rest_access_token_request)

Update HTTP token

Modify an access token according to the given request. Any fields not specified will not be altered.

### Example


```python
import bitbucketclient
from .models.rest_access_token import RestAccessToken
from .models.rest_access_token_request import RestAccessTokenRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    token_id = 'token_id_example' # str | The token id.
    rest_access_token_request = .RestAccessTokenRequest() # RestAccessTokenRequest | The request containing the details of the access token to modify (optional)

    try:
        # Update HTTP token
        api_response = api_instance.update_access_token(project_key, token_id, rest_access_token_request=rest_access_token_request)
        print("The response of AuthenticationApi->update_access_token:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_access_token: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **token_id** | **str**| The token id. | 
 **rest_access_token_request** | [**RestAccessTokenRequest**](RestAccessTokenRequest.md)| The request containing the details of the access token to modify | [optional] 

### Return type

[**RestAccessToken**](RestAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the updated access token and associated details. |  -  |
**400** | One of the provided permission levels are unknown. |  -  |
**401** | The currently authenticated user is not permitted to update an access token on behalf of this user or authentication failed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_access_token1**
> RestAccessToken update_access_token1(project_key, token_id, repository_slug, rest_access_token_request=rest_access_token_request)

Update HTTP token

Modify an access token according to the given request. Any fields not specified will not be altered.

### Example


```python
import bitbucketclient
from .models.rest_access_token import RestAccessToken
from .models.rest_access_token_request import RestAccessTokenRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    token_id = 'token_id_example' # str | The token id.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_access_token_request = .RestAccessTokenRequest() # RestAccessTokenRequest | The request containing the details of the access token to modify (optional)

    try:
        # Update HTTP token
        api_response = api_instance.update_access_token1(project_key, token_id, repository_slug, rest_access_token_request=rest_access_token_request)
        print("The response of AuthenticationApi->update_access_token1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_access_token1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **token_id** | **str**| The token id. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_access_token_request** | [**RestAccessTokenRequest**](RestAccessTokenRequest.md)| The request containing the details of the access token to modify | [optional] 

### Return type

[**RestAccessToken**](RestAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the updated access token and associated details. |  -  |
**400** | One of the provided permission levels are unknown. |  -  |
**401** | The currently authenticated user is not permitted to update an access token on behalf of this user or authentication failed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_access_token2**
> RestAccessToken update_access_token2(token_id, user_slug, rest_access_token_request=rest_access_token_request)

Update HTTP token

Modify an access token according to the given request. Any fields not specified will not be altered.

### Example


```python
import bitbucketclient
from .models.rest_access_token import RestAccessToken
from .models.rest_access_token_request import RestAccessTokenRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    token_id = 'token_id_example' # str | The token id.
    user_slug = 'user_slug_example' # str | The user slug.
    rest_access_token_request = .RestAccessTokenRequest() # RestAccessTokenRequest | The request containing the details of the access token to modify (optional)

    try:
        # Update HTTP token
        api_response = api_instance.update_access_token2(token_id, user_slug, rest_access_token_request=rest_access_token_request)
        print("The response of AuthenticationApi->update_access_token2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_access_token2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **token_id** | **str**| The token id. | 
 **user_slug** | **str**| The user slug. | 
 **rest_access_token_request** | [**RestAccessTokenRequest**](RestAccessTokenRequest.md)| The request containing the details of the access token to modify | [optional] 

### Return type

[**RestAccessToken**](RestAccessToken.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the updated access token and associated details. |  -  |
**400** | One of the provided permission levels are unknown. |  -  |
**401** | The currently authenticated user is not permitted to update an access token on behalf of this user or authentication failed. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_config**
> SsoConfigEntity update_config(sso_config_entity=sso_config_entity)

Update SSO configuration

Update the SSO configuration.

### Example


```python
import bitbucketclient
from .models.sso_config_entity import SsoConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    sso_config_entity = .SsoConfigEntity() # SsoConfigEntity | A request containing the SSO configuration to update. (optional)

    try:
        # Update SSO configuration
        api_response = api_instance.update_config(sso_config_entity=sso_config_entity)
        print("The response of AuthenticationApi->update_config:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_config: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **sso_config_entity** | [**SsoConfigEntity**](SsoConfigEntity.md)| A request containing the SSO configuration to update. | [optional] 

### Return type

[**SsoConfigEntity**](SsoConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated SSO Configuration |  -  |
**400** | The provided SSO configuration was invalid. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_idp**
> IdpConfigEntity update_idp(id, idp_config_entity=idp_config_entity)

Update IdP configuration

Updates the configuration for the IdP that matches the given ID. 

Only the provided properties will be applied to the IdP configuration.

### Example


```python
import bitbucketclient
from .models.idp_config_entity import IdpConfigEntity
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    id = 'id_example' # str | The ID of the IdP
    idp_config_entity = .IdpConfigEntity() # IdpConfigEntity | A request containing the IdP configuration to update. The ID must either be null or equal to the ID specified in the path. (optional)

    try:
        # Update IdP configuration
        api_response = api_instance.update_idp(id, idp_config_entity=idp_config_entity)
        print("The response of AuthenticationApi->update_idp:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_idp: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**| The ID of the IdP | 
 **idp_config_entity** | [**IdpConfigEntity**](IdpConfigEntity.md)| A request containing the IdP configuration to update. The ID must either be null or equal to the ID specified in the path. | [optional] 

### Return type

[**IdpConfigEntity**](IdpConfigEntity.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated configuration for the given IdP. |  -  |
**400** | The provided IdP configuration was either incorrect or invalid. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_permission**
> RestSshAccessKey update_permission(project_key, key_id, permission)

Update project SSH key permission

Updates the permission granted to the specified SSH key to the project identified in the URL.

### Example


```python
import bitbucketclient
from .models.rest_ssh_access_key import RestSshAccessKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    key_id = 'key_id_example' # str | The newly created access key
    permission = 'permission_example' # str | The new permission to be granted to the SSH key

    try:
        # Update project SSH key permission
        api_response = api_instance.update_permission(project_key, key_id, permission)
        print("The response of AuthenticationApi->update_permission:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_permission: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **key_id** | **str**| The newly created access key | 
 **permission** | **str**| The new permission to be granted to the SSH key | 

### Return type

[**RestSshAccessKey**](RestSshAccessKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly created access key. |  -  |
**401** | The currently authenticated user has insufficient permissions on the project to edit its access keys. |  -  |
**404** | The specified project does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_permission1**
> RestSshAccessKey update_permission1(project_key, key_id, permission, repository_slug)

Update repository SSH key permission

Updates the permission granted to the specified SSH key to the repository identified in the URL.

### Example


```python
import bitbucketclient
from .models.rest_ssh_access_key import RestSshAccessKey
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    project_key = 'project_key_example' # str | The project key
    key_id = 'key_id_example' # str | The newly created access key
    permission = 'permission_example' # str | The new permission to be granted to the SSH key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Update repository SSH key permission
        api_response = api_instance.update_permission1(project_key, key_id, permission, repository_slug)
        print("The response of AuthenticationApi->update_permission1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AuthenticationApi->update_permission1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **key_id** | **str**| The newly created access key | 
 **permission** | **str**| The new permission to be granted to the SSH key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestSshAccessKey**](RestSshAccessKey.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly created access key. |  -  |
**401** | The currently authenticated user has insufficient permissions on the repository to edit its access keys. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **verify_code**
> verify_code(totp_code_verification_dto=totp_code_verification_dto)

Authenticate using TOTP code

Authenticate as the given user using a TOTP code.

### Example


```python
import bitbucketclient
from .models.totp_code_verification_dto import TotpCodeVerificationDTO
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .AuthenticationApi(api_client)
    totp_code_verification_dto = .TotpCodeVerificationDTO() # TotpCodeVerificationDTO | A request containing a TOTP code for the given user. (optional)

    try:
        # Authenticate using TOTP code
        api_instance.verify_code(totp_code_verification_dto=totp_code_verification_dto)
    except Exception as e:
        print("Exception when calling AuthenticationApi->verify_code: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **totp_code_verification_dto** | [**TotpCodeVerificationDTO**](TotpCodeVerificationDTO.md)| A request containing a TOTP code for the given user. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user was successfully logged in. |  -  |
**400** | The requested conversation cannot be found or is not valid in the requested context. |  -  |
**401** | The TOTP code provided was incorrect. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

