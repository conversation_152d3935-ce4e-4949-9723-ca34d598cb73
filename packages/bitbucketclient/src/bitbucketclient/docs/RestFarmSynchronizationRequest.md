# RestFarmSynchronizationRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**attempt** | **int** |  | [optional] 
**created_at** | **str** |  | [optional] 
**external_repo_id** | **str** |  | [optional] 
**type** | **str** |  | [optional] 

## Example

```python
from .models.rest_farm_synchronization_request import RestFarmSynchronizationRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RestFarmSynchronizationRequest from a JSON string
rest_farm_synchronization_request_instance = RestFarmSynchronizationRequest.from_json(json)
# print the JSON string representation of the object
print(RestFarmSynchronizationRequest.to_json())

# convert the object into a dict
rest_farm_synchronization_request_dict = rest_farm_synchronization_request_instance.to_dict()
# create an instance of RestFarmSynchronizationRequest from a dict
rest_farm_synchronization_request_from_dict = RestFarmSynchronizationRequest.from_dict(rest_farm_synchronization_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


