# RestJob


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**end_date** | **int** |  | [optional] 
**id** | **int** |  | [optional] 
**initiator** | [**RestCommentAnchorPullRequestAuthorUser**](RestCommentAnchorPullRequestAuthorUser.md) |  | [optional] 
**node_id** | **str** |  | [optional] 
**progress** | [**RestJobProgress**](RestJobProgress.md) |  | [optional] 
**start_date** | **int** |  | [optional] 
**state** | **str** |  | [optional] 
**type** | **str** |  | [optional] 
**updated_date** | **int** |  | [optional] 

## Example

```python
from .models.rest_job import RestJob

# TODO update the JSON string below
json = "{}"
# create an instance of RestJob from a JSON string
rest_job_instance = RestJob.from_json(json)
# print the JSON string representation of the object
print(RestJob.to_json())

# convert the object into a dict
rest_job_dict = rest_job_instance.to_dict()
# create an instance of RestJob from a dict
rest_job_from_dict = RestJob.from_dict(rest_job_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


