# RestSetInsightReportRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**coverage_provider_key** | **str** |  | [optional] 
**created_date** | **int** |  | [optional] 
**data** | [**List[RestInsightReportData]**](RestInsightReportData.md) |  | 
**details** | **str** |  | [optional] 
**link** | **str** |  | [optional] 
**logo_url** | **str** |  | [optional] 
**reporter** | **str** |  | [optional] 
**result** | **str** |  | [optional] 
**title** | **str** |  | 

## Example

```python
from .models.rest_set_insight_report_request import RestSetInsightReportRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RestSetInsightReportRequest from a JSON string
rest_set_insight_report_request_instance = RestSetInsightReportRequest.from_json(json)
# print the JSON string representation of the object
print(RestSetInsightReportRequest.to_json())

# convert the object into a dict
rest_set_insight_report_request_dict = rest_set_insight_report_request_instance.to_dict()
# create an instance of RestSetInsightReportRequest from a dict
rest_set_insight_report_request_from_dict = RestSetInsightReportRequest.from_dict(rest_set_insight_report_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


