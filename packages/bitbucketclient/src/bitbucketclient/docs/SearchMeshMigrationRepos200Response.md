# SearchMeshMigrationRepos200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**is_last_page** | **bool** |  | [optional] 
**limit** | **float** |  | [optional] 
**next_page_start** | **int** |  | [optional] 
**size** | **float** |  | [optional] 
**start** | **int** |  | [optional] 
**values** | [**List[RestMigrationRepository]**](RestMigrationRepository.md) |  | [optional] 

## Example

```python
from .models.search_mesh_migration_repos200_response import SearchMeshMigrationRepos200Response

# TODO update the JSON string below
json = "{}"
# create an instance of SearchMeshMigrationRepos200Response from a JSON string
search_mesh_migration_repos200_response_instance = SearchMeshMigrationRepos200Response.from_json(json)
# print the JSON string representation of the object
print(SearchMeshMigrationRepos200Response.to_json())

# convert the object into a dict
search_mesh_migration_repos200_response_dict = search_mesh_migration_repos200_response_instance.to_dict()
# create an instance of SearchMeshMigrationRepos200Response from a dict
search_mesh_migration_repos200_response_from_dict = SearchMeshMigrationRepos200Response.from_dict(search_mesh_migration_repos200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


