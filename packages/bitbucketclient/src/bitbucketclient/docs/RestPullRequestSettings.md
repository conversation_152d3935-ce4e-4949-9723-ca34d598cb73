# RestPullRequestSettings


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**merge_config** | [**RestPullRequestSettingsMergeConfig**](RestPullRequestSettingsMergeConfig.md) |  | [optional] 

## Example

```python
from .models.rest_pull_request_settings import RestPullRequestSettings

# TODO update the JSON string below
json = "{}"
# create an instance of RestPullRequestSettings from a JSON string
rest_pull_request_settings_instance = RestPullRequestSettings.from_json(json)
# print the JSON string representation of the object
print(RestPullRequestSettings.to_json())

# convert the object into a dict
rest_pull_request_settings_dict = rest_pull_request_settings_instance.to_dict()
# create an instance of RestPullRequestSettings from a dict
rest_pull_request_settings_from_dict = RestPullRequestSettings.from_dict(rest_pull_request_settings_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


