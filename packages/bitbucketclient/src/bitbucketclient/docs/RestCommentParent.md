# RestCommentParent


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**anchor** | [**RestCommentAnchor**](RestCommentAnchor.md) |  | [optional] 
**anchored** | **bool** |  | [optional] [readonly] 
**author** | [**RestCommentAuthor**](RestCommentAuthor.md) |  | [optional] 
**comments** | [**List[RestComment]**](RestComment.md) |  | [optional] [readonly] 
**created_date** | **int** |  | [optional] [readonly] 
**html** | **str** |  | [optional] [readonly] 
**id** | **int** |  | [optional] 
**pending** | **bool** |  | [optional] [readonly] 
**properties** | **object** |  | [optional] 
**reply** | **bool** |  | [optional] [readonly] 
**resolved_date** | **int** |  | [optional] [readonly] 
**resolver** | [**RestCommentAuthor**](RestCommentAuthor.md) |  | [optional] 
**severity** | **str** |  | [optional] 
**state** | **str** |  | [optional] 
**text** | **str** |  | [optional] 
**thread_resolved** | **bool** | Indicates if this comment thread has been marked as resolved or not | [optional] 
**thread_resolved_date** | **int** |  | [optional] [readonly] 
**thread_resolver** | [**RestCommentAuthor**](RestCommentAuthor.md) |  | [optional] 
**updated_date** | **int** |  | [optional] [readonly] 
**version** | **int** |  | [optional] 

## Example

```python
from .models.rest_comment_parent import RestCommentParent

# TODO update the JSON string below
json = "{}"
# create an instance of RestCommentParent from a JSON string
rest_comment_parent_instance = RestCommentParent.from_json(json)
# print the JSON string representation of the object
print(RestCommentParent.to_json())

# convert the object into a dict
rest_comment_parent_dict = rest_comment_parent_instance.to_dict()
# create an instance of RestCommentParent from a dict
rest_comment_parent_from_dict = RestCommentParent.from_dict(rest_comment_parent_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


