# RestProjectSettingsRestriction


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**component_key** | **str** |  | [optional] 
**feature_key** | **str** |  | [optional] 
**namespace** | **str** |  | [optional] 
**processed_state** | **str** |  | [optional] 
**project** | [**RestChangesetRepositoryOriginProject**](RestChangesetRepositoryOriginProject.md) |  | [optional] 

## Example

```python
from .models.rest_project_settings_restriction import RestProjectSettingsRestriction

# TODO update the JSON string below
json = "{}"
# create an instance of RestProjectSettingsRestriction from a JSON string
rest_project_settings_restriction_instance = RestProjectSettingsRestriction.from_json(json)
# print the JSON string representation of the object
print(RestProjectSettingsRestriction.to_json())

# convert the object into a dict
rest_project_settings_restriction_dict = rest_project_settings_restriction_instance.to_dict()
# create an instance of RestProjectSettingsRestriction from a dict
rest_project_settings_restriction_from_dict = RestProjectSettingsRestriction.from_dict(rest_project_settings_restriction_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


