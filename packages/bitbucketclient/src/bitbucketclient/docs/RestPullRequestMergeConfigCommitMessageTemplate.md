# RestPullRequestMergeConfigCommitMessageTemplate


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**body** | **str** |  | [optional] 
**title** | **str** |  | [optional] 

## Example

```python
from .models.rest_pull_request_merge_config_commit_message_template import RestPullRequestMergeConfigCommitMessageTemplate

# TODO update the JSON string below
json = "{}"
# create an instance of RestPullRequestMergeConfigCommitMessageTemplate from a JSON string
rest_pull_request_merge_config_commit_message_template_instance = RestPullRequestMergeConfigCommitMessageTemplate.from_json(json)
# print the JSON string representation of the object
print(RestPullRequestMergeConfigCommitMessageTemplate.to_json())

# convert the object into a dict
rest_pull_request_merge_config_commit_message_template_dict = rest_pull_request_merge_config_commit_message_template_instance.to_dict()
# create an instance of RestPullRequestMergeConfigCommitMessageTemplate from a dict
rest_pull_request_merge_config_commit_message_template_from_dict = RestPullRequestMergeConfigCommitMessageTemplate.from_dict(rest_pull_request_merge_config_commit_message_template_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


