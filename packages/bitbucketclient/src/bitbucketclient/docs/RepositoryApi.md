# .RepositoryApi

All URIs are relative to *http://example.com:7990/rest*

Method | HTTP request | Description
------------- | ------------- | -------------
[**add_default_task1**](RepositoryApi.md#add_default_task1) | **POST** /default-tasks/latest/projects/{projectKey}/repos/{repositorySlug}/tasks | Add a default task
[**add_label**](RepositoryApi.md#add_label) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/labels | Add repository label
[**create_branch**](RepositoryApi.md#create_branch) | **POST** /branch-utils/latest/projects/{projectKey}/repos/{repositorySlug}/branches | Create branch
[**create_branch_for_repository**](RepositoryApi.md#create_branch_for_repository) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/branches | Create branch
[**create_comment**](RepositoryApi.md#create_comment) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments | Add a new commit comment
[**create_restrictions1**](RepositoryApi.md#create_restrictions1) | **POST** /branch-permissions/latest/projects/{projectKey}/repos/{repositorySlug}/restrictions | Create multiple ref restrictions
[**create_tag**](RepositoryApi.md#create_tag) | **POST** /git/latest/projects/{projectKey}/repos/{repositorySlug}/tags | Create tag
[**create_tag_for_repository**](RepositoryApi.md#create_tag_for_repository) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/tags | Create tag
[**create_webhook1**](RepositoryApi.md#create_webhook1) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks | Create webhook
[**delete5**](RepositoryApi.md#delete5) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/auto-merge | Delete pull request auto-merge settings
[**delete_all_default_tasks1**](RepositoryApi.md#delete_all_default_tasks1) | **DELETE** /default-tasks/latest/projects/{projectKey}/repos/{repositorySlug}/tasks | Deletes all default tasks for the repository
[**delete_attachment**](RepositoryApi.md#delete_attachment) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/attachments/{attachmentId} | Delete an attachment
[**delete_attachment_metadata**](RepositoryApi.md#delete_attachment_metadata) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/attachments/{attachmentId}/metadata | Delete attachment metadata
[**delete_auto_decline_settings1**](RepositoryApi.md#delete_auto_decline_settings1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/auto-decline | Delete auto decline settings
[**delete_branch**](RepositoryApi.md#delete_branch) | **DELETE** /branch-utils/latest/projects/{projectKey}/repos/{repositorySlug}/branches | Delete branch
[**delete_comment**](RepositoryApi.md#delete_comment) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments/{commentId} | Delete a commit comment
[**delete_default_task1**](RepositoryApi.md#delete_default_task1) | **DELETE** /default-tasks/latest/projects/{projectKey}/repos/{repositorySlug}/tasks/{taskId} | Delete a specific default task
[**delete_repository_hook**](RepositoryApi.md#delete_repository_hook) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks/{hookKey} | Delete repository hook
[**delete_restriction1**](RepositoryApi.md#delete_restriction1) | **DELETE** /branch-permissions/latest/projects/{projectKey}/repos/{repositorySlug}/restrictions/{id} | Delete a ref restriction
[**delete_tag**](RepositoryApi.md#delete_tag) | **DELETE** /git/latest/projects/{projectKey}/repos/{repositorySlug}/tags/{name} | Delete tag
[**delete_webhook1**](RepositoryApi.md#delete_webhook1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/{webhookId} | Delete webhook
[**disable_hook1**](RepositoryApi.md#disable_hook1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks/{hookKey}/enabled | Disable repository hook
[**edit_file**](RepositoryApi.md#edit_file) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/browse/{path} | Edit file
[**enable_hook1**](RepositoryApi.md#enable_hook1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks/{hookKey}/enabled | Enable repository hook
[**find_branches**](RepositoryApi.md#find_branches) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/ref-change-activities/branches | Get branches with ref change activities for repository
[**find_by_commit**](RepositoryApi.md#find_by_commit) | **GET** /branch-utils/latest/projects/{projectKey}/repos/{repositorySlug}/branches/info/{commitId} | Get branch
[**find_webhooks1**](RepositoryApi.md#find_webhooks1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks | Find webhooks
[**get5**](RepositoryApi.md#get5) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/auto-merge | Get pull request auto-merge settings
[**get_all_labels_for_repository**](RepositoryApi.md#get_all_labels_for_repository) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/labels | Get repository labels
[**get_archive**](RepositoryApi.md#get_archive) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/archive | Stream archive of repository
[**get_attachment**](RepositoryApi.md#get_attachment) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/attachments/{attachmentId} | Get an attachment
[**get_attachment_metadata**](RepositoryApi.md#get_attachment_metadata) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/attachments/{attachmentId}/metadata | Get attachment metadata
[**get_auto_decline_settings1**](RepositoryApi.md#get_auto_decline_settings1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/auto-decline | Get auto decline settings
[**get_branches**](RepositoryApi.md#get_branches) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/branches | Find branches
[**get_changes**](RepositoryApi.md#get_changes) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/changes | Get changes in commit
[**get_changes1**](RepositoryApi.md#get_changes1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/changes | Get changes made in commit
[**get_comment**](RepositoryApi.md#get_comment) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments/{commentId} | Get a commit comment
[**get_comments**](RepositoryApi.md#get_comments) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments | Search for commit comments
[**get_commit**](RepositoryApi.md#get_commit) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId} | Get commit by ID
[**get_commits**](RepositoryApi.md#get_commits) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits | Get commits
[**get_configurations1**](RepositoryApi.md#get_configurations1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/hook-scripts | Get hook scripts
[**get_content**](RepositoryApi.md#get_content) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/browse | Get file content at revision
[**get_content1**](RepositoryApi.md#get_content1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/browse/{path} | Get file content
[**get_default_branch1**](RepositoryApi.md#get_default_branch1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/branches/default | Get default branch
[**get_default_tasks1**](RepositoryApi.md#get_default_tasks1) | **GET** /default-tasks/latest/projects/{projectKey}/repos/{repositorySlug}/tasks | Get a page of default tasks
[**get_diff_stats_summary**](RepositoryApi.md#get_diff_stats_summary) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/diff-stats-summary/{path} | Get diff stats summary between revisions
[**get_diff_stats_summary1**](RepositoryApi.md#get_diff_stats_summary1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/compare/diff-stats-summary{path} | Retrieve the diff stats summary between commits
[**get_latest_invocation1**](RepositoryApi.md#get_latest_invocation1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/{webhookId}/latest | Get last webhook invocation details
[**get_merge_base**](RepositoryApi.md#get_merge_base) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/merge-base | Get the common ancestor between two commits
[**get_pull_request_settings1**](RepositoryApi.md#get_pull_request_settings1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/pull-requests | Get pull request settings
[**get_ref_change_activity**](RepositoryApi.md#get_ref_change_activity) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/ref-change-activities | Get ref change activity
[**get_repositories1**](RepositoryApi.md#get_repositories1) | **GET** /api/latest/repos | Search for repositories
[**get_repositories_recently_accessed**](RepositoryApi.md#get_repositories_recently_accessed) | **GET** /api/latest/profile/recent/repos | Get recently accessed repositories
[**get_repository_hook1**](RepositoryApi.md#get_repository_hook1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks/{hookKey} | Get repository hook
[**get_repository_hooks1**](RepositoryApi.md#get_repository_hooks1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks | Get repository hooks
[**get_restriction1**](RepositoryApi.md#get_restriction1) | **GET** /branch-permissions/latest/projects/{projectKey}/repos/{repositorySlug}/restrictions/{id} | Get a ref restriction
[**get_restrictions1**](RepositoryApi.md#get_restrictions1) | **GET** /branch-permissions/latest/projects/{projectKey}/repos/{repositorySlug}/restrictions | Search for ref restrictions
[**get_settings1**](RepositoryApi.md#get_settings1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks/{hookKey}/settings | Get repository hook settings
[**get_statistics1**](RepositoryApi.md#get_statistics1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/{webhookId}/statistics | Get webhook statistics
[**get_statistics_summary1**](RepositoryApi.md#get_statistics_summary1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/{webhookId}/statistics/summary | Get webhook statistics summary
[**get_status**](RepositoryApi.md#get_status) | **GET** /sync/latest/projects/{projectKey}/repos/{repositorySlug} | Get synchronization status
[**get_tag**](RepositoryApi.md#get_tag) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/tags/{name} | Get tag
[**get_tags**](RepositoryApi.md#get_tags) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/tags | Find tag
[**get_webhook1**](RepositoryApi.md#get_webhook1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/{webhookId} | Get webhook
[**react**](RepositoryApi.md#react) | **PUT** /comment-likes/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments/{commentId}/reactions/{emoticon} | React to a comment
[**remove_configuration1**](RepositoryApi.md#remove_configuration1) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/hook-scripts/{scriptId} | Remove a hook script
[**remove_label**](RepositoryApi.md#remove_label) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/labels/{labelName} | Remove repository label
[**save_attachment_metadata**](RepositoryApi.md#save_attachment_metadata) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/attachments/{attachmentId}/metadata | Save attachment metadata
[**search_webhooks**](RepositoryApi.md#search_webhooks) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/search | Search webhooks
[**set1**](RepositoryApi.md#set1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/auto-merge | Create or update the pull request auto-merge settings
[**set_auto_decline_settings1**](RepositoryApi.md#set_auto_decline_settings1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/auto-decline | Create auto decline settings
[**set_configuration1**](RepositoryApi.md#set_configuration1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/hook-scripts/{scriptId} | Create/update a hook script
[**set_default_branch1**](RepositoryApi.md#set_default_branch1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/branches/default | Update default branch
[**set_enabled**](RepositoryApi.md#set_enabled) | **POST** /sync/latest/projects/{projectKey}/repos/{repositorySlug} | Disable synchronization
[**set_settings1**](RepositoryApi.md#set_settings1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/hooks/{hookKey}/settings | Update repository hook settings
[**stream**](RepositoryApi.md#stream) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/last-modified | Stream files
[**stream1**](RepositoryApi.md#stream1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/last-modified/{path} | Stream files with last modified commit in path
[**stream_changes**](RepositoryApi.md#stream_changes) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/compare/changes | Compare commits
[**stream_commits**](RepositoryApi.md#stream_commits) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/compare/commits | Get accessible commits
[**stream_diff**](RepositoryApi.md#stream_diff) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/diff/{path} | Get diff between revisions
[**stream_diff1**](RepositoryApi.md#stream_diff1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/compare/diff{path} | Get diff between commits
[**stream_files**](RepositoryApi.md#stream_files) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/files | Get files in directory
[**stream_files1**](RepositoryApi.md#stream_files1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/files/{path} | Get files in directory
[**stream_patch**](RepositoryApi.md#stream_patch) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/patch | Get patch content at revision
[**stream_raw**](RepositoryApi.md#stream_raw) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/raw/{path} | Get raw content of a file at revision
[**stream_raw_diff**](RepositoryApi.md#stream_raw_diff) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/diff | Get raw diff for path
[**stream_raw_diff1**](RepositoryApi.md#stream_raw_diff1) | **GET** /api/latest/projects/{projectKey}/repos/{repositorySlug}/diff/{path} | Get raw diff for path
[**synchronize**](RepositoryApi.md#synchronize) | **POST** /sync/latest/projects/{projectKey}/repos/{repositorySlug}/synchronize | Manual synchronization
[**test_webhook1**](RepositoryApi.md#test_webhook1) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/test | Test webhook
[**un_react**](RepositoryApi.md#un_react) | **DELETE** /comment-likes/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments/{commentId}/reactions/{emoticon} | Remove a reaction from comment
[**unwatch**](RepositoryApi.md#unwatch) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/watch | Stop watching commit
[**unwatch2**](RepositoryApi.md#unwatch2) | **DELETE** /api/latest/projects/{projectKey}/repos/{repositorySlug}/watch | Stop watching repository
[**update_comment**](RepositoryApi.md#update_comment) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/comments/{commentId} | Update a commit comment
[**update_default_task1**](RepositoryApi.md#update_default_task1) | **PUT** /default-tasks/latest/projects/{projectKey}/repos/{repositorySlug}/tasks/{taskId} | Update a default task
[**update_pull_request_settings1**](RepositoryApi.md#update_pull_request_settings1) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/settings/pull-requests | Update pull request settings
[**update_webhook1**](RepositoryApi.md#update_webhook1) | **PUT** /api/latest/projects/{projectKey}/repos/{repositorySlug}/webhooks/{webhookId} | Update webhook
[**watch**](RepositoryApi.md#watch) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/commits/{commitId}/watch | Watch commit
[**watch2**](RepositoryApi.md#watch2) | **POST** /api/latest/projects/{projectKey}/repos/{repositorySlug}/watch | Watch repository


# **add_default_task1**
> RestDefaultTask add_default_task1(project_key, repository_slug, rest_default_task_request)

Add a default task

Creates a default task for the supplied repository.

The authenticated user must have **REPO_ADMIN** permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_default_task import RestDefaultTask
from .models.rest_default_task_request import RestDefaultTaskRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_default_task_request = .RestDefaultTaskRequest() # RestDefaultTaskRequest | The task to be added

    try:
        # Add a default task
        api_response = api_instance.add_default_task1(project_key, repository_slug, rest_default_task_request)
        print("The response of RepositoryApi->add_default_task1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->add_default_task1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_default_task_request** | [**RestDefaultTaskRequest**](RestDefaultTaskRequest.md)| The task to be added | 

### Return type

[**RestDefaultTask**](RestDefaultTask.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default task |  -  |
**400** | One or more of the following error cases occurred (check the error message for more details):    - the description is empty- the sourceMatcher or targetMatcher is invalid |  -  |
**401** | The currently authenticated user has insufficient permissions to add a default task |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **add_label**
> RestLabel add_label(project_key, repository_slug, rest_label=rest_label)

Add repository label

Applies a label to the repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository.

### Example


```python
import bitbucketclient
from .models.rest_label import RestLabel
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_label = .RestLabel() # RestLabel | The label to apply (optional)

    try:
        # Add repository label
        api_response = api_instance.add_label(project_key, repository_slug, rest_label=rest_label)
        print("The response of RepositoryApi->add_label:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->add_label: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_label** | [**RestLabel**](RestLabel.md)| The label to apply | [optional] 

### Return type

[**RestLabel**](RestLabel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The applied label. |  -  |
**400** | A validation error prevented the label from being created or applied. Possible validation errors include: The name of the label contains uppercase characters, the name is smaller than 3 characters or longer than 50 characters, the label contains other characters than a-z 0-9 and - or the label is already applied to the given repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to apply a label. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_branch**
> RestBranch create_branch(project_key, repository_slug, rest_branch_create_request)

Create branch

 Creates a branch in the specified repository.


The authenticated user must have an effective <strong>REPO_WRITE</strong> permission to call this resource. If
branch permissions are set up in the repository, the authenticated user must also have access to the branch name
that is to be created.

### Example


```python
import bitbucketclient
from .models.rest_branch import RestBranch
from .models.rest_branch_create_request import RestBranchCreateRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_branch_create_request = .RestBranchCreateRequest() # RestBranchCreateRequest | 

    try:
        # Create branch
        api_response = api_instance.create_branch(project_key, repository_slug, rest_branch_create_request)
        print("The response of RepositoryApi->create_branch:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_branch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_branch_create_request** | [**RestBranchCreateRequest**](RestBranchCreateRequest.md)|  | 

### Return type

[**RestBranch**](RestBranch.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | JSON representation of the newly created branch |  -  |
**400** | The branch was not created because the request was invalid, e.g. the provided ref name already existed in the repository, or was not a valid ref name in the repository |  -  |
**401** | The currently authenticated user has insufficient permissions to create a branch. This could be due to insufficient repository permissions, or lack of branch permission for the provided ref name |  -  |
**409** | The branch name overlapped with an existing branch |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_branch_for_repository**
> RestBranch create_branch_for_repository(project_key, repository_slug, rest_create_branch_request=rest_create_branch_request)

Create branch

Creates a branch using the information provided in the RestCreateBranchRequest request 

The authenticated user must have <strong>REPO_WRITE</strong> permission for the context repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_branch import RestBranch
from .models.rest_create_branch_request import RestCreateBranchRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_create_branch_request = .RestCreateBranchRequest() # RestCreateBranchRequest | The request to create a branch containing a <strong>name</strong>, <strong>startPoint</strong>, and optionally a <strong>message</strong> (optional)

    try:
        # Create branch
        api_response = api_instance.create_branch_for_repository(project_key, repository_slug, rest_create_branch_request=rest_create_branch_request)
        print("The response of RepositoryApi->create_branch_for_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_branch_for_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_create_branch_request** | [**RestCreateBranchRequest**](RestCreateBranchRequest.md)| The request to create a branch containing a &lt;strong&gt;name&lt;/strong&gt;, &lt;strong&gt;startPoint&lt;/strong&gt;, and optionally a &lt;strong&gt;message&lt;/strong&gt; | [optional] 

### Return type

[**RestBranch**](RestBranch.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The created branch. |  -  |
**401** | The currently authenticated user has insufficient permissions to write to the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_comment**
> RestComment create_comment(project_key, commit_id, repository_slug, since=since, rest_comment=rest_comment)

Add a new commit comment

Add a new comment.

Comments can be added in a few places by setting different attributes:

General commit comment:

```{
      "text": "An insightful general comment on a commit."
}

</pre>
Reply to a comment:
<pre>{
      "text": "A measured reply.",
      "parent": {
          "id": 1
      }
}
</pre>
General file comment:
<pre>{
      "text": "An insightful general comment on a file.",
      "anchor": {
          "diffType": "COMMIT",
          "fromHash": "6df3858eeb9a53a911cd17e66a9174d44ffb02cd",
          "path": "path/to/file",
          "srcPath": "path/to/file",
          "toHash": "04c7c5c931b9418ca7b66f51fe934d0bd9b2ba4b"
      }
}
</pre>
File line comment:
<pre>{
      "text": "A pithy comment on a particular line within a file.",
      "anchor": {
          "diffType": "COMMIT",
          "line": 1,
          "lineType": "CONTEXT",
          "fileType": "FROM",
          "fromHash": "6df3858eeb9a53a911cd17e66a9174d44ffb02cd",
          "path": "path/to/file",
          "srcPath": "path/to/file",
          "toHash": "04c7c5c931b9418ca7b66f51fe934d0bd9b2ba4b"
      }
}
```

Note: general file comments are an experimental feature and may change in the near future!

For file and line comments, 'path' refers to the path of the file to which the comment should be applied and 'srcPath' refers to the path the that file used to have (only required for copies and moves). Also, fromHash and toHash refer to the sinceId / untilId (respectively) used to produce the diff on which the comment was added. fromHash will be resolved automatically as first parent if not specified. Note that this behaviour differs from `/pull-requests/comments`

Finally diffType refers to the type of diff the comment was added on.

For line comments, 'line' refers to the line in the diff that the comment should apply to. 'lineType' refers to the type of diff hunk, which can be:- 'ADDED' - for an added line;</li>- 'REMOVED' - for a removed line; or</li>- 'CONTEXT' - for a line that was unmodified but is in the vicinity of the diff.</li>'fileType' refers to the file of the diff to which the anchor should be attached - which is of relevance when displaying the diff in a side-by-side way. Currently the supported values are:- 'FROM' - the source file of the diff</li>- 'TO' - the destination file of the diff</li>If the current user is not a participant the user is added as one and updated to watch the commit.

The authenticated user must have REPO_READ permission for the repository that the commit is in to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug
    since = 'since_example' # str | For a merge commit, a parent can be provided to specify which diff the comments should be on. For a commit range, a sinceId can be provided to specify where the comments should be anchored from. (optional)
    rest_comment = .RestComment() # RestComment | the comment (optional)

    try:
        # Add a new commit comment
        api_response = api_instance.create_comment(project_key, commit_id, repository_slug, since=since, rest_comment=rest_comment)
        print("The response of RepositoryApi->create_comment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_comment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 
 **since** | **str**| For a merge commit, a parent can be provided to specify which diff the comments should be on. For a commit range, a sinceId can be provided to specify where the comments should be anchored from. | [optional] 
 **rest_comment** | [**RestComment**](RestComment.md)| the comment | [optional] 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | The newly created comment. |  -  |
**400** | The comment was not created due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the commit, create a comment or watch the commit. |  -  |
**404** | Unable to find the supplied project, repository, commit or parent comment. The missing entity will be specified in the error details. |  -  |
**409** | Adding, deleting, or editing comments isn&#39;t supported on archived repositories. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_restrictions1**
> RestRefRestriction create_restrictions1(project_key, repository_slug, rest_restriction_request=rest_restriction_request)

Create multiple ref restrictions

Allows creating multiple restrictions at once.

### Example


```python
import bitbucketclient
from .models.rest_ref_restriction import RestRefRestriction
from .models.rest_restriction_request import RestRestrictionRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_restriction_request = [.RestRestrictionRequest()] # List[RestRestrictionRequest] | The request containing a list of the details of the restrictions to create. (optional)

    try:
        # Create multiple ref restrictions
        api_response = api_instance.create_restrictions1(project_key, repository_slug, rest_restriction_request=rest_restriction_request)
        print("The response of RepositoryApi->create_restrictions1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_restrictions1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_restriction_request** | [**List[RestRestrictionRequest]**](RestRestrictionRequest.md)| The request containing a list of the details of the restrictions to create. | [optional] 

### Return type

[**RestRefRestriction**](RestRefRestriction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/vnd.atl.bitbucket.bulk+json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Response contains the ref restriction that was just created. |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user has insufficient permissions to perform this operation. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_tag**
> RestTag create_tag(project_key, repository_slug, rest_git_tag_create_request=rest_git_tag_create_request)

Create tag

Creates a tag in the specified repository.

The authenticated user must have an effective <strong>REPO_WRITE</strong> permission to call this resource.

'LIGHTWEIGHT' and 'ANNOTATED' are the two type of tags that can be created. The 'startPoint' can either be a ref or a 'commit'.

### Example


```python
import bitbucketclient
from .models.rest_git_tag_create_request import RestGitTagCreateRequest
from .models.rest_tag import RestTag
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_git_tag_create_request = .RestGitTagCreateRequest() # RestGitTagCreateRequest | The create git tag request. (optional)

    try:
        # Create tag
        api_response = api_instance.create_tag(project_key, repository_slug, rest_git_tag_create_request=rest_git_tag_create_request)
        print("The response of RepositoryApi->create_tag:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_tag: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_git_tag_create_request** | [**RestGitTagCreateRequest**](RestGitTagCreateRequest.md)| The create git tag request. | [optional] 

### Return type

[**RestTag**](RestTag.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | A JSON representation of the newly created tag. |  -  |
**400** | The tag was not created because the request was invalid, e.g. the provided ref name already existed in the repository, or was not a valid ref name in the repository, or the start point is invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to create a tag. This could be due to insufficient repository permissions. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_tag_for_repository**
> RestTag create_tag_for_repository(project_key, repository_slug, rest_create_tag_request=rest_create_tag_request)

Create tag

Creates a tag using the information provided in the RestCreateTagRequest request 

The authenticated user must have <strong>REPO_WRITE</strong> permission for the context repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_create_tag_request import RestCreateTagRequest
from .models.rest_tag import RestTag
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_create_tag_request = .RestCreateTagRequest() # RestCreateTagRequest | The request to create a tag containing a <strong>name</strong>, <strong>startPoint</strong>, and optionally a <strong>message</strong> (optional)

    try:
        # Create tag
        api_response = api_instance.create_tag_for_repository(project_key, repository_slug, rest_create_tag_request=rest_create_tag_request)
        print("The response of RepositoryApi->create_tag_for_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_tag_for_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_create_tag_request** | [**RestCreateTagRequest**](RestCreateTagRequest.md)| The request to create a tag containing a &lt;strong&gt;name&lt;/strong&gt;, &lt;strong&gt;startPoint&lt;/strong&gt;, and optionally a &lt;strong&gt;message&lt;/strong&gt; | [optional] 

### Return type

[**RestTag**](RestTag.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The created tag. |  -  |
**401** | The currently authenticated user has insufficient permissions to write to the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **create_webhook1**
> RestWebhook create_webhook1(project_key, repository_slug, rest_webhook=rest_webhook)

Create webhook

Create a webhook for the repository specified via the URL. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook import RestWebhook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_webhook = .RestWebhook() # RestWebhook | The webhook to be created for this repository. (optional)

    try:
        # Create webhook
        api_response = api_instance.create_webhook1(project_key, repository_slug, rest_webhook=rest_webhook)
        print("The response of RepositoryApi->create_webhook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->create_webhook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_webhook** | [**RestWebhook**](RestWebhook.md)| The webhook to be created for this repository. | [optional] 

### Return type

[**RestWebhook**](RestWebhook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A created webhook. |  -  |
**400** | The webhook parameters were invalid or not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to create webhooks in the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete5**
> delete5(project_key, repository_slug)

Delete pull request auto-merge settings

Deletes pull request auto-merge settings for the supplied repository.

The authenticated user must have <strong>REPO_ADMIN</strong> permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Delete pull request auto-merge settings
        api_instance.delete5(project_key, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete5: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The pull request auto-merge settings |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the pull request auto-merge settings. |  -  |
**403** | The pull request auto-merge settings cannot be modified due to a restriction enforced by the supplied repository&#39;s project. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_all_default_tasks1**
> delete_all_default_tasks1(project_key, repository_slug)

Deletes all default tasks for the repository

Delete all the default tasks for the supplied repository

The authenticated user must have **REPO_ADMIN** permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Deletes all default tasks for the repository
        api_instance.delete_all_default_tasks1(project_key, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_all_default_tasks1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default tasks have been deleted successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete default tasks |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_attachment**
> delete_attachment(project_key, attachment_id, repository_slug)

Delete an attachment

Delete an attachment.

The user must be authenticated and have <strong>REPO_ADMIN</strong> permission for the specified repository.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    attachment_id = 'attachment_id_example' # str | the attachment ID
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Delete an attachment
        api_instance.delete_attachment(project_key, attachment_id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_attachment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **attachment_id** | **str**| the attachment ID | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** |  |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the attachment |  -  |
**404** | The attachment does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_attachment_metadata**
> delete_attachment_metadata(project_key, attachment_id, repository_slug)

Delete attachment metadata

Delete attachment metadata.

The user must be authenticated and have <strong>REPO_ADMIN</strong> permission for the specified repository.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    attachment_id = 'attachment_id_example' # str | the attachment ID
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Delete attachment metadata
        api_instance.delete_attachment_metadata(project_key, attachment_id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_attachment_metadata: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **attachment_id** | **str**| the attachment ID | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** |  |  -  |
**401** | The currently authenticated user has insufficient permissions to delete theattachment metadata |  -  |
**404** | The attachment or the attachment metadata does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_auto_decline_settings1**
> delete_auto_decline_settings1(project_key, repository_slug)

Delete auto decline settings

Delete auto decline settings for the supplied repository.

The authenticated user must have <strong>REPO_ADMIN</strong> permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Delete auto decline settings
        api_instance.delete_auto_decline_settings1(project_key, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_auto_decline_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The auto decline settings have been deleted successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the auto decline settings. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_branch**
> delete_branch(project_key, repository_slug, rest_branch_delete_request=rest_branch_delete_request)

Delete branch

 Deletes a branch in the specified repository.


 If the branch does not exist, this operation will not raise an error. In other words after calling this resource
 and receiving a 204 response the branch provided in the request is guaranteed to not exist in the specified
 repository any more, regardless of its existence beforehand.


 The optional 'endPoint' parameter of the request may contain a commit ID that the provided ref name is
 expected to point to. Should the ref point to a different commit ID, a 400 response will be returned with
 appropriate error details.


 The authenticated user must have an effective <strong>REPO_WRITE</strong> permission to call this resource. If
 branch permissions are set up in the repository, the authenticated user must also have access to the branch name
 that is to be deleted.

### Example


```python
import bitbucketclient
from .models.rest_branch_delete_request import RestBranchDeleteRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_branch_delete_request = .RestBranchDeleteRequest() # RestBranchDeleteRequest | Branch delete request (optional)

    try:
        # Delete branch
        api_instance.delete_branch(project_key, repository_slug, rest_branch_delete_request=rest_branch_delete_request)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_branch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_branch_delete_request** | [**RestBranchDeleteRequest**](RestBranchDeleteRequest.md)| Branch delete request | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the branch no longer exists in the repository |  -  |
**400** | The branch was not deleted because the request was invalid, e.g. no ref name to delete was provided, or the provided ref name points to the default branch in the repository that cannot be deleted |  -  |
**401** | The currently authenticated user has insufficient permissions to delete a branch. This could be due to insufficient repository permissions, or lack of branch permission for the provided ref name. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_comment**
> delete_comment(project_key, comment_id, commit_id, repository_slug, version=version)

Delete a commit comment

Delete a commit comment. Anyone can delete their own comment. Only users with <strong>REPO_ADMIN</strong> and above may delete comments created by other users. Comments which have replies <i>may not be deleted</i>, regardless of the user's granted permissions.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that the commit is in to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    comment_id = 'comment_id_example' # str | the comment
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug
    version = 'version_example' # str | The expected version of the comment. This must match the server's version of the comment or the delete will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the delete. Look for the 'version' attribute in the returned JSON structure. (optional)

    try:
        # Delete a commit comment
        api_instance.delete_comment(project_key, comment_id, commit_id, repository_slug, version=version)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_comment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **comment_id** | **str**| the comment | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 
 **version** | **str**| The expected version of the comment. This must match the server&#39;s version of the comment or the delete will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the delete. Look for the &#39;version&#39; attribute in the returned JSON structure. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The operation was successful |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the comment. |  -  |
**404** | Unable to find the supplied project, repository or commit. The missing entity will be specified in the error details. |  -  |
**409** | The comment has replies, the version supplied does not match the comment&#39;s current version or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_default_task1**
> delete_default_task1(project_key, repository_slug, task_id)

Delete a specific default task

Delete a specific default task for a repository.

The authenticated user must have **REPO_ADMIN** permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    task_id = 'task_id_example' # str | The ID of the default task

    try:
        # Delete a specific default task
        api_instance.delete_default_task1(project_key, repository_slug, task_id)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_default_task1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **task_id** | **str**| The ID of the default task | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The default task has been deleted successfully. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete default tasks |  -  |
**404** | The specified repository or task does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_repository_hook**
> delete_repository_hook(project_key, hook_key, repository_slug)

Delete repository hook

Delete repository hook configuration for the supplied <strong>hookKey</strong> and <strong>repositorySlug</strong>

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete repository hook
        api_instance.delete_repository_hook(project_key, hook_key, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_repository_hook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The hook configuration matching the supplied &lt;strong&gt;hookKey&lt;/strong&gt; and &lt;strong&gt;repositorySlug&lt;/strong&gt; was deleted |  -  |
**400** | The settings specified are invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete the hook. |  -  |
**404** | The specified repository or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_restriction1**
> delete_restriction1(project_key, id, repository_slug)

Delete a ref restriction

Deletes a restriction as specified by a restriction id.

The authenticated user must have <strong>REPO_ADMIN</strong> permission or higher to call this resource. Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The restriction id.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete a ref restriction
        api_instance.delete_restriction1(project_key, id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_restriction1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The restriction id. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the operation was successful |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user is not permitted to delete restrictions on the provided project |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_tag**
> delete_tag(project_key, name, repository_slug)

Delete tag

Deletes a tag in the specified repository.

The authenticated user must have an effective <strong>REPO_WRITE</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    name = 'name_example' # str | The name of the tag to be deleted.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete tag
        api_instance.delete_tag(project_key, name, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_tag: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **name** | **str**| The name of the tag to be deleted. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the tag no longer exists in the repository. |  -  |
**400** | The tag was not deleted because repository is either empty, or is not a git repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete a tag. This could be due to insufficient repository permissions. |  -  |
**404** | If the tag doesn&#39;t exist in the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **delete_webhook1**
> delete_webhook1(project_key, webhook_id, repository_slug)

Delete webhook

Delete a webhook for the repository specified via the URL. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | The ID of the webhook to be deleted.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Delete webhook
        api_instance.delete_webhook1(project_key, webhook_id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->delete_webhook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| The ID of the webhook to be deleted. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The webhook for the repository has been deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to delete webhooks in the repository. |  -  |
**404** | The specified repository does not exist, or webhook does not exist in this repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **disable_hook1**
> RestRepositoryHook disable_hook1(project_key, hook_key, repository_slug)

Disable repository hook

Disable a repository hook for this repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository_hook import RestRepositoryHook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Disable repository hook
        api_response = api_instance.disable_hook1(project_key, hook_key, repository_slug)
        print("The response of RepositoryApi->disable_hook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->disable_hook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestRepositoryHook**](RestRepositoryHook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository hooks with their associated enabled state for the supplied hookKey. |  -  |
**401** | The currently authenticated user has insufficient permissions to disable the hook. |  -  |
**404** | The specified repository or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **edit_file**
> RestCommit edit_file(path, project_key, repository_slug, branch=branch, content=content, message=message, source_branch=source_branch, source_commit_id=source_commit_id)

Edit file

Update the content of path, on the given repository and branch. 

This resource accepts PUT multipart form data, containing the file in a form-field named content. 

An example <a href="http://curl.haxx.se/">curl</a> request to update 'README.md' would be:

```curl -X PUT -u username:password -F content=@README.md  -F 'message=Updated using file-edit REST API' -F branch=master -F  sourceCommitId=5636641a50b  http://example.com/rest/api/latest/projects/PROJECT_1/repos/repo_1/browse/README.md ```

- branch:  the branch on which the path should be modified or created
- content: the full content of the file at path 
- message: the message associated with this change, to be used as the commit message. Or null if the default message should be used.
- sourceCommitId: the commit ID of the file before it was edited, used to identify if content has changed. Or null if this is a new file


The file can be updated or created on a new branch. In this case, the sourceBranch parameter should be provided to identify the starting point for the new branch and the branch parameter identifies the branch to create the new commit on.

### Example


```python
import bitbucketclient
from .models.rest_commit import RestCommit
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The path of the file that is to be modified or created
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    branch = 'branch_example' # str | The branch on which the <code>path</code> should be modified or created. (optional)
    content = 'content_example' # str | The full content of the file at <code>path</code>. (optional)
    message = 'message_example' # str | The message associated with this change, to be used as the commit message. Or null if the default message should be used. (optional)
    source_branch = 'source_branch_example' # str | The starting point for <code>branch</code>. If provided and different from <code>branch</code>, <code>branch</code> will be created as a new branch, branching off from <code>sourceBranch</code>. (optional)
    source_commit_id = 'source_commit_id_example' # str | The commit ID of the file before it was edited, used to identify if content has changed. Or null if this is a new file (optional)

    try:
        # Edit file
        api_response = api_instance.edit_file(path, project_key, repository_slug, branch=branch, content=content, message=message, source_branch=source_branch, source_commit_id=source_commit_id)
        print("The response of RepositoryApi->edit_file:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->edit_file: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The path of the file that is to be modified or created | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **branch** | **str**| The branch on which the &lt;code&gt;path&lt;/code&gt; should be modified or created. | [optional] 
 **content** | **str**| The full content of the file at &lt;code&gt;path&lt;/code&gt;. | [optional] 
 **message** | **str**| The message associated with this change, to be used as the commit message. Or null if the default message should be used. | [optional] 
 **source_branch** | **str**| The starting point for &lt;code&gt;branch&lt;/code&gt;. If provided and different from &lt;code&gt;branch&lt;/code&gt;, &lt;code&gt;branch&lt;/code&gt; will be created as a new branch, branching off from &lt;code&gt;sourceBranch&lt;/code&gt;. | [optional] 
 **source_commit_id** | **str**| The commit ID of the file before it was edited, used to identify if content has changed. Or null if this is a new file | [optional] 

### Return type

[**RestCommit**](RestCommit.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly created commit. |  -  |
**400** | There are validation errors, e.g. The branch or content parameters were not supplied. |  -  |
**401** | The currently authenticated user does not have write permission for the given repository. |  -  |
**403** | The request was authenticated using a project or repository access token, which does not have a valid user associated with it |  -  |
**404** | The repository does not exist. |  -  |
**409** | The file already exists when trying to create a file, or the given content does not modify the file, or the file has changed since the given sourceCommitId, or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **enable_hook1**
> RestRepositoryHook enable_hook1(project_key, hook_key, repository_slug, content_length=content_length)

Enable repository hook

Enable a repository hook for this repository and optionally apply new configuration. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource. 

A JSON document may be provided to use as the settings for the hook. These structure and validity of the document is decided by the plugin providing the hook.

### Example


```python
import bitbucketclient
from .models.rest_repository_hook import RestRepositoryHook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    content_length = 'content_length_example' # str | The content length. (optional)

    try:
        # Enable repository hook
        api_response = api_instance.enable_hook1(project_key, hook_key, repository_slug, content_length=content_length)
        print("The response of RepositoryApi->enable_hook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->enable_hook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **repository_slug** | **str**| The repository slug. | 
 **content_length** | **str**| The content length. | [optional] 

### Return type

[**RestRepositoryHook**](RestRepositoryHook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository hooks with their associated enabled state for the supplied hookKey. |  -  |
**401** | The currently authenticated user has insufficient permissions to enable the hook. |  -  |
**404** | The specified repository or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **find_branches**
> FindByCommit200Response find_branches(project_key, repository_slug, filter_text=filter_text, start=start, limit=limit)

Get branches with ref change activities for repository

Retrieve a page of branches with ref change activities for a specific repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.find_by_commit200_response import FindByCommit200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    filter_text = 'filter_text_example' # str | (optional) Partial match for a ref ID to filter minimal refs for (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get branches with ref change activities for repository
        api_response = api_instance.find_branches(project_key, repository_slug, filter_text=filter_text, start=start, limit=limit)
        print("The response of RepositoryApi->find_branches:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->find_branches: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **filter_text** | **str**| (optional) Partial match for a ref ID to filter minimal refs for | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**FindByCommit200Response**](FindByCommit200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of branches with ref change activities. |  -  |
**401** | The user is currently not authenticated or the user does not have REPO_ADMIN permission. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **find_by_commit**
> FindByCommit200Response find_by_commit(project_key, commit_id, repository_slug, start=start, limit=limit)

Get branch

Gets the branch information associated with a single commit from a given repository.

### Example


```python
import bitbucketclient
from .models.find_by_commit200_response import FindByCommit200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    commit_id = 'commit_id_example' # str | 
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get branch
        api_response = api_instance.find_by_commit(project_key, commit_id, repository_slug, start=start, limit=limit)
        print("The response of RepositoryApi->find_by_commit:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->find_by_commit: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **commit_id** | **str**|  | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**FindByCommit200Response**](FindByCommit200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of branch refs associated with the commit |  -  |
**500** | The request has timed out processing the branch request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **find_webhooks1**
> find_webhooks1(project_key, repository_slug, event=event, statistics=statistics)

Find webhooks

Find webhooks in this repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    event = 'event_example' # str | List of <code>com.atlassian.webhooks.WebhookEvent</code> IDs to filter for (optional)
    statistics = True # bool | <code>true</code> if statistics should be provided for all found webhooks (optional)

    try:
        # Find webhooks
        api_instance.find_webhooks1(project_key, repository_slug, event=event, statistics=statistics)
    except Exception as e:
        print("Exception when calling RepositoryApi->find_webhooks1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **event** | **str**| List of &lt;code&gt;com.atlassian.webhooks.WebhookEvent&lt;/code&gt; IDs to filter for | [optional] 
 **statistics** | **bool**| &lt;code&gt;true&lt;/code&gt; if statistics should be provided for all found webhooks | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of webhooks. |  -  |
**401** | The currently authenticated user has insufficient permissions to find webhooks in the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get5**
> RestAutoMergeRestrictedSettings get5(project_key, repository_slug)

Get pull request auto-merge settings

Retrieves the pull request auto-merge settings for the supplied repository. Project settings will be returned if no explicit settings have been set for the repository. In the case that there are no project settings, the default settings will be returned. If the repository's project has restricted its auto-merge settings, then the settings of the project will be returned.

The authenticated user must have <strong>REPO_READ</strong> permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_merge_restricted_settings import RestAutoMergeRestrictedSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get pull request auto-merge settings
        api_response = api_instance.get5(project_key, repository_slug)
        print("The response of RepositoryApi->get5:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get5: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestAutoMergeRestrictedSettings**](RestAutoMergeRestrictedSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The pull request auto-merge settings |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the pull request auto-merge settings. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_all_labels_for_repository**
> RestLabel get_all_labels_for_repository(project_key, repository_slug)

Get repository labels

Get all labels applied to the given repository. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository.

### Example


```python
import bitbucketclient
from .models.rest_label import RestLabel
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get repository labels
        api_response = api_instance.get_all_labels_for_repository(project_key, repository_slug)
        print("The response of RepositoryApi->get_all_labels_for_repository:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_all_labels_for_repository: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestLabel**](RestLabel.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The applied label. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the labels. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_archive**
> get_archive(project_key, repository_slug, path=path, filename=filename, at=at, prefix=prefix, format=format)

Stream archive of repository

Streams an archive of the repository's contents at the requested commit. If no `at=` commit is requested, an archive of the default branch is streamed.

The <code>filename=</code> query parameter may be used to specify the exact filename to include in the "Content-Disposition" header. If an explicit filename is not provided, one will be automatically generated based on what is being archived. Its format depends on the at= value: 

- No <code>at=</code> commit:     &lt;slug&gt;-&lt;default-branch-name&gt;@&lt;commit&gt;.&lt;format&gt;;     e.g. <EMAIL>
- <code>at=</code>sha: &lt;slug&gt;-&lt;at&gt;.&lt;format&gt;; e.g.     example-09bcbb00100cfbb5310fb6834a1d5ce6cac253e9.tar.gz
- <code>at=</code>branchOrTag: &lt;slug&gt;-&lt;branchOrTag&gt;@&lt;commit&gt;.&lt;format&gt;;     e.g. <EMAIL> 

    - If the branch or tag is qualified (e.g. refs/heads/master, the short name         (master) will be included in the filename
    - If the branch or tag's <i>short name</i> includes slashes (e.g. release/4.6),         they will be converted to hyphens in the filename (release-4.5)




Archives may be requested in the following formats by adding the <code>format=</code> query parameter: 

- zip: A zip file using standard compression (Default)
- tar: An uncompressed tarball
- tar.gz or tgz: A GZip-compressed tarball


The contents of the archive may be filtered by using the <code>path=</code> query parameter to specify paths to include. <code>path=</code> may be specified multiple times to include multiple paths. 

The <code>prefix=</code> query parameter may be used to define a directory (or multiple directories) where the archive's contents should be placed. If the prefix does not end with /, one will be added automatically. The prefix is <i>always</i> treated as a directory; it is not possible to use it to prepend characters to the entries in the archive. 

Archives of public repositories may be streamed by any authenticated or anonymous user. Streaming archives for non-public repositories requires an <i>authenticated user</i> with at least <b>REPO_READ</b> permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    path = 'path_example' # str | Paths to include in the streamed archive; may be repeated to include multiple paths (optional)
    filename = 'filename_example' # str | A filename to include the \"Content-Disposition\" header (optional)
    at = 'at_example' # str | The commit to stream an archive of; if not supplied, an archive of the default branch is streamed (optional)
    prefix = 'prefix_example' # str | A prefix to apply to all entries in the streamed archive; if the supplied prefix does not end with a trailing /, one will be added automatically (optional)
    format = 'format_example' # str | The format to stream the archive in; must be one of: zip, tar, tar.gz or tgz (optional)

    try:
        # Stream archive of repository
        api_instance.get_archive(project_key, repository_slug, path=path, filename=filename, at=at, prefix=prefix, format=format)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_archive: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **path** | **str**| Paths to include in the streamed archive; may be repeated to include multiple paths | [optional] 
 **filename** | **str**| A filename to include the \&quot;Content-Disposition\&quot; header | [optional] 
 **at** | **str**| The commit to stream an archive of; if not supplied, an archive of the default branch is streamed | [optional] 
 **prefix** | **str**| A prefix to apply to all entries in the streamed archive; if the supplied prefix does not end with a trailing /, one will be added automatically | [optional] 
 **format** | **str**| The format to stream the archive in; must be one of: zip, tar, tar.gz or tgz | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/octet-stream, application/x-tar, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | An archive or the requested commit, in zip, tar or gzipped-tar format. |  -  |
**400** | The requested format is not supported. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist or does not contain the at commit. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_attachment**
> get_attachment(project_key, attachment_id, repository_slug, user_agent=user_agent, range=range)

Get an attachment

Retrieve the attachment.

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository that is associated to the attachment.

Range requests (see IETF RFC7233) are supported. However only a single range issupported. If multiple ranges are passed the ranges will be ignored and the entire content will be returned in the response.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    attachment_id = 'attachment_id_example' # str | the attachment ID
    repository_slug = 'repository_slug_example' # str | The repository slug
    user_agent = 'user_agent_example' # str |  (optional)
    range = 'range_example' # str |  (optional)

    try:
        # Get an attachment
        api_instance.get_attachment(project_key, attachment_id, repository_slug, user_agent=user_agent, range=range)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_attachment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **attachment_id** | **str**| the attachment ID | 
 **repository_slug** | **str**| The repository slug | 
 **user_agent** | **str**|  | [optional] 
 **range** | **str**|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | the attachment |  -  |
**206** | the requested range of bytes from the attachment |  -  |
**401** | the user is currently not authenticated |  -  |
**404** | The attachment does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_attachment_metadata**
> RestAttachmentMetadata get_attachment_metadata(project_key, attachment_id, repository_slug)

Get attachment metadata

Retrieve the attachment metadata.

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository that is associated to the attachment that has the attachment metadata.

### Example


```python
import bitbucketclient
from .models.rest_attachment_metadata import RestAttachmentMetadata
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    attachment_id = 'attachment_id_example' # str | the attachment ID
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get attachment metadata
        api_response = api_instance.get_attachment_metadata(project_key, attachment_id, repository_slug)
        print("The response of RepositoryApi->get_attachment_metadata:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_attachment_metadata: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **attachment_id** | **str**| the attachment ID | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestAttachmentMetadata**](RestAttachmentMetadata.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The attachment metadata |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the attachment metadata |  -  |
**404** | The attachment or the attachment metadata does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_auto_decline_settings1**
> RestAutoDeclineSettings get_auto_decline_settings1(project_key, repository_slug)

Get auto decline settings

Retrieves the auto decline settings for the supplied repository. Project settings will be returned if no explicit settings have been set for the repository. In the case that there are no project settings, the default settings will be returned.

The authenticated user must have <strong>REPO_READ</strong> permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_decline_settings import RestAutoDeclineSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get auto decline settings
        api_response = api_instance.get_auto_decline_settings1(project_key, repository_slug)
        print("The response of RepositoryApi->get_auto_decline_settings1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_auto_decline_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestAutoDeclineSettings**](RestAutoDeclineSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The auto decline settings |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the auto decline settings. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_branches**
> GetBranches200Response get_branches(project_key, repository_slug, boost_matches=boost_matches, context=context, order_by=order_by, details=details, filter_text=filter_text, base=base, start=start, limit=limit)

Find branches

Retrieve the branches matching the supplied <strong>filterText</strong> param. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_branches200_response import GetBranches200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    boost_matches = True # bool | Controls whether exact and prefix matches will be boosted to the top (optional)
    context = 'context_example' # str |  (optional)
    order_by = 'order_by_example' # str | Ordering of refs either ALPHABETICAL (by name) or MODIFICATION (last updated) (optional)
    details = True # bool | Whether to retrieve plugin-provided metadata about each branch (optional)
    filter_text = 'filter_text_example' # str | The text to match on (optional)
    base = 'base_example' # str | Base branch or tag to compare each branch to (for the metadata providers that uses that information (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Find branches
        api_response = api_instance.get_branches(project_key, repository_slug, boost_matches=boost_matches, context=context, order_by=order_by, details=details, filter_text=filter_text, base=base, start=start, limit=limit)
        print("The response of RepositoryApi->get_branches:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_branches: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **boost_matches** | **bool**| Controls whether exact and prefix matches will be boosted to the top | [optional] 
 **context** | **str**|  | [optional] 
 **order_by** | **str**| Ordering of refs either ALPHABETICAL (by name) or MODIFICATION (last updated) | [optional] 
 **details** | **bool**| Whether to retrieve plugin-provided metadata about each branch | [optional] 
 **filter_text** | **str**| The text to match on | [optional] 
 **base** | **str**| Base branch or tag to compare each branch to (for the metadata providers that uses that information | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetBranches200Response**](GetBranches200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The branches matching the supplied &lt;strong&gt;filterText&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_changes**
> GetChanges1200Response get_changes(project_key, commit_id, repository_slug, with_comments=with_comments, since=since, start=start, limit=limit)

Get changes in commit

Retrieve a page of changes made in a specified commit. 

 <strong>Note:</strong> The implementation will apply a hard cap (<code>page.max.changes</code>) and it is not possible to request subsequent content when that cap is exceeded. 

 The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_changes1200_response import GetChanges1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The commit to retrieve changes for
    repository_slug = 'repository_slug_example' # str | The repository slug
    with_comments = 'with_comments_example' # str | <code>true</code> to apply comment counts in the changes (the default); otherwise, <code>false</code> to stream changes without comment counts (optional)
    since = 'since_example' # str | The commit to which <code>until</code> should be compared to produce a page of changes. If not specified the commit's first parent is assumed (if one exists) (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get changes in commit
        api_response = api_instance.get_changes(project_key, commit_id, repository_slug, with_comments=with_comments, since=since, start=start, limit=limit)
        print("The response of RepositoryApi->get_changes:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_changes: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The commit to retrieve changes for | 
 **repository_slug** | **str**| The repository slug | 
 **with_comments** | **str**| &lt;code&gt;true&lt;/code&gt; to apply comment counts in the changes (the default); otherwise, &lt;code&gt;false&lt;/code&gt; to stream changes without comment counts | [optional] 
 **since** | **str**| The commit to which &lt;code&gt;until&lt;/code&gt; should be compared to produce a page of changes. If not specified the commit&#39;s first parent is assumed (if one exists) | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetChanges1200Response**](GetChanges1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of changes |  -  |
**400** | The until parameter was not supplied |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository or the since or until parameters supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_changes1**
> GetChanges1200Response get_changes1(project_key, repository_slug, until=until, since=since, start=start, limit=limit)

Get changes made in commit

Retrieve a page of changes made in a specified commit. 

<strong>Note:</strong> The implementation will apply a hard cap ({@code page.max.changes}) and it is not possible to request subsequent content when that cap is exceeded. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_changes1200_response import GetChanges1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    until = 'until_example' # str | The commit to retrieve changes for (optional)
    since = 'since_example' # str | The commit to which <code>until</code> should be compared to produce a page of changes. If not specified the commit's first parent is assumed (if one exists) (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get changes made in commit
        api_response = api_instance.get_changes1(project_key, repository_slug, until=until, since=since, start=start, limit=limit)
        print("The response of RepositoryApi->get_changes1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_changes1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **until** | **str**| The commit to retrieve changes for | [optional] 
 **since** | **str**| The commit to which &lt;code&gt;until&lt;/code&gt; should be compared to produce a page of changes. If not specified the commit&#39;s first parent is assumed (if one exists) | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetChanges1200Response**](GetChanges1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of changes |  -  |
**400** | The until parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository or the since or until parameters supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_comment**
> RestComment get_comment(project_key, comment_id, commit_id, repository_slug)

Get a commit comment

Retrieves a commit discussion comment.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that the commit is in to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Get a commit comment
        api_response = api_instance.get_comment(project_key, comment_id, commit_id, repository_slug)
        print("The response of RepositoryApi->get_comment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_comment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **comment_id** | **str**| The ID of the comment to retrieve | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The requested comment. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the comment |  -  |
**404** | Unable to find the supplied project, repository, commit or comment. The missing entity will be specified in the error details. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_comments**
> GetComments200Response get_comments(project_key, commit_id, repository_slug, path=path, since=since, start=start, limit=limit)

Search for commit comments

Retrieves the commit discussion comments that match the specified search criteria.

It is possible to retrieve commit discussion comments that are anchored to a range of commits by providing the sinceId that the comments anchored from.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that the commit is in to call this resource.

### Example


```python
import bitbucketclient
from .models.get_comments200_response import GetComments200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug
    path = 'path_example' # str | The path to the file on which comments were made (optional)
    since = 'since_example' # str | For a merge commit, a parent can be provided to specify which diff the comments are on. For a commit range, a sinceId can be provided to specify where the comments are anchored from. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Search for commit comments
        api_response = api_instance.get_comments(project_key, commit_id, repository_slug, path=path, since=since, start=start, limit=limit)
        print("The response of RepositoryApi->get_comments:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_comments: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 
 **path** | **str**| The path to the file on which comments were made | [optional] 
 **since** | **str**| For a merge commit, a parent can be provided to specify which diff the comments are on. For a commit range, a sinceId can be provided to specify where the comments are anchored from. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetComments200Response**](GetComments200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of comments that match the search criteria |  -  |
**400** | The request was malformed. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the comment |  -  |
**404** | Unable to find the supplied project, repository, or commit. The missing entity will be specified in the error details. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_commit**
> RestCommit get_commit(project_key, commit_id, repository_slug, path=path)

Get commit by ID

Retrieve a single commit <i>identified by its ID</i>. In general, that ID is a SHA1. <u>From 2.11, ref names like "refs/heads/master" are no longer accepted by this resource.</u>

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_commit import RestCommit
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The commit ID to retrieve
    repository_slug = 'repository_slug_example' # str | The repository slug
    path = 'path_example' # str | An optional path to filter the commit by. If supplied the details returned <i>may not</i> be for the specified commit. Instead, starting from the specified commit, they will be the details for the first commit affecting the specified path. (optional)

    try:
        # Get commit by ID
        api_response = api_instance.get_commit(project_key, commit_id, repository_slug, path=path)
        print("The response of RepositoryApi->get_commit:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_commit: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The commit ID to retrieve | 
 **repository_slug** | **str**| The repository slug | 
 **path** | **str**| An optional path to filter the commit by. If supplied the details returned &lt;i&gt;may not&lt;/i&gt; be for the specified commit. Instead, starting from the specified commit, they will be the details for the first commit affecting the specified path. | [optional] 

### Return type

[**RestCommit**](RestCommit.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A commit |  -  |
**400** | The supplied commit ID was invalid |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_commits**
> GetCommits200Response get_commits(project_key, repository_slug, avatar_scheme=avatar_scheme, path=path, with_counts=with_counts, follow_renames=follow_renames, until=until, avatar_size=avatar_size, since=since, merges=merges, ignore_missing=ignore_missing, start=start, limit=limit)

Get commits

Retrieve a page of commits from a given starting commit or "between" two commits. If no explicit commit is specified, the tip of the repository's default branch is assumed. commits may be identified by branch or tag name or by ID. A path may be supplied to restrict the returned commits to only those which affect that path. 

The authenticated user must have <b>REPO_READ</b> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_commits200_response import GetCommits200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug
    avatar_scheme = 'avatar_scheme_example' # str | The desired scheme for the avatar URL. If the parameter is not present URLs will use the same scheme as this request (optional)
    path = 'path_example' # str | An optional path to filter commits by (optional)
    with_counts = 'with_counts_example' # str | Optionally include the total number of commits and total number of unique authors (optional)
    follow_renames = 'follow_renames_example' # str | If <code>true</code>, the commit history of the specified file will be followed past renames. Only valid for a path to a single file. (optional)
    until = 'until_example' # str | The commit ID (SHA1) or ref (inclusively) to retrieve commits before (optional)
    avatar_size = 'avatar_size_example' # str | If present the service adds avatar URLs for commit authors. Should be an integer specifying the desired size in pixels. If the parameter is not present, avatar URLs will not be set (optional)
    since = 'since_example' # str | The commit ID or ref (exclusively) to retrieve commits after (optional)
    merges = 'merges_example' # str | If present, controls how merge commits should be filtered. Can be either <code>exclude</code>, to exclude merge commits, <code>include</code>, to include both merge commits and non-merge commits or <code>only</code>, to only return merge commits. (optional)
    ignore_missing = 'ignore_missing_example' # str | <code>true</code> to ignore missing commits, <code>false</code> otherwise (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get commits
        api_response = api_instance.get_commits(project_key, repository_slug, avatar_scheme=avatar_scheme, path=path, with_counts=with_counts, follow_renames=follow_renames, until=until, avatar_size=avatar_size, since=since, merges=merges, ignore_missing=ignore_missing, start=start, limit=limit)
        print("The response of RepositoryApi->get_commits:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_commits: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 
 **avatar_scheme** | **str**| The desired scheme for the avatar URL. If the parameter is not present URLs will use the same scheme as this request | [optional] 
 **path** | **str**| An optional path to filter commits by | [optional] 
 **with_counts** | **str**| Optionally include the total number of commits and total number of unique authors | [optional] 
 **follow_renames** | **str**| If &lt;code&gt;true&lt;/code&gt;, the commit history of the specified file will be followed past renames. Only valid for a path to a single file. | [optional] 
 **until** | **str**| The commit ID (SHA1) or ref (inclusively) to retrieve commits before | [optional] 
 **avatar_size** | **str**| If present the service adds avatar URLs for commit authors. Should be an integer specifying the desired size in pixels. If the parameter is not present, avatar URLs will not be set | [optional] 
 **since** | **str**| The commit ID or ref (exclusively) to retrieve commits after | [optional] 
 **merges** | **str**| If present, controls how merge commits should be filtered. Can be either &lt;code&gt;exclude&lt;/code&gt;, to exclude merge commits, &lt;code&gt;include&lt;/code&gt;, to include both merge commits and non-merge commits or &lt;code&gt;only&lt;/code&gt;, to only return merge commits. | [optional] 
 **ignore_missing** | **str**| &lt;code&gt;true&lt;/code&gt; to ignore missing commits, &lt;code&gt;false&lt;/code&gt; otherwise | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetCommits200Response**](GetCommits200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of commits |  -  |
**400** | One of the supplied commit IDs or refs was invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_configurations1**
> GetConfigurations200Response get_configurations1(project_key, repository_slug, start=start, limit=limit)

Get hook scripts

Return a page of hook scripts configured for the specified repository. 

This endpoint requires **REPO_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.get_configurations200_response import GetConfigurations200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get hook scripts
        api_response = api_instance.get_configurations1(project_key, repository_slug, start=start, limit=limit)
        print("The response of RepositoryApi->get_configurations1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_configurations1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetConfigurations200Response**](GetConfigurations200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of hook scripts. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_content**
> get_content(project_key, repository_slug, no_content=no_content, at=at, size=size, blame=blame, type=type)

Get file content at revision

Retrieve a page of content for a file path at a specified revision. 

Responses from this endpoint vary widely depending on the query parameters. The example JSON is for a request that does not use size, type, blame or noContent. 

1. size will return a response like {"size":10000}
2. type will return a response like {"type":"FILE"}, where possible values are    "DIRECTORY", "FILE" and "SUBMODULE"
3. blame <i>without</i> noContent will include blame for the lines of    content returned on the page
4. blame <i>with</i> noContent will omit file contents and only return    blame for the requested lines
5. noContent without blame is ignored and does nothing


The various parameters are "processed" in the above order. That means ?size=true&amp;type=truewill return a size response, not a type one; the type parameter will be ignored. 

The blame and noContent query parameters are handled differently from size and type. For blame and noContent, the <i>presence</i> of the parameter implies "true" if no value is specified; size and and type both require an explicit=true or they're treated as "false". 

- ?blame is the same as ?blame=true
- ?blame&amp;noContent is the same as ?blame=true&amp;noContent=true
- ?size is the same as ?size=false
- ?type is the same as ?type=false


The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    no_content = 'no_content_example' # str | If blame&amp;noContent only the blame is retrieved instead of the contents (optional)
    at = 'at_example' # str | The commit ID or ref to retrieve the content for (optional)
    size = 'size_example' # str | If true only the size will be returned for the file path instead of the contents (optional)
    blame = 'blame_example' # str | If present and not equal to 'false', the blame will be returned for the file as well (optional)
    type = 'type_example' # str | If true only the type will be returned for the file path instead of the contents (optional)

    try:
        # Get file content at revision
        api_instance.get_content(project_key, repository_slug, no_content=no_content, at=at, size=size, blame=blame, type=type)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_content: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **no_content** | **str**| If blame&amp;amp;noContent only the blame is retrieved instead of the contents | [optional] 
 **at** | **str**| The commit ID or ref to retrieve the content for | [optional] 
 **size** | **str**| If true only the size will be returned for the file path instead of the contents | [optional] 
 **blame** | **str**| If present and not equal to &#39;false&#39;, the blame will be returned for the file as well | [optional] 
 **type** | **str**| If true only the type will be returned for the file path instead of the contents | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of contents from a file. |  -  |
**400** | The path parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_content1**
> get_content1(path, project_key, repository_slug, no_content=no_content, at=at, size=size, blame=blame, type=type)

Get file content

Retrieve a page of content for a file path at a specified revision. 

Responses from this endpoint vary widely depending on the query parameters. The example JSON is for a request that does not use size, type, blame or noContent. 

1. size will return a response like {"size":10000}
2. type will return a response like {"type":"FILE"}, where possible values are    "DIRECTORY", "FILE" and "SUBMODULE"
3. blame <i>without</i> noContent will include blame for the lines of    content returned on the page
4. blame <i>with</i> noContent will omit file contents and only return    blame for the requested lines
5. noContent without blame is ignored and does nothing


The various parameters are "processed" in the above order. That means ?size=true&amp;type=truewill return a size response, not a type one; the type parameter will be ignored. 

The blame and noContent query parameters are handled differently from size and type. For blame and noContent, the <i>presence</i> of the parameter implies "true" if no value is specified; size and and type both require an explicit=true or they're treated as "false". 

- ?blame is the same as ?blame=true
- ?blame&amp;noContent is the same as ?blame=true&amp;noContent=true
- ?size is the same as ?size=false
- ?type is the same as ?type=false


The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The file path to retrieve content from
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    no_content = 'no_content_example' # str | If blame&amp;noContent only the blame is retrieved instead of the contents (optional)
    at = 'at_example' # str | The commit ID or ref to retrieve the content for (optional)
    size = 'size_example' # str | If true only the size will be returned for the file path instead of the contents (optional)
    blame = 'blame_example' # str | If present and not equal to 'false', the blame will be returned for the file as well (optional)
    type = 'type_example' # str | If true only the type will be returned for the file path instead of the contents (optional)

    try:
        # Get file content
        api_instance.get_content1(path, project_key, repository_slug, no_content=no_content, at=at, size=size, blame=blame, type=type)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_content1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The file path to retrieve content from | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **no_content** | **str**| If blame&amp;amp;noContent only the blame is retrieved instead of the contents | [optional] 
 **at** | **str**| The commit ID or ref to retrieve the content for | [optional] 
 **size** | **str**| If true only the size will be returned for the file path instead of the contents | [optional] 
 **blame** | **str**| If present and not equal to &#39;false&#39;, the blame will be returned for the file as well | [optional] 
 **type** | **str**| If true only the type will be returned for the file path instead of the contents | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of contents from a file. |  -  |
**400** | The path or until parameters were not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_default_branch1**
> RestBranch get_default_branch1(project_key, repository_slug)

Get default branch

Retrieves the repository's default branch, if it has been created. If the repository is empty, 204 No Content will be returned. For non-empty repositories, if the configured default branch has not yet been created a 404 Not Found will be returned. 

This URL is deprecated. Callers should use <code>GET /projects/{key}/repos/{slug}/default-branch</code> instead, which allows retrieving the <i>configured</i> default branch even if the ref has not been created yet. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_branch import RestBranch
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get default branch
        api_response = api_instance.get_default_branch1(project_key, repository_slug)
        print("The response of RepositoryApi->get_default_branch1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_default_branch1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestBranch**](RestBranch.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The configured default branch for the repository. |  -  |
**204** | The repository is empty, and has no default branch. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist, or its configured default branch does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_default_tasks1**
> GetDefaultTasks1200Response get_default_tasks1(project_key, repository_slug, markup=markup, start=start, limit=limit)

Get a page of default tasks

Retrieves the default tasks for the supplied repository.

The authenticated user must have **REPO_VIEW** permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .models.get_default_tasks1200_response import GetDefaultTasks1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    markup = 'markup_example' # str | If present or `\"true\"`, includes a markup-rendered description (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get a page of default tasks
        api_response = api_instance.get_default_tasks1(project_key, repository_slug, markup=markup, start=start, limit=limit)
        print("The response of RepositoryApi->get_default_tasks1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_default_tasks1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **markup** | **str**| If present or &#x60;\&quot;true\&quot;&#x60;, includes a markup-rendered description | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetDefaultTasks1200Response**](GetDefaultTasks1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of default tasks |  -  |
**401** | The currently authenticated user has insufficient permissions to delete default tasks |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_diff_stats_summary**
> object get_diff_stats_summary(path, project_key, commit_id, repository_slug, src_path=src_path, auto_src_path=auto_src_path, whitespace=whitespace, since=since)

Get diff stats summary between revisions

Retrieve the diff stats summary for a commit.

The stats summary include the total number of modified files, added lines, and deleted lines. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The path to the file which should be diffed (optional)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The commit ID to diff to.
    repository_slug = 'repository_slug_example' # str | The repository slug
    src_path = 'src_path_example' # str | The source path for the file, if it was copied, moved or renamed (optional)
    auto_src_path = 'auto_src_path_example' # str | <code>true</code> to automatically try to find the source path when it's not provided, <code>false</code> otherwise. Requires the path to be provided. (optional)
    whitespace = 'whitespace_example' # str | Optional whitespace flag which can be set to ignore-all (optional)
    since = 'since_example' # str | The base revision to diff from. If omitted the parent revision of the commit ID is used (optional)

    try:
        # Get diff stats summary between revisions
        api_response = api_instance.get_diff_stats_summary(path, project_key, commit_id, repository_slug, src_path=src_path, auto_src_path=auto_src_path, whitespace=whitespace, since=since)
        print("The response of RepositoryApi->get_diff_stats_summary:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_diff_stats_summary: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The path to the file which should be diffed (optional) | 
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The commit ID to diff to. | 
 **repository_slug** | **str**| The repository slug | 
 **src_path** | **str**| The source path for the file, if it was copied, moved or renamed | [optional] 
 **auto_src_path** | **str**| &lt;code&gt;true&lt;/code&gt; to automatically try to find the source path when it&#39;s not provided, &lt;code&gt;false&lt;/code&gt; otherwise. Requires the path to be provided. | [optional] 
 **whitespace** | **str**| Optional whitespace flag which can be set to ignore-all | [optional] 
 **since** | **str**| The base revision to diff from. If omitted the parent revision of the commit ID is used | [optional] 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The diff stats summary for a commit. |  -  |
**400** | The until parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_diff_stats_summary1**
> RestDiff get_diff_stats_summary1(path, project_key, repository_slug, from_repo=from_repo, src_path=src_path, var_from=var_from, to=to, whitespace=whitespace)

Retrieve the diff stats summary between commits

Retrieve the diff stats summary of the changes available in the <code>from</code> commit but not in the <code> to</code> commit.

If either the <code> from</code> or <code> to</code> commit are not specified, they will be replaced by the default branch of their containing repository.

### Example


```python
import bitbucketclient
from .models.rest_diff import RestDiff
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | the path to the file to diff (optional)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    from_repo = 'from_repo_example' # str | an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID <em>fromRepo=42</em> or by its project key plus its repo slug separated by a slash: <em>fromRepo=projectKey/repoSlug</em> (optional)
    src_path = 'src_path_example' # str | source path (optional)
    var_from = 'var_from_example' # str | the source commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    to = 'to_example' # str | the target commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    whitespace = 'whitespace_example' # str | an optional whitespace flag which can be set to <code>ignore-all</code> (optional)

    try:
        # Retrieve the diff stats summary between commits
        api_response = api_instance.get_diff_stats_summary1(path, project_key, repository_slug, from_repo=from_repo, src_path=src_path, var_from=var_from, to=to, whitespace=whitespace)
        print("The response of RepositoryApi->get_diff_stats_summary1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_diff_stats_summary1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| the path to the file to diff (optional) | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **from_repo** | **str**| an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID &lt;em&gt;fromRepo&#x3D;42&lt;/em&gt; or by its project key plus its repo slug separated by a slash: &lt;em&gt;fromRepo&#x3D;projectKey/repoSlug&lt;/em&gt; | [optional] 
 **src_path** | **str**| source path | [optional] 
 **var_from** | **str**| the source commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **to** | **str**| the target commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **whitespace** | **str**| an optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 

### Return type

[**RestDiff**](RestDiff.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The diff stats summary for the changes. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The source repository,target repository, or commit does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_latest_invocation1**
> RestDetailedInvocation get_latest_invocation1(project_key, webhook_id, repository_slug, event=event, outcome=outcome)

Get last webhook invocation details

Get the latest invocations for a specific webhook. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_detailed_invocation import RestDetailedInvocation
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    repository_slug = 'repository_slug_example' # str | The repository slug.
    event = 'event_example' # str | The string ID of a specific event to retrieve the last invocation for. (optional)
    outcome = 'outcome_example' # str | The outcome to filter for. Can be SUCCESS, FAILURE, ERROR. None specified means that the all will be considered (optional)

    try:
        # Get last webhook invocation details
        api_response = api_instance.get_latest_invocation1(project_key, webhook_id, repository_slug, event=event, outcome=outcome)
        print("The response of RepositoryApi->get_latest_invocation1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_latest_invocation1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **repository_slug** | **str**| The repository slug. | 
 **event** | **str**| The string ID of a specific event to retrieve the last invocation for. | [optional] 
 **outcome** | **str**| The outcome to filter for. Can be SUCCESS, FAILURE, ERROR. None specified means that the all will be considered | [optional] 

### Return type

[**RestDetailedInvocation**](RestDetailedInvocation.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook invocation dataset. |  -  |
**204** | No webhook invocations exist. |  -  |
**401** | The currently authenticated user has insufficient permissions to get webhook invocations in the repository. |  -  |
**404** | The specified repository does not exist, or the webhook does not exist in the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_merge_base**
> RestCommit get_merge_base(project_key, commit_id, repository_slug, other_commit_id=other_commit_id)

Get the common ancestor between two commits

Returns the best common ancestor between two commits.

If more than one best common ancestor exists, only one will be returned. It is unspecified which will be returned.

### Example


```python
import bitbucketclient
from .models.rest_commit import RestCommit
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug
    other_commit_id = 'other_commit_id_example' # str | The other commit id to calculate the merge-base on (optional)

    try:
        # Get the common ancestor between two commits
        api_response = api_instance.get_merge_base(project_key, commit_id, repository_slug, other_commit_id=other_commit_id)
        print("The response of RepositoryApi->get_merge_base:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_merge_base: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 
 **other_commit_id** | **str**| The other commit id to calculate the merge-base on | [optional] 

### Return type

[**RestCommit**](RestCommit.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The common ancestor of the two given commits |  -  |
**204** | No common parent between the two commits exist |  -  |
**400** | The supplied commit ID(s) was/were invalid |  -  |
**404** | The project, repository, or commit(s) does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_pull_request_settings1**
> RestRepositoryPullRequestSettings get_pull_request_settings1(project_key, repository_slug)

Get pull request settings

Retrieve the pull request settings for the context repository. 

The authenticated user must have <strong>REPO_READ</strong> permission for the context repository to call this resource. 

This resource will call all RestFragments that are registered with the key <strong>bitbucket.repository.settings.pullRequests</strong>. If any fragment fails validations by returning a non-empty Map of errors, then no fragments will execute. 

The property keys for the settings that are bundled with the application are 

- mergeConfig - the merge strategy configuration for pull requests
- requiredApprovers - (Deprecated, please use com.atlassian.bitbucket.server.bundled-hooks.requiredApproversMergeHook instead) the number of approvals required on a pull request for it to be mergeable, or 0 if the merge check is disabled
- com.atlassian.bitbucket.server.bundled-hooks.requiredApproversMergeHook - the merge check configuration for required approvers
- requiredAllApprovers - whether or not all approvers must approve a pull request for it to be mergeable
- requiredAllTasksComplete - whether or not all tasks on a pull request need to be completed for it to be mergeable
- requiredSuccessfulBuilds - (Deprecated, please use com.atlassian.bitbucket.server.bitbucket-build.requiredBuildsMergeCheck instead) the number of successful builds on a pull request for it to be mergeable, or 0 if the merge check is disabled
- com.atlassian.bitbucket.server.bitbucket-build.requiredBuildsMergeCheck - the merge check configuration for required builds




### Example


```python
import bitbucketclient
from .models.rest_repository_pull_request_settings import RestRepositoryPullRequestSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get pull request settings
        api_response = api_instance.get_pull_request_settings1(project_key, repository_slug)
        print("The response of RepositoryApi->get_pull_request_settings1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_pull_request_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestRepositoryPullRequestSettings**](RestRepositoryPullRequestSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository pull request settings for the context repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_ref_change_activity**
> GetRefChangeActivity200Response get_ref_change_activity(project_key, repository_slug, ref=ref, start=start, limit=limit)

Get ref change activity

Retrieve a page of repository ref change activity. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission to call this resource.

### Example


```python
import bitbucketclient
from .models.get_ref_change_activity200_response import GetRefChangeActivity200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    ref = 'ref_example' # str | (optional) exact match for a ref ID to filter ref change activity for (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get ref change activity
        api_response = api_instance.get_ref_change_activity(project_key, repository_slug, ref=ref, start=start, limit=limit)
        print("The response of RepositoryApi->get_ref_change_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_ref_change_activity: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **ref** | **str**| (optional) exact match for a ref ID to filter ref change activity for | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRefChangeActivity200Response**](GetRefChangeActivity200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of ref change activity. |  -  |
**401** | The user is currently not authenticated or the user does not have REPO_ADMIN permission. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repositories1**
> GetRepositoriesRecentlyAccessed200Response get_repositories1(archived=archived, projectname=projectname, projectkey=projectkey, visibility=visibility, name=name, permission=permission, state=state, start=start, limit=limit)

Search for repositories

Retrieve a page of repositories based on query parameters that control the search. See the documentation of the parameters for more details. 

This resource is anonymously accessible, if anonymous access is enabled. 

<b>Note on permissions.</b> In absence of the <code>permission</code> query parameter the implicit 'read' permission is assumed. Please note that this permission is lower than the <tt>REPO_READ</tt> permission rather than being equal to it. The implicit 'read' permission for a given repository is assigned to any user that has any of the higher permissions, such as <tt>REPO_READ</tt>, as well as to anonymous users if the repository is marked as public. The important implication of the above is that an anonymous request to this resource with a permission level <tt>REPO_READ</tt> is guaranteed to receive an empty list of repositories as a result. For anonymous requests it is therefore recommended to not specify the <tt>permission</tt> parameter at all.

### Example


```python
import bitbucketclient
from .models.get_repositories_recently_accessed200_response import GetRepositoriesRecentlyAccessed200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    archived = 'archived_example' # str | (optional) if specified, this will limit the resulting repository list to ones whose are <tt>ACTIVE</tt>, <tt>ARCHIVED</tt> or <tt>ALL</tt> for both. The match performed is case-insensitive. This filter defaults to <tt>ACTIVE</tt> when not set. <em>Available since 8.0</em> (optional)
    projectname = 'projectname_example' # str | (optional) if specified, this will limit the resulting repository list to ones whose project's name matches this parameter's value. The match performed is case-insensitive and any leading and/or trailing whitespace characters on the <code>projectname</code> parameter will be stripped. (optional)
    projectkey = 'projectkey_example' # str | (optional) if specified, this will limit the resulting repository list to ones whose project's key matches this parameter's value. The match performed is case-insensitive and any leading  and/or trailing whitespace characters on the <code>projectKey</code> parameter will be stripped. <em>Available since 8.0</em> (optional)
    visibility = 'visibility_example' # str | (optional) if specified, this will limit the resulting repository list based on the repositories visibility. Valid values are <em>public</em> or <em>private</em>. (optional)
    name = 'name_example' # str | (optional) if specified, this will limit the resulting repository list to ones whose name matches this parameter's value. The match performed is case-insensitive and any leading and/or trailing whitespace characters on the <code>name</code> parameter will be stripped. (optional)
    permission = 'permission_example' # str | (optional) if specified, it must be a valid repository permission level name and will limit the resulting repository list to ones that the requesting user has the specified permission level to. If not specified, the default implicit 'read' permission level will be assumed. The currently supported explicit permission values are <tt>REPO_READ</tt>, <tt>REPO_WRITE</tt> and <tt>REPO_ADMIN</tt>. (optional)
    state = 'state_example' # str | (optional) if specified, it must be a valid repository state name and will limit the resulting repository list to ones that are in the specified state. The currently supported explicit state values are <tt>AVAILABLE</tt>, <tt>INITIALISING</tt> and <tt>INITIALISATION_FAILED</tt>. Filtering by <tt>OFFLINE</tt> repositories is not supported.<br><em>Available since 5.13</em> (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Search for repositories
        api_response = api_instance.get_repositories1(archived=archived, projectname=projectname, projectkey=projectkey, visibility=visibility, name=name, permission=permission, state=state, start=start, limit=limit)
        print("The response of RepositoryApi->get_repositories1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_repositories1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **archived** | **str**| (optional) if specified, this will limit the resulting repository list to ones whose are &lt;tt&gt;ACTIVE&lt;/tt&gt;, &lt;tt&gt;ARCHIVED&lt;/tt&gt; or &lt;tt&gt;ALL&lt;/tt&gt; for both. The match performed is case-insensitive. This filter defaults to &lt;tt&gt;ACTIVE&lt;/tt&gt; when not set. &lt;em&gt;Available since 8.0&lt;/em&gt; | [optional] 
 **projectname** | **str**| (optional) if specified, this will limit the resulting repository list to ones whose project&#39;s name matches this parameter&#39;s value. The match performed is case-insensitive and any leading and/or trailing whitespace characters on the &lt;code&gt;projectname&lt;/code&gt; parameter will be stripped. | [optional] 
 **projectkey** | **str**| (optional) if specified, this will limit the resulting repository list to ones whose project&#39;s key matches this parameter&#39;s value. The match performed is case-insensitive and any leading  and/or trailing whitespace characters on the &lt;code&gt;projectKey&lt;/code&gt; parameter will be stripped. &lt;em&gt;Available since 8.0&lt;/em&gt; | [optional] 
 **visibility** | **str**| (optional) if specified, this will limit the resulting repository list based on the repositories visibility. Valid values are &lt;em&gt;public&lt;/em&gt; or &lt;em&gt;private&lt;/em&gt;. | [optional] 
 **name** | **str**| (optional) if specified, this will limit the resulting repository list to ones whose name matches this parameter&#39;s value. The match performed is case-insensitive and any leading and/or trailing whitespace characters on the &lt;code&gt;name&lt;/code&gt; parameter will be stripped. | [optional] 
 **permission** | **str**| (optional) if specified, it must be a valid repository permission level name and will limit the resulting repository list to ones that the requesting user has the specified permission level to. If not specified, the default implicit &#39;read&#39; permission level will be assumed. The currently supported explicit permission values are &lt;tt&gt;REPO_READ&lt;/tt&gt;, &lt;tt&gt;REPO_WRITE&lt;/tt&gt; and &lt;tt&gt;REPO_ADMIN&lt;/tt&gt;. | [optional] 
 **state** | **str**| (optional) if specified, it must be a valid repository state name and will limit the resulting repository list to ones that are in the specified state. The currently supported explicit state values are &lt;tt&gt;AVAILABLE&lt;/tt&gt;, &lt;tt&gt;INITIALISING&lt;/tt&gt; and &lt;tt&gt;INITIALISATION_FAILED&lt;/tt&gt;. Filtering by &lt;tt&gt;OFFLINE&lt;/tt&gt; repositories is not supported.&lt;br&gt;&lt;em&gt;Available since 5.13&lt;/em&gt; | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoriesRecentlyAccessed200Response**](GetRepositoriesRecentlyAccessed200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of repositories. |  -  |
**400** | The &lt;code&gt;visibility&lt;/code&gt; parameter contains an invalid value. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repositories_recently_accessed**
> GetRepositoriesRecentlyAccessed200Response get_repositories_recently_accessed(permission=permission, start=start, limit=limit)

Get recently accessed repositories

Retrieve a page of recently accessed repositories for the currently authenticated user. 

Repositories are ordered from most recently to least recently accessed. <p>Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .models.get_repositories_recently_accessed200_response import GetRepositoriesRecentlyAccessed200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    permission = 'REPO_READ' # str | (optional) If specified, it must be a valid repository permission level name and will limit the resulting repository list to ones that the requesting user has the specified permission level to. If not specified, the default <code>REPO_READ</code> permission level will be assumed. (optional) (default to 'REPO_READ')
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get recently accessed repositories
        api_response = api_instance.get_repositories_recently_accessed(permission=permission, start=start, limit=limit)
        print("The response of RepositoryApi->get_repositories_recently_accessed:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_repositories_recently_accessed: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **permission** | **str**| (optional) If specified, it must be a valid repository permission level name and will limit the resulting repository list to ones that the requesting user has the specified permission level to. If not specified, the default &lt;code&gt;REPO_READ&lt;/code&gt; permission level will be assumed. | [optional] [default to &#39;REPO_READ&#39;]
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoriesRecentlyAccessed200Response**](GetRepositoriesRecentlyAccessed200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of recently accessed repositories. |  -  |
**400** | The permission level is unknown or not related to repository. |  -  |
**401** | The request is unauthenticated. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_hook1**
> RestRepositoryHook get_repository_hook1(project_key, hook_key, repository_slug)

Get repository hook

Retrieve a repository hook for this repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository_hook import RestRepositoryHook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get repository hook
        api_response = api_instance.get_repository_hook1(project_key, hook_key, repository_slug)
        print("The response of RepositoryApi->get_repository_hook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_repository_hook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestRepositoryHook**](RestRepositoryHook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository hooks with their associated enabled state for the supplied hookKey. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the hook. |  -  |
**404** | The specified repository hook does not exist for the given repository, or the repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_repository_hooks1**
> GetRepositoryHooks1200Response get_repository_hooks1(project_key, repository_slug, type=type, start=start, limit=limit)

Get repository hooks

Retrieve a page of repository hooks for this repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_repository_hooks1200_response import GetRepositoryHooks1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    type = 'type_example' # str | The optional type to filter by. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get repository hooks
        api_response = api_instance.get_repository_hooks1(project_key, repository_slug, type=type, start=start, limit=limit)
        print("The response of RepositoryApi->get_repository_hooks1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_repository_hooks1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **type** | **str**| The optional type to filter by. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRepositoryHooks1200Response**](GetRepositoryHooks1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of repository hooks with their associated enabled state. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the hooks. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_restriction1**
> RestRefRestriction get_restriction1(project_key, id, repository_slug)

Get a ref restriction

Returns a restriction as specified by a restriction id. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission or higher to call this resource. Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .models.rest_ref_restriction import RestRefRestriction
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    id = 'id_example' # str | The restriction id.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get a ref restriction
        api_response = api_instance.get_restriction1(project_key, id, repository_slug)
        print("The response of RepositoryApi->get_restriction1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_restriction1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **id** | **str**| The restriction id. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestRefRestriction**](RestRefRestriction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing the restriction. |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user is not permitted to get restrictions on the provided project |  -  |
**404** | No restriction exists for the provided ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_restrictions1**
> GetRestrictions1200Response get_restrictions1(project_key, repository_slug, matcher_type=matcher_type, matcher_id=matcher_id, type=type, start=start, limit=limit)

Search for ref restrictions

Search for restrictions using the supplied parameters.

The authenticated user must have <strong>REPO_ADMIN</strong> permission or higher to call this resource. Only authenticated users may call this resource.

### Example


```python
import bitbucketclient
from .models.get_restrictions1200_response import GetRestrictions1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    matcher_type = 'matcher_type_example' # str | Matcher type to filter on (optional)
    matcher_id = 'matcher_id_example' # str | Matcher id to filter on. Requires the matcherType parameter to be specified also. (optional)
    type = 'type_example' # str | Types of restrictions to filter on. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Search for ref restrictions
        api_response = api_instance.get_restrictions1(project_key, repository_slug, matcher_type=matcher_type, matcher_id=matcher_id, type=type, start=start, limit=limit)
        print("The response of RepositoryApi->get_restrictions1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_restrictions1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **matcher_type** | **str**| Matcher type to filter on | [optional] 
 **matcher_id** | **str**| Matcher id to filter on. Requires the matcherType parameter to be specified also. | [optional] 
 **type** | **str**| Types of restrictions to filter on. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetRestrictions1200Response**](GetRestrictions1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A response containing a page of restrictions. |  -  |
**400** | The request has failed validation. |  -  |
**401** | The currently authenticated user is not permitted to get restrictions on the provided project |  -  |
**404** | No restriction exists for the provided ID. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_settings1**
> ExampleSettings get_settings1(project_key, hook_key, repository_slug)

Get repository hook settings

Retrieve the settings for a repository hook for this repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.example_settings import ExampleSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get repository hook settings
        api_response = api_instance.get_settings1(project_key, hook_key, repository_slug)
        print("The response of RepositoryApi->get_settings1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**ExampleSettings**](ExampleSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The settings for the hook. |  -  |
**401** | The currently authenticated user has insufficient permissions to retrieve the hook settings. |  -  |
**404** | The specified repository or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_statistics1**
> object get_statistics1(project_key, webhook_id, repository_slug, event=event)

Get webhook statistics

Get the statistics for a specific webhook. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    repository_slug = 'repository_slug_example' # str | The repository slug.
    event = 'event_example' # str | The string ID of a specific event to retrieve the last invocation for. May be empty, in which case all events are considered (optional)

    try:
        # Get webhook statistics
        api_response = api_instance.get_statistics1(project_key, webhook_id, repository_slug, event=event)
        print("The response of RepositoryApi->get_statistics1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_statistics1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **repository_slug** | **str**| The repository slug. | 
 **event** | **str**| The string ID of a specific event to retrieve the last invocation for. May be empty, in which case all events are considered | [optional] 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook invocation dataset. |  -  |
**401** | The currently authenticated user has insufficient permissions to get webhook statistics in the repository. |  -  |
**404** | The specified repository does not exist, or the webhook does not exist in the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_statistics_summary1**
> object get_statistics_summary1(project_key, webhook_id, repository_slug)

Get webhook statistics summary

Get the statistics summary for a specific webhook. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get webhook statistics summary
        api_response = api_instance.get_statistics_summary1(project_key, webhook_id, repository_slug)
        print("The response of RepositoryApi->get_statistics_summary1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_statistics_summary1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook invocation dataset. |  -  |
**401** | The currently authenticated user has insufficient permissions to get webhook statistics summary in the repository. |  -  |
**404** | The repository does not exist, or the webhook does not exist in the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_status**
> RestRefSyncStatus get_status(project_key, repository_slug, at=at)

Get synchronization status

Retrieves the synchronization status for the specified repository. In addition to listing refs which cannot be synchronized, if any, the status also provides the timestamp for the most recent synchronization and indicates whether synchronization is available and enabled. If "?at" is specified in the URL, the synchronization status for the specified ref is returned, rather than the complete repository status.

The authenticated user must have <b>REPO_READ</b> permission for the repository, or it must be public if the request is anonymous. Additionally, after synchronization is enabled for a repository, meaning synchronization was available at that time, permission changes and other actions can cause it to become unavailable. Even when synchronization is enabled, if it is no longer available for the repository it will not be performed.

### Example


```python
import bitbucketclient
from .models.rest_ref_sync_status import RestRefSyncStatus
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | Retrieves the synchronization status for the specified ref within the repository, rather than for the entire repository (optional)

    try:
        # Get synchronization status
        api_response = api_instance.get_status(project_key, repository_slug, at=at)
        print("The response of RepositoryApi->get_status:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_status: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| Retrieves the synchronization status for the specified ref within the repository, rather than for the entire repository | [optional] 

### Return type

[**RestRefSyncStatus**](RestRefSyncStatus.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Synchronization status for the specified repository, or specific ref within that repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository, or the repository is not public if the request is anonymous. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_tag**
> RestTag get_tag(project_key, name, repository_slug)

Get tag

Retrieve a tag in the specified repository. 

The authenticated user must have <strong>REPO_READ</strong> permission for the context repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_tag import RestTag
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    name = 'name_example' # str | The name of the tag to be retrieved.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Get tag
        api_response = api_instance.get_tag(project_key, name, repository_slug)
        print("The response of RepositoryApi->get_tag:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_tag: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **name** | **str**| The name of the tag to be retrieved. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestTag**](RestTag.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The tag which matches the supplied &lt;strong&gt;name&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified tag does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_tags**
> GetTags200Response get_tags(project_key, repository_slug, order_by=order_by, filter_text=filter_text, start=start, limit=limit)

Find tag

Retrieve the tags matching the supplied <strong>filterText</strong> param. 

The authenticated user must have <strong>REPO_READ</strong> permission for the context repository to call this resource.

### Example


```python
import bitbucketclient
from .models.get_tags200_response import GetTags200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    order_by = 'order_by_example' # str | Ordering of refs either ALPHABETICAL (by name) or MODIFICATION (last updated) (optional)
    filter_text = 'filter_text_example' # str | The text to match on. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Find tag
        api_response = api_instance.get_tags(project_key, repository_slug, order_by=order_by, filter_text=filter_text, start=start, limit=limit)
        print("The response of RepositoryApi->get_tags:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_tags: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **order_by** | **str**| Ordering of refs either ALPHABETICAL (by name) or MODIFICATION (last updated) | [optional] 
 **filter_text** | **str**| The text to match on. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetTags200Response**](GetTags200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The tags matching the supplied &lt;strong&gt;filterText&lt;/strong&gt;. |  -  |
**401** | The currently authenticated user has insufficient permissions to read the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **get_webhook1**
> RestWebhook get_webhook1(project_key, webhook_id, repository_slug, statistics=statistics)

Get webhook

Get a webhook by ID. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook import RestWebhook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | ID of the webhook
    repository_slug = 'repository_slug_example' # str | The repository slug.
    statistics = 'statistics_example' # str | <code>true</code> if statistics should be provided for the webhook (optional)

    try:
        # Get webhook
        api_response = api_instance.get_webhook1(project_key, webhook_id, repository_slug, statistics=statistics)
        print("The response of RepositoryApi->get_webhook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->get_webhook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| ID of the webhook | 
 **repository_slug** | **str**| The repository slug. | 
 **statistics** | **str**| &lt;code&gt;true&lt;/code&gt; if statistics should be provided for the webhook | [optional] 

### Return type

[**RestWebhook**](RestWebhook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook. |  -  |
**401** | The currently authenticated user has insufficient permissions to get a webhook in the repository. |  -  |
**404** | The repository does not exist, or the webhook does not exist in the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **react**
> RestUserReaction react(project_key, comment_id, commit_id, emoticon, repository_slug)

React to a comment

Add an emoticon reaction to a comment

### Example


```python
import bitbucketclient
from .models.rest_user_reaction import RestUserReaction
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The comment id
    commit_id = 'commit_id_example' # str | The commit id
    emoticon = 'emoticon_example' # str | The emoticon to add
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # React to a comment
        api_response = api_instance.react(project_key, comment_id, commit_id, emoticon, repository_slug)
        print("The response of RepositoryApi->react:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->react: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The comment id | 
 **commit_id** | **str**| The commit id | 
 **emoticon** | **str**| The emoticon to add | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

[**RestUserReaction**](RestUserReaction.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The added reaction |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_configuration1**
> remove_configuration1(project_key, script_id, repository_slug)

Remove a hook script

Removes the hook script from the set of hook scripts configured to run in the repository. 

This endpoint requires **REPO_ADMIN** permission.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    script_id = 'script_id_example' # str | The ID of the hook script
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Remove a hook script
        api_instance.remove_configuration1(project_key, script_id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->remove_configuration1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **script_id** | **str**| The ID of the hook script | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The hook script was successfully deleted. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified repository. |  -  |
**404** | The repository slug or hook script ID supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **remove_label**
> remove_label(project_key, label_name, repository_slug)

Remove repository label

Remove label that is applied to the given repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    label_name = 'label_name_example' # str | The label to remove
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Remove repository label
        api_instance.remove_label(project_key, label_name, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->remove_label: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **label_name** | **str**| The label to remove | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | An empty response indicating that the label is no longer associated to the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to remove the label. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **save_attachment_metadata**
> save_attachment_metadata(project_key, attachment_id, repository_slug, body=body)

Save attachment metadata

Save attachment metadata.

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository that is associated to the attachment that has the attachment metadata.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    attachment_id = 'attachment_id_example' # str | the attachment ID
    repository_slug = 'repository_slug_example' # str | The repository slug
    body = 'body_example' # str | The attachment metadata can be any valid JSON content (optional)

    try:
        # Save attachment metadata
        api_instance.save_attachment_metadata(project_key, attachment_id, repository_slug, body=body)
    except Exception as e:
        print("Exception when calling RepositoryApi->save_attachment_metadata: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **attachment_id** | **str**| the attachment ID | 
 **repository_slug** | **str**| The repository slug | 
 **body** | **str**| The attachment metadata can be any valid JSON content | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The attachment metadata |  -  |
**400** | The supplied content is not valid JSON |  -  |
**401** | The currently authenticated user has insufficient permissions to save theattachment metadata |  -  |
**404** | The repository or the attachment does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **search_webhooks**
> search_webhooks(project_key, repository_slug, scope_type=scope_type, event=event, statistics=statistics)

Search webhooks

Search webhooks in this repository and parent project. This endpoint returns a superset of the results returned by the /webhooks endpoint because it allows filtering by project scope too, not just repository webhooks.

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    scope_type = 'scope_type_example' # str | Scopes to filter by. This parameter can be specified once e.g. \"scopeType=repository\", or twice e.g. \"scopeType=repository&scopeType=project\", to filter by more than one scope level.  (optional)
    event = 'event_example' # str | List of <code>com.atlassian.webhooks.WebhookEvent</code> ids to filter for (optional)
    statistics = True # bool | <code>true</code> if statistics should be provided for all found webhooks (optional)

    try:
        # Search webhooks
        api_instance.search_webhooks(project_key, repository_slug, scope_type=scope_type, event=event, statistics=statistics)
    except Exception as e:
        print("Exception when calling RepositoryApi->search_webhooks: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **scope_type** | **str**| Scopes to filter by. This parameter can be specified once e.g. \&quot;scopeType&#x3D;repository\&quot;, or twice e.g. \&quot;scopeType&#x3D;repository&amp;scopeType&#x3D;project\&quot;, to filter by more than one scope level.  | [optional] 
 **event** | **str**| List of &lt;code&gt;com.atlassian.webhooks.WebhookEvent&lt;/code&gt; ids to filter for | [optional] 
 **statistics** | **bool**| &lt;code&gt;true&lt;/code&gt; if statistics should be provided for all found webhooks | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of webhooks. |  -  |
**401** | The currently authenticated user has insufficient permissions to find webhooks in the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set1**
> RestAutoMergeRestrictedSettings set1(project_key, repository_slug, rest_auto_merge_settings_request=rest_auto_merge_settings_request)

Create or update the pull request auto-merge settings

Creates or updates the pull request auto-merge settings for the supplied repository.

The authenticated user must have <strong>REPO_ADMIN</strong> permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_auto_merge_restricted_settings import RestAutoMergeRestrictedSettings
from .models.rest_auto_merge_settings_request import RestAutoMergeSettingsRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug
    rest_auto_merge_settings_request = .RestAutoMergeSettingsRequest() # RestAutoMergeSettingsRequest | The settings to create or update (optional)

    try:
        # Create or update the pull request auto-merge settings
        api_response = api_instance.set1(project_key, repository_slug, rest_auto_merge_settings_request=rest_auto_merge_settings_request)
        print("The response of RepositoryApi->set1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->set1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 
 **rest_auto_merge_settings_request** | [**RestAutoMergeSettingsRequest**](RestAutoMergeSettingsRequest.md)| The settings to create or update | [optional] 

### Return type

[**RestAutoMergeRestrictedSettings**](RestAutoMergeRestrictedSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The pull request auto-merge settings |  -  |
**400** | The &#39;enabled&#39; field was not provided correctly. |  -  |
**401** | The currently authenticated user has insufficient permissions to create or update the pull request auto-merge settings. |  -  |
**403** | The pull request auto-merge settings cannot be modified due to a restriction enforced by the supplied repository&#39;s project. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_auto_decline_settings1**
> RestAutoDeclineSettings set_auto_decline_settings1(project_key, repository_slug, rest_auto_decline_settings_request=rest_auto_decline_settings_request)

Create auto decline settings

Creates or updates the auto decline settings for the supplied repository.

The authenticated user must have <strong>REPO_ADMIN</strong> permission for this repository to call the resource

### Example


```python
import bitbucketclient
from .models.rest_auto_decline_settings import RestAutoDeclineSettings
from .models.rest_auto_decline_settings_request import RestAutoDeclineSettingsRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug
    rest_auto_decline_settings_request = .RestAutoDeclineSettingsRequest() # RestAutoDeclineSettingsRequest | The settings to create or update (optional)

    try:
        # Create auto decline settings
        api_response = api_instance.set_auto_decline_settings1(project_key, repository_slug, rest_auto_decline_settings_request=rest_auto_decline_settings_request)
        print("The response of RepositoryApi->set_auto_decline_settings1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->set_auto_decline_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 
 **rest_auto_decline_settings_request** | [**RestAutoDeclineSettingsRequest**](RestAutoDeclineSettingsRequest.md)| The settings to create or update | [optional] 

### Return type

[**RestAutoDeclineSettings**](RestAutoDeclineSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The auto decline settings |  -  |
**400** | inactivityWeeks was not one of 1, 2, 4, 8, or, 12, or the enabled parameter was not included in the request. |  -  |
**401** | The currently authenticated user has insufficient permissions to create or update the auto decline settings. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_configuration1**
> RestHookScriptConfig set_configuration1(project_key, script_id, repository_slug, rest_hook_script_triggers=rest_hook_script_triggers)

Create/update a hook script

Creates/updates the hook script configuration for the provided hook script and repository. 

This endpoint requires **REPO_ADMIN** permission.

### Example


```python
import bitbucketclient
from .models.rest_hook_script_config import RestHookScriptConfig
from .models.rest_hook_script_triggers import RestHookScriptTriggers
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    script_id = 'script_id_example' # str | The ID of the hook script
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_hook_script_triggers = .RestHookScriptTriggers() # RestHookScriptTriggers | The hook triggers for which the hook script should be run (optional)

    try:
        # Create/update a hook script
        api_response = api_instance.set_configuration1(project_key, script_id, repository_slug, rest_hook_script_triggers=rest_hook_script_triggers)
        print("The response of RepositoryApi->set_configuration1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->set_configuration1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **script_id** | **str**| The ID of the hook script | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_hook_script_triggers** | [**RestHookScriptTriggers**](RestHookScriptTriggers.md)| The hook triggers for which the hook script should be run | [optional] 

### Return type

[**RestHookScriptConfig**](RestHookScriptConfig.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated hook script. |  -  |
**400** | The hook script was not created/updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the specified repository. |  -  |
**404** | The repository slug supplied does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_default_branch1**
> set_default_branch1(project_key, repository_slug, rest_branch=rest_branch)

Update default branch

Update the default branch of a repository. 

This URL is deprecated. Callers should use <code>PUT /projects/{key}/repos/{slug}/default-branch</code> instead. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_branch import RestBranch
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_branch = .RestBranch() # RestBranch | The branch to set as default (optional)

    try:
        # Update default branch
        api_instance.set_default_branch1(project_key, repository_slug, rest_branch=rest_branch)
    except Exception as e:
        print("Exception when calling RepositoryApi->set_default_branch1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_branch** | [**RestBranch**](RestBranch.md)| The branch to set as default | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The operation was successful. |  -  |
**401** | The currently authenticated user has insufficient permissions to update the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_enabled**
> RestRefSyncStatus set_enabled(project_key, repository_slug, rest_ref_sync_status=rest_ref_sync_status)

Disable synchronization

Enables or disables synchronization for the specified repository. When synchronization is enabled, branches within the repository are immediately synchronized and the status is updated with the outcome. That initial synchronization is performed before the REST request returns, allowing it to return the updated status.

The authenticated user must have <b>REPO_ADMIN</b> permission for the specified repository. Anonymous users cannot manage synchronization, even on public repositories. Additionally, synchronization must be available for the specified repository. Synchronization is only available if:

- The repository is a fork, since its origin is used as upstream
- The owning user still has access to the fork's origin,  if the repository is a <i>personalfork</i>

### Example


```python
import bitbucketclient
from .models.rest_ref_sync_status import RestRefSyncStatus
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_ref_sync_status = .RestRefSyncStatus() # RestRefSyncStatus |  (optional)

    try:
        # Disable synchronization
        api_response = api_instance.set_enabled(project_key, repository_slug, rest_ref_sync_status=rest_ref_sync_status)
        print("The response of RepositoryApi->set_enabled:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->set_enabled: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_ref_sync_status** | [**RestRefSyncStatus**](RestRefSyncStatus.md)|  | [optional] 

### Return type

[**RestRefSyncStatus**](RestRefSyncStatus.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The updated synchronization status for the repository, after enabling synchronization. 204 NO CONTENT is returned instead after disabling synchronization. |  -  |
**204** | Synchronization has successfully been disabled. 200 OK, with updated status information, is returned instead after enabling synchronization. |  -  |
**400** | The JSON payload for the request did not define the \&quot;enabled\&quot; property. |  -  |
**401** | The currently authenticated user has insufficient permissions to manage synchronization in the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **set_settings1**
> ExampleSettings set_settings1(project_key, hook_key, repository_slug, example_settings=example_settings)

Update repository hook settings

Modify the settings for a repository hook for this repository. 

The service will reject any settings which are too large, the current limit is 32KB once serialized. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource. 

A JSON document can be provided to use as the settings for the hook. These structure and validity of the document is decided by the plugin providing the hook.

### Example


```python
import bitbucketclient
from .models.example_settings import ExampleSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    hook_key = 'hook_key_example' # str | The hook key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    example_settings = .ExampleSettings() # ExampleSettings | The raw settings. (optional)

    try:
        # Update repository hook settings
        api_response = api_instance.set_settings1(project_key, hook_key, repository_slug, example_settings=example_settings)
        print("The response of RepositoryApi->set_settings1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->set_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **hook_key** | **str**| The hook key. | 
 **repository_slug** | **str**| The repository slug. | 
 **example_settings** | [**ExampleSettings**](ExampleSettings.md)| The raw settings. | [optional] 

### Return type

[**ExampleSettings**](ExampleSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The settings for the hook. |  -  |
**400** | The settings specified are invalid. |  -  |
**401** | The currently authenticated user has insufficient permissions to modify the hook settings. |  -  |
**404** | The specified repository or hook does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream**
> ExampleFiles stream(project_key, repository_slug, at=at)

Stream files

Streams files from the repository's root with the last commit to modify each file. Commit modifications are traversed starting from the <code>at</code> commit or, if not specified, from the tip of the default branch.

Unless the repository is public, the authenticated user must have <b>REPO_READ</b> access to call this resource.

### Example


```python
import bitbucketclient
from .models.example_files import ExampleFiles
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug
    at = 'at_example' # str | The commit to use as the starting point when listing files and calculating modifications (optional)

    try:
        # Stream files
        api_response = api_instance.stream(project_key, repository_slug, at=at)
        print("The response of RepositoryApi->stream:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 
 **at** | **str**| The commit to use as the starting point when listing files and calculating modifications | [optional] 

### Return type

[**ExampleFiles**](ExampleFiles.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A map of files to the last commit that modified them, and the latest commit to the repository (by nature, any commit to a repository modifies its root). |  -  |
**400** | No &lt;code&gt;at&lt;/code&gt; commit was specified. When streaming modifications, an explicit starting commit must be supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist or does not contain the &lt;code&gt;at&lt;/code&gt; commit. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream1**
> ExampleFiles stream1(path, project_key, repository_slug, at=at)

Stream files with last modified commit in path

Streams files in the requested <code>path</code> with the last commit to modify each file. Commit modifications are traversed starting from the <code>at</code> commit or, if not specified, from the tip of the default branch.

Unless the repository is public, the authenticated user must have <b>REPO_READ</b> access to call this resource.

### Example


```python
import bitbucketclient
from .models.example_files import ExampleFiles
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The path within the repository whose files should be streamed
    project_key = 'project_key_example' # str | The project key
    repository_slug = 'repository_slug_example' # str | The repository slug
    at = 'at_example' # str | The commit to use as the starting point when listing files and calculating modifications (optional)

    try:
        # Stream files with last modified commit in path
        api_response = api_instance.stream1(path, project_key, repository_slug, at=at)
        print("The response of RepositoryApi->stream1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The path within the repository whose files should be streamed | 
 **project_key** | **str**| The project key | 
 **repository_slug** | **str**| The repository slug | 
 **at** | **str**| The commit to use as the starting point when listing files and calculating modifications | [optional] 

### Return type

[**ExampleFiles**](ExampleFiles.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A map of files to the last commit that modified them, and the latest commit to update the requested path. |  -  |
**400** | No &lt;code&gt;at&lt;/code&gt; commit was specified. When streaming modifications, an explicit starting commit must be supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist or does not contain the &lt;code&gt;at&lt;/code&gt; commit, or the &lt;code&gt;at&lt;/code&gt; commit does not contain the requested path. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_changes**
> GetChanges1200Response stream_changes(project_key, repository_slug, from_repo=from_repo, var_from=var_from, to=to, start=start, limit=limit)

Compare commits

Gets the file changes available in the <code> from</code> commit but not in the <code> to</code> commit.


If either the <code> from</code> or <code> to</code> commit are not specified, they will be replaced by the default branch of their containing repository.

### Example


```python
import bitbucketclient
from .models.get_changes1200_response import GetChanges1200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    from_repo = 'from_repo_example' # str | an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID <em>fromRepo=42</em> or by its project key plus its repo slug separated by a slash: <em>fromRepo=projectKey/repoSlug</em> (optional)
    var_from = 'var_from_example' # str | the source commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    to = 'to_example' # str | the target commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Compare commits
        api_response = api_instance.stream_changes(project_key, repository_slug, from_repo=from_repo, var_from=var_from, to=to, start=start, limit=limit)
        print("The response of RepositoryApi->stream_changes:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_changes: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **from_repo** | **str**| an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID &lt;em&gt;fromRepo&#x3D;42&lt;/em&gt; or by its project key plus its repo slug separated by a slash: &lt;em&gt;fromRepo&#x3D;projectKey/repoSlug&lt;/em&gt; | [optional] 
 **var_from** | **str**| the source commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **to** | **str**| the target commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetChanges1200Response**](GetChanges1200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of changes. |  -  |
**404** | The source repository,target repository, or commit does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_commits**
> GetCommits200Response stream_commits(project_key, repository_slug, from_repo=from_repo, var_from=var_from, to=to, start=start, limit=limit)

Get accessible commits

Gets the commits accessible from the <code>from</code> commit but not in the <code>to</code> commit.

If either the <code>from</code> or <code>to</code> commit are not specified, they will be replaced by the default branch of their containing repository.

### Example


```python
import bitbucketclient
from .models.get_commits200_response import GetCommits200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    from_repo = 'from_repo_example' # str | an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID <em>fromRepo=42</em> or by its project key plus its repo slug separated by a slash: <em>fromRepo=projectKey/repoSlug</em> (optional)
    var_from = 'var_from_example' # str | the source commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    to = 'to_example' # str | the target commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get accessible commits
        api_response = api_instance.stream_commits(project_key, repository_slug, from_repo=from_repo, var_from=var_from, to=to, start=start, limit=limit)
        print("The response of RepositoryApi->stream_commits:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_commits: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **from_repo** | **str**| an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID &lt;em&gt;fromRepo&#x3D;42&lt;/em&gt; or by its project key plus its repo slug separated by a slash: &lt;em&gt;fromRepo&#x3D;projectKey/repoSlug&lt;/em&gt; | [optional] 
 **var_from** | **str**| the source commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **to** | **str**| the target commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**GetCommits200Response**](GetCommits200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of commits. |  -  |
**404** | The source repository,target repository, or commit does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_diff**
> RestDiff stream_diff(commit_id, repository_slug, path, project_key, src_path=src_path, avatar_size=avatar_size, filter=filter, avatar_scheme=avatar_scheme, context_lines=context_lines, auto_src_path=auto_src_path, whitespace=whitespace, with_comments=with_comments, since=since)

Get diff between revisions

Retrieve the diff between two provided revisions.

To stream a raw text representation of the diff, this endpoint can be called with the request header 'Accept: text/plain'. 

Note:</strong> This resource is currently <i>not paged</i>. The server will internally apply a hard cap to the streamed lines, and it is not possible to request subsequent pages if that cap is exceeded. In the event that the cap is reached, the diff will be cut short and one or more {@code truncated} flags will be set to true on the "segments", "hunks" and "diffs" properties, as well as the top-level object, in the returned JSON response.

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_diff import RestDiff
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug
    path = 'path_example' # str | The path to the file which should be diffed (optional)
    project_key = 'project_key_example' # str | The project key
    src_path = 'src_path_example' # str | The source path for the file, if it was copied, moved or renamed (optional)
    avatar_size = 'avatar_size_example' # str | If present the service adds avatar URLs for comment authors where the provided value specifies the desired avatar size in pixels. Not applicable if streaming raw diff (optional)
    filter = 'filter_example' # str | Text used to filter files and lines (optional). Not applicable if streaming raw diff (optional)
    avatar_scheme = 'avatar_scheme_example' # str | The security scheme for avatar URLs. If the scheme is not present then it is inherited from the request. It can be set to \"https\" to force the use of secure URLs. Not applicable if streaming raw diff (optional)
    context_lines = 'context_lines_example' # str | The number of context lines to include around added/removed lines in the diff.Not applicable if streaming raw diff (optional)
    auto_src_path = 'auto_src_path_example' # str | <code>true</code> to automatically try to find the source path when it's not provided, <code>false</code> otherwise. Requires the path to be provided. (optional)
    whitespace = 'whitespace_example' # str | Optional whitespace flag which can be set to ignore-all (optional)
    with_comments = 'with_comments_example' # str | <code>true</code> to embed comments in the diff (the default); otherwise <code>false</code> to stream the diff without comments. Not applicable if streaming raw diff (optional)
    since = 'since_example' # str | The base revision to diff from. If omitted the parent revision of the until revision is used (optional)

    try:
        # Get diff between revisions
        api_response = api_instance.stream_diff(commit_id, repository_slug, path, project_key, src_path=src_path, avatar_size=avatar_size, filter=filter, avatar_scheme=avatar_scheme, context_lines=context_lines, auto_src_path=auto_src_path, whitespace=whitespace, with_comments=with_comments, since=since)
        print("The response of RepositoryApi->stream_diff:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_diff: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 
 **path** | **str**| The path to the file which should be diffed (optional) | 
 **project_key** | **str**| The project key | 
 **src_path** | **str**| The source path for the file, if it was copied, moved or renamed | [optional] 
 **avatar_size** | **str**| If present the service adds avatar URLs for comment authors where the provided value specifies the desired avatar size in pixels. Not applicable if streaming raw diff | [optional] 
 **filter** | **str**| Text used to filter files and lines (optional). Not applicable if streaming raw diff | [optional] 
 **avatar_scheme** | **str**| The security scheme for avatar URLs. If the scheme is not present then it is inherited from the request. It can be set to \&quot;https\&quot; to force the use of secure URLs. Not applicable if streaming raw diff | [optional] 
 **context_lines** | **str**| The number of context lines to include around added/removed lines in the diff.Not applicable if streaming raw diff | [optional] 
 **auto_src_path** | **str**| &lt;code&gt;true&lt;/code&gt; to automatically try to find the source path when it&#39;s not provided, &lt;code&gt;false&lt;/code&gt; otherwise. Requires the path to be provided. | [optional] 
 **whitespace** | **str**| Optional whitespace flag which can be set to ignore-all | [optional] 
 **with_comments** | **str**| &lt;code&gt;true&lt;/code&gt; to embed comments in the diff (the default); otherwise &lt;code&gt;false&lt;/code&gt; to stream the diff without comments. Not applicable if streaming raw diff | [optional] 
 **since** | **str**| The base revision to diff from. If omitted the parent revision of the until revision is used | [optional] 

### Return type

[**RestDiff**](RestDiff.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A diff between two revisions. |  -  |
**400** | The until parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_diff1**
> RestDiff stream_diff1(path, project_key, repository_slug, context_lines=context_lines, from_repo=from_repo, src_path=src_path, var_from=var_from, to=to, whitespace=whitespace)

Get diff between commits

Gets a diff of the changes available in the <code>from</code> commit but not in the <code> to</code> commit.

If either the <code> from</code> or <code> to</code> commit are not specified, they will be replaced by the default branch of their containing repository.

### Example


```python
import bitbucketclient
from .models.rest_diff import RestDiff
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | the path to the file to diff (optional)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    context_lines = 'context_lines_example' # str | an optional number of context lines to include around each added or removed lines in the diff (optional)
    from_repo = 'from_repo_example' # str | an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID <em>fromRepo=42</em> or by its project key plus its repo slug separated by a slash: <em>fromRepo=projectKey/repoSlug</em> (optional)
    src_path = 'src_path_example' # str | source path (optional)
    var_from = 'var_from_example' # str | the source commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    to = 'to_example' # str | the target commit (can be a partial/full commit ID or qualified/unqualified ref name) (optional)
    whitespace = 'whitespace_example' # str | an optional whitespace flag which can be set to <code>ignore-all</code> (optional)

    try:
        # Get diff between commits
        api_response = api_instance.stream_diff1(path, project_key, repository_slug, context_lines=context_lines, from_repo=from_repo, src_path=src_path, var_from=var_from, to=to, whitespace=whitespace)
        print("The response of RepositoryApi->stream_diff1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_diff1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| the path to the file to diff (optional) | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **context_lines** | **str**| an optional number of context lines to include around each added or removed lines in the diff | [optional] 
 **from_repo** | **str**| an optional parameter specifying the source repository containing the source commit if that commit is not present in the current repository; the repository can be specified by either its ID &lt;em&gt;fromRepo&#x3D;42&lt;/em&gt; or by its project key plus its repo slug separated by a slash: &lt;em&gt;fromRepo&#x3D;projectKey/repoSlug&lt;/em&gt; | [optional] 
 **src_path** | **str**| source path | [optional] 
 **var_from** | **str**| the source commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **to** | **str**| the target commit (can be a partial/full commit ID or qualified/unqualified ref name) | [optional] 
 **whitespace** | **str**| an optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 

### Return type

[**RestDiff**](RestDiff.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The diff of the changes. |  -  |
**404** | The source repository,target repository, or commit does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_files**
> StreamFiles200Response stream_files(project_key, repository_slug, at=at, start=start, limit=limit)

Get files in directory

Retrieve a page of files from particular directory of a repository. The search is done recursively, so all files from any sub-directory of the specified directory will be returned. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.stream_files200_response import StreamFiles200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | The commit ID or ref (e.g. a branch or tag) to list the files at. If not specified the default branch will be used instead. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get files in directory
        api_response = api_instance.stream_files(project_key, repository_slug, at=at, start=start, limit=limit)
        print("The response of RepositoryApi->stream_files:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_files: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| The commit ID or ref (e.g. a branch or tag) to list the files at. If not specified the default branch will be used instead. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**StreamFiles200Response**](StreamFiles200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of files. |  -  |
**400** | The path parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The path requested does not exist at the supplied commit. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_files1**
> StreamFiles200Response stream_files1(path, project_key, repository_slug, at=at, start=start, limit=limit)

Get files in directory

Retrieve a page of files from particular directory of a repository. The search is done recursively, so all files from any sub-directory of the specified directory will be returned. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.stream_files200_response import StreamFiles200Response
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The directory to list files for.
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | The commit ID or ref (e.g. a branch or tag) to list the files at. If not specified the default branch will be used instead. (optional)
    start = 0 # float | Start number for the page (inclusive). If not passed, first page is assumed. (optional)
    limit = 25 # float | Number of items to return. If not passed, a page size of 25 is used. (optional)

    try:
        # Get files in directory
        api_response = api_instance.stream_files1(path, project_key, repository_slug, at=at, start=start, limit=limit)
        print("The response of RepositoryApi->stream_files1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_files1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The directory to list files for. | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| The commit ID or ref (e.g. a branch or tag) to list the files at. If not specified the default branch will be used instead. | [optional] 
 **start** | **float**| Start number for the page (inclusive). If not passed, first page is assumed. | [optional] 
 **limit** | **float**| Number of items to return. If not passed, a page size of 25 is used. | [optional] 

### Return type

[**StreamFiles200Response**](StreamFiles200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A page of files. |  -  |
**400** | The path requested is not a directory at the supplied commit. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The path requested does not exist at the supplied commit. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_patch**
> stream_patch(project_key, repository_slug, until=until, all_ancestors=all_ancestors, since=since)

Get patch content at revision

Retrieve the patch content for a repository at a specified revision. 

Cache headers are added to the response (only if full commit hashes are used, not in the case of short hashes). 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    until = 'until_example' # str | The target revision from which to generate the patch (required) (optional)
    all_ancestors = 'all_ancestors_example' # str | indicates whether or not to generate a patch which includes all the ancestors of the 'until' revision. If true, the value provided by 'since' is ignored. (optional)
    since = 'since_example' # str | The base revision from which to generate the patch. This is only applicable when 'allAncestors' is false. If omitted the patch will represent one single commit, the 'until'. (optional)

    try:
        # Get patch content at revision
        api_instance.stream_patch(project_key, repository_slug, until=until, all_ancestors=all_ancestors, since=since)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_patch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **until** | **str**| The target revision from which to generate the patch (required) | [optional] 
 **all_ancestors** | **str**| indicates whether or not to generate a patch which includes all the ancestors of the &#39;until&#39; revision. If true, the value provided by &#39;since&#39; is ignored. | [optional] 
 **since** | **str**| The base revision from which to generate the patch. This is only applicable when &#39;allAncestors&#39; is false. If omitted the patch will represent one single commit, the &#39;until&#39;. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The patch contents from a repository. |  -  |
**400** | The until parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_raw**
> stream_raw(path, project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)

Get raw content of a file at revision

Retrieve the raw content for a file path at a specified revision. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The file path to retrieve content from
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    at = 'at_example' # str | A specific commit or ref to retrieve the raw content at, or the default branch if not specified (optional)
    markup = 'markup_example' # str | If present or \"true\", triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than \"true\", the content is streamed without markup (optional)
    html_escape = 'html_escape_example' # str | (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the markup.render.html.escape property, which is true by default, will be used (optional)
    include_heading_id = 'include_heading_id_example' # str | (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the markup.render.headerids property, which is false by default, will be used (optional)
    hardwrap = 'hardwrap_example' # str | (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the markup.render.hardwrap property, which is true by default, will be used (optional)

    try:
        # Get raw content of a file at revision
        api_instance.stream_raw(path, project_key, repository_slug, at=at, markup=markup, html_escape=html_escape, include_heading_id=include_heading_id, hardwrap=hardwrap)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_raw: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The file path to retrieve content from | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **at** | **str**| A specific commit or ref to retrieve the raw content at, or the default branch if not specified | [optional] 
 **markup** | **str**| If present or \&quot;true\&quot;, triggers the raw content to be markup-rendered and returned as HTML; otherwise, if not specified, or any value other than \&quot;true\&quot;, the content is streamed without markup | [optional] 
 **html_escape** | **str**| (Optional) true if HTML should be escaped in the input markup, false otherwise. If not specified, the value of the markup.render.html.escape property, which is true by default, will be used | [optional] 
 **include_heading_id** | **str**| (Optional) true if headings should contain an ID based on the heading content. If not specified, the value of the markup.render.headerids property, which is false by default, will be used | [optional] 
 **hardwrap** | **str**| (Optional) Whether the markup implementation should convert newlines to breaks. If not specified, the value of the markup.render.hardwrap property, which is true by default, will be used | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The raw contents from a file. |  -  |
**400** | The path parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_raw_diff**
> stream_raw_diff(project_key, repository_slug, context_lines=context_lines, src_path=src_path, until=until, whitespace=whitespace, since=since)

Get raw diff for path

Stream the raw diff between two provided revisions. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    context_lines = 'context_lines_example' # str | The number of context lines to include around added/removed lines in the diff (optional)
    src_path = 'src_path_example' # str | The source path for the file, if it was copied, moved or renamed (optional)
    until = 'until_example' # str | The target revision to diff to (required) (optional)
    whitespace = 'whitespace_example' # str | Optional whitespace flag which can be set to <code>ignore-all</code> (optional)
    since = 'since_example' # str | The base revision to diff from. If omitted the parent revision of the until revision is used (optional)

    try:
        # Get raw diff for path
        api_instance.stream_raw_diff(project_key, repository_slug, context_lines=context_lines, src_path=src_path, until=until, whitespace=whitespace, since=since)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_raw_diff: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **context_lines** | **str**| The number of context lines to include around added/removed lines in the diff | [optional] 
 **src_path** | **str**| The source path for the file, if it was copied, moved or renamed | [optional] 
 **until** | **str**| The target revision to diff to (required) | [optional] 
 **whitespace** | **str**| Optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 
 **since** | **str**| The base revision to diff from. If omitted the parent revision of the until revision is used | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain; qs=0.1, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A raw diff between two revisions. |  -  |
**400** | The path parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **stream_raw_diff1**
> stream_raw_diff1(path, project_key, repository_slug, context_lines=context_lines, src_path=src_path, until=until, whitespace=whitespace, since=since)

Get raw diff for path

Stream the raw diff between two provided revisions. 

The authenticated user must have <strong>REPO_READ</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    path = 'path_example' # str | The path to the file which should be diffed (required)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    context_lines = 'context_lines_example' # str | The number of context lines to include around added/removed lines in the diff (optional)
    src_path = 'src_path_example' # str | The source path for the file, if it was copied, moved or renamed (optional)
    until = 'until_example' # str | The target revision to diff to (required) (optional)
    whitespace = 'whitespace_example' # str | Optional whitespace flag which can be set to <code>ignore-all</code> (optional)
    since = 'since_example' # str | The base revision to diff from. If omitted the parent revision of the until revision is used (optional)

    try:
        # Get raw diff for path
        api_instance.stream_raw_diff1(path, project_key, repository_slug, context_lines=context_lines, src_path=src_path, until=until, whitespace=whitespace, since=since)
    except Exception as e:
        print("Exception when calling RepositoryApi->stream_raw_diff1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **path** | **str**| The path to the file which should be diffed (required) | 
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **context_lines** | **str**| The number of context lines to include around added/removed lines in the diff | [optional] 
 **src_path** | **str**| The source path for the file, if it was copied, moved or renamed | [optional] 
 **until** | **str**| The target revision to diff to (required) | [optional] 
 **whitespace** | **str**| Optional whitespace flag which can be set to &lt;code&gt;ignore-all&lt;/code&gt; | [optional] 
 **since** | **str**| The base revision to diff from. If omitted the parent revision of the until revision is used | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain; qs=0.1, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A raw diff between two revisions. |  -  |
**400** | The until parameter was not supplied. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **synchronize**
> RestRejectedRef synchronize(project_key, repository_slug, rest_ref_sync_request=rest_ref_sync_request)

Manual synchronization

Allows developers to apply a manual operation to bring a ref back in sync with upstream when it becomes out of sync due to conflicting changes. The following actions are supported:

- <tt>MERGE</tt>: Merges in commits from the upstream ref. After applying this action, the   synchronized ref will be <tt>AHEAD</tt> (as it still includes commits that do not exist   upstream.
   - This action is only supported for <tt>DIVERGED</tt> refs
   - If a "commitMessage" is provided in the context, it will be used on the merge commit.      Otherwise a default message is used.
- <tt>DISCARD</tt>: <i>Throws away</i> local changes in favour of those made upstream. This is a   <i>destructive</i> operation where commits in the local repository are lost.
   - No context entries are supported for this action
   - If the upstream ref has been deleted, the local ref is deleted as well
   - Otherwise, the local ref is updated to reference the same commit as upstream, even if      the update is not fast-forward (similar to a forced push)


The authenticated user must have <b>REPO_WRITE</b> permission for the specified repository. Anonymous users cannot synchronize refs, even on public repositories. Additionally, synchronization must be <i>enabled</i> and <i>available</i> for the specified repository.

### Example


```python
import bitbucketclient
from .models.rest_ref_sync_request import RestRefSyncRequest
from .models.rest_rejected_ref import RestRejectedRef
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_ref_sync_request = .RestRefSyncRequest() # RestRefSyncRequest |  (optional)

    try:
        # Manual synchronization
        api_response = api_instance.synchronize(project_key, repository_slug, rest_ref_sync_request=rest_ref_sync_request)
        print("The response of RepositoryApi->synchronize:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->synchronize: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_ref_sync_request** | [**RestRefSyncRequest**](RestRefSyncRequest.md)|  | [optional] 

### Return type

[**RestRejectedRef**](RestRejectedRef.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The requested action was successfully performed, and has updated the ref&#39;s state, but the ref if is still not in sync with upstream. For example, after applying the &lt;tt&gt;MERGE&lt;/tt&gt; action, the ref will still be &lt;tt&gt;AHEAD&lt;/tt&gt; of upstream. If the action brings the ref in sync with upstream, 204 NO CONTENT is returned instead. |  -  |
**204** | The requested action was successfully performed and the ref is now in sync with upstream. If the action updates the ref but does not bring it in sync with upstream, 200 OK is returned instead. |  -  |
**400** | The requested synchronization action was not understood. |  -  |
**401** | The currently authenticated user has insufficient permissions to update refs within the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |
**409** | Synchronization is not available or enabled for the specified repository, or the ref is already in sync with upstream. |  -  |
**501** | The requested synchronization action was understood by the server, but the mechanism to apply it has not been implemented. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **test_webhook1**
> object test_webhook1(project_key, repository_slug, webhook_id=webhook_id, ssl_verification_required=ssl_verification_required, url=url, rest_webhook_credentials=rest_webhook_credentials)

Test webhook

Test connectivity to a specific endpoint. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook_credentials import RestWebhookCredentials
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    webhook_id = 56 # int |  (optional)
    ssl_verification_required = 'ssl_verification_required_example' # str | Whether SSL verification is required for the specified webhook URL. Default value is  <code>true</code>. (optional)
    url = 'url_example' # str | The url in which to connect to (optional)
    rest_webhook_credentials = .RestWebhookCredentials() # RestWebhookCredentials | Basic authentication credentials, if required. (optional)

    try:
        # Test webhook
        api_response = api_instance.test_webhook1(project_key, repository_slug, webhook_id=webhook_id, ssl_verification_required=ssl_verification_required, url=url, rest_webhook_credentials=rest_webhook_credentials)
        print("The response of RepositoryApi->test_webhook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->test_webhook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **webhook_id** | **int**|  | [optional] 
 **ssl_verification_required** | **str**| Whether SSL verification is required for the specified webhook URL. Default value is  &lt;code&gt;true&lt;/code&gt;. | [optional] 
 **url** | **str**| The url in which to connect to | [optional] 
 **rest_webhook_credentials** | [**RestWebhookCredentials**](RestWebhookCredentials.md)| Basic authentication credentials, if required. | [optional] 

### Return type

**object**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook. |  -  |
**401** | The currently authenticated user has insufficient permissions to test a connection. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **un_react**
> un_react(project_key, comment_id, commit_id, emoticon, repository_slug)

Remove a reaction from comment

Remove an emoticon reaction from a comment

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    comment_id = 'comment_id_example' # str | The comment id
    commit_id = 'commit_id_example' # str | The commit id
    emoticon = 'emoticon_example' # str | The emoticon to remove
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Remove a reaction from comment
        api_instance.un_react(project_key, comment_id, commit_id, emoticon, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->un_react: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **comment_id** | **str**| The comment id | 
 **commit_id** | **str**| The commit id | 
 **emoticon** | **str**| The emoticon to remove | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The added reaction |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unwatch**
> unwatch(project_key, commit_id, repository_slug)

Stop watching commit

Remove the authenticated user as a watcher for the specified commit.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository containing the commit to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Stop watching commit
        api_instance.unwatch(project_key, commit_id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->unwatch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user is no longer watching the commit. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request. |  -  |
**404** | The specified project, repository or commit does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **unwatch2**
> unwatch2(project_key, repository_slug)

Stop watching repository

Remove the authenticated user as a watcher for the specified repository. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.

    try:
        # Stop watching repository
        api_instance.unwatch2(project_key, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->unwatch2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user is no longer watching the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_comment**
> RestComment update_comment(project_key, comment_id, commit_id, repository_slug, rest_comment=rest_comment)

Update a commit comment

Update a comment, with the following restrictions:

- only the author of the comment may update the <i>text</i> of the comment
- only the author of the comment or repository admins and above may update the other   fields of a comment


<strong>Note:</strong> the supplied supplied JSON object must contain a <code>version</code> that must match the server's version of the comment or the update will fail. To determine the current version of the comment, the comment should be fetched from the server prior to the update. Look for the 'version' attribute in the returned JSON structure.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository that the commit is in to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_comment import RestComment
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    comment_id = 'comment_id_example' # str | The ID of the comment to retrieve
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug
    rest_comment = .RestComment() # RestComment | The comment to update (optional)

    try:
        # Update a commit comment
        api_response = api_instance.update_comment(project_key, comment_id, commit_id, repository_slug, rest_comment=rest_comment)
        print("The response of RepositoryApi->update_comment:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->update_comment: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **comment_id** | **str**| The ID of the comment to retrieve | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 
 **rest_comment** | [**RestComment**](RestComment.md)| The comment to update | [optional] 

### Return type

[**RestComment**](RestComment.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The newly updated comment. |  -  |
**400** | The comment was not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the commit, update the comment or watch the commit. |  -  |
**404** | Unable to find the supplied project, repository, commit or comment. The missing entity will be specified in the error details. |  -  |
**409** | The comment version supplied does not match the current version or the repository is archived. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_default_task1**
> RestDefaultTask update_default_task1(project_key, repository_slug, task_id, rest_default_task_request)

Update a default task

Updates a default task for the supplied repository.

The authenticated user must have **REPO_ADMIN** permission for this repository to call the resource.

### Example


```python
import bitbucketclient
from .models.rest_default_task import RestDefaultTask
from .models.rest_default_task_request import RestDefaultTaskRequest
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    task_id = 'task_id_example' # str | The ID of the default task
    rest_default_task_request = .RestDefaultTaskRequest() # RestDefaultTaskRequest | The task to be updated

    try:
        # Update a default task
        api_response = api_instance.update_default_task1(project_key, repository_slug, task_id, rest_default_task_request)
        print("The response of RepositoryApi->update_default_task1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->update_default_task1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **task_id** | **str**| The ID of the default task | 
 **rest_default_task_request** | [**RestDefaultTaskRequest**](RestDefaultTaskRequest.md)| The task to be updated | 

### Return type

[**RestDefaultTask**](RestDefaultTask.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The default task |  -  |
**400** | One or more of the following error cases occurred (check the error message for more details):    - the provided taskId does not exist- the description is empty- the sourceMatcher or targetMatcher is invalid |  -  |
**401** | The currently authenticated user has insufficient permissions to add a default task |  -  |
**404** | The specified repository does not exist |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_pull_request_settings1**
> RestRepositoryPullRequestSettings update_pull_request_settings1(project_key, repository_slug, rest_repository_pull_request_settings=rest_repository_pull_request_settings)

Update pull request settings

Update the pull request settings for the context repository. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the context repository to call this resource. 

This resource will call all RestFragments that are registered with the key <strong>bitbucket.repository.settings.pullRequests</strong>. If any fragment fails validations by returning a non-empty Map of errors, then no fragments will execute. 

Only the settings that should be updated need to be included in the request. 

The property keys for the settings that are bundled with the application are 

- mergeConfig - the merge strategy configuration for pull requests
- requiredApprovers - (Deprecated, please use com.atlassian.bitbucket.server.bundled-hooks.requiredApproversMergeHook instead) the number of approvals required on a pull request for it to be mergeable, or 0 to disable the merge check
- com.atlassian.bitbucket.server.bundled-hooks.requiredApproversMergeHook - a json map containing the keys 'enabled' (a boolean to enable or disable this merge check) and 'count' (an integer to set the number of required approvals)
- requiredAllApprovers - whether or not all approvers must approve a pull request for it to be mergeable
- requiredAllTasksComplete - whether or not all tasks on a pull request need to be completed for it to be mergeable
- requiredSuccessfulBuilds - (Deprecated, please use com.atlassian.bitbucket.server.bitbucket-build.requiredBuildsMergeCheck instead) the number of successful builds on a pull request for it to be mergeable, or 0 to disable the merge check
- com.atlassian.bitbucket.server.bitbucket-build.requiredBuildsMergeCheck - a json map containing the keys 'enabled' (a boolean to enable or disable this merge check) and 'count' (an integer to set the number of required builds)


<strong>Merge strategy configuration deletion:</strong>

An explicitly set pull request merge strategy configuration can be deleted by POSTing a document with an empty "mergeConfig" attribute. i.e: 


```{ 
    "mergeConfig": { 
    } 
} 
```

Upon completion of this request, the effective configuration will be: 

- The configuration set for this repository's SCM type as set at the project level, if present, otherwise
- the configuration set for this repository's SCM type as set at the instance level, if present, otherwise
- the default configuration for this repository's SCM type




### Example


```python
import bitbucketclient
from .models.rest_repository_pull_request_settings import RestRepositoryPullRequestSettings
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_repository_pull_request_settings = .RestRepositoryPullRequestSettings() # RestRepositoryPullRequestSettings | The updated settings. (optional)

    try:
        # Update pull request settings
        api_response = api_instance.update_pull_request_settings1(project_key, repository_slug, rest_repository_pull_request_settings=rest_repository_pull_request_settings)
        print("The response of RepositoryApi->update_pull_request_settings1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->update_pull_request_settings1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_repository_pull_request_settings** | [**RestRepositoryPullRequestSettings**](RestRepositoryPullRequestSettings.md)| The updated settings. | [optional] 

### Return type

[**RestRepositoryPullRequestSettings**](RestRepositoryPullRequestSettings.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | The repository pull request settings for the context repository. |  -  |
**400** | The repository pull request settings were not updated due to a validation error. |  -  |
**401** | The currently authenticated user has insufficient permissions to see the specified repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **update_webhook1**
> RestWebhook update_webhook1(project_key, webhook_id, repository_slug, rest_webhook=rest_webhook)

Update webhook

Update an existing webhook. 

The authenticated user must have <strong>REPO_ADMIN</strong> permission for the specified repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_webhook import RestWebhook
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    webhook_id = 'webhook_id_example' # str | Id of the existing webhook
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_webhook = .RestWebhook() # RestWebhook | The representation of the updated values for the webhook (optional)

    try:
        # Update webhook
        api_response = api_instance.update_webhook1(project_key, webhook_id, repository_slug, rest_webhook=rest_webhook)
        print("The response of RepositoryApi->update_webhook1:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling RepositoryApi->update_webhook1: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **webhook_id** | **str**| Id of the existing webhook | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_webhook** | [**RestWebhook**](RestWebhook.md)| The representation of the updated values for the webhook | [optional] 

### Return type

[**RestWebhook**](RestWebhook.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json;charset=UTF-8, application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | A webhook. |  -  |
**401** | The currently authenticated user has insufficient permissions to update a webhook in this repository. |  -  |
**404** | The repository does not exist, or the webhook does not exist in the repository. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **watch**
> watch(project_key, commit_id, repository_slug)

Watch commit

Add the authenticated user as a watcher for the specified commit.

The authenticated user must have <strong>REPO_READ</strong> permission for the repository containing the commit to call this resource.

### Example


```python
import bitbucketclient
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key
    commit_id = 'commit_id_example' # str | The <i>full ID</i> of the commit within the repository
    repository_slug = 'repository_slug_example' # str | The repository slug

    try:
        # Watch commit
        api_instance.watch(project_key, commit_id, repository_slug)
    except Exception as e:
        print("Exception when calling RepositoryApi->watch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key | 
 **commit_id** | **str**| The &lt;i&gt;full ID&lt;/i&gt; of the commit within the repository | 
 **repository_slug** | **str**| The repository slug | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user is now watching the commit. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the pull request |  -  |
**404** | The specified project, repository or commit does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **watch2**
> watch2(project_key, repository_slug, rest_repository=rest_repository)

Watch repository

Add the authenticated user as a watcher for the specified repository. 

The authenticated user must have <strong>REPO_READ</strong> permission for the repository to call this resource.

### Example


```python
import bitbucketclient
from .models.rest_repository import RestRepository
from .rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://example.com:7990/rest
# See configuration.py for a list of all supported configuration parameters.
configuration = .Configuration(
    host = "http://example.com:7990/rest"
)


# Enter a context with an instance of the API client
with .ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = .RepositoryApi(api_client)
    project_key = 'project_key_example' # str | The project key.
    repository_slug = 'repository_slug_example' # str | The repository slug.
    rest_repository = .RestRepository() # RestRepository | The repository to watch. (optional)

    try:
        # Watch repository
        api_instance.watch2(project_key, repository_slug, rest_repository=rest_repository)
    except Exception as e:
        print("Exception when calling RepositoryApi->watch2: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **project_key** | **str**| The project key. | 
 **repository_slug** | **str**| The repository slug. | 
 **rest_repository** | [**RestRepository**](RestRepository.md)| The repository to watch. | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | The user is now watching the repository. |  -  |
**401** | The currently authenticated user has insufficient permissions to view the repository. |  -  |
**404** | The specified repository does not exist. |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

