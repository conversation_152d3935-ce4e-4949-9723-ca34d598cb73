# RestReviewerGroupScope


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**resource_id** | **int** |  | [optional] 
**type** | **str** |  | [optional] 

## Example

```python
from .models.rest_reviewer_group_scope import RestReviewerGroupScope

# TODO update the JSON string below
json = "{}"
# create an instance of RestReviewerGroupScope from a JSON string
rest_reviewer_group_scope_instance = RestReviewerGroupScope.from_json(json)
# print the JSON string representation of the object
print(RestReviewerGroupScope.to_json())

# convert the object into a dict
rest_reviewer_group_scope_dict = rest_reviewer_group_scope_instance.to_dict()
# create an instance of RestReviewerGroupScope from a dict
rest_reviewer_group_scope_from_dict = RestReviewerGroupScope.from_dict(rest_reviewer_group_scope_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


