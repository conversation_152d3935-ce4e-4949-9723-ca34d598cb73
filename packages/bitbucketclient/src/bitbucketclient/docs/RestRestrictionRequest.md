# RestRestrictionRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**access_key_ids** | **List[int]** |  | [optional] 
**access_keys** | [**List[RestSshAccessKey]**](RestSshAccessKey.md) |  | [optional] 
**group_names** | **List[str]** |  | [optional] 
**groups** | **List[str]** |  | [optional] 
**id** | **int** |  | [optional] [readonly] 
**matcher** | [**UpdatePullRequestCondition1RequestSourceMatcher**](UpdatePullRequestCondition1RequestSourceMatcher.md) |  | [optional] 
**scope** | [**RestPullRequestConditionScope**](RestPullRequestConditionScope.md) |  | [optional] 
**type** | **str** |  | [optional] 
**user_slugs** | **List[str]** |  | [optional] 
**users** | [**List[RestApplicationUser]**](RestApplicationUser.md) |  | [optional] 

## Example

```python
from .models.rest_restriction_request import RestRestrictionRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RestRestrictionRequest from a JSON string
rest_restriction_request_instance = RestRestrictionRequest.from_json(json)
# print the JSON string representation of the object
print(RestRestrictionRequest.to_json())

# convert the object into a dict
rest_restriction_request_dict = rest_restriction_request_instance.to_dict()
# create an instance of RestRestrictionRequest from a dict
rest_restriction_request_from_dict = RestRestrictionRequest.from_dict(rest_restriction_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


