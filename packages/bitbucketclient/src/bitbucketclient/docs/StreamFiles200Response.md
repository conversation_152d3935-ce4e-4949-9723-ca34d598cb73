# StreamFiles200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**is_last_page** | **bool** |  | [optional] 
**limit** | **float** |  | [optional] 
**next_page_start** | **int** |  | [optional] 
**size** | **float** |  | [optional] 
**start** | **int** |  | [optional] 
**values** | **List[object]** |  | [optional] 

## Example

```python
from .models.stream_files200_response import StreamFiles200Response

# TODO update the JSON string below
json = "{}"
# create an instance of StreamFiles200Response from a JSON string
stream_files200_response_instance = StreamFiles200Response.from_json(json)
# print the JSON string representation of the object
print(StreamFiles200Response.to_json())

# convert the object into a dict
stream_files200_response_dict = stream_files200_response_instance.to_dict()
# create an instance of StreamFiles200Response from a dict
stream_files200_response_from_dict = StreamFiles200Response.from_dict(stream_files200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


