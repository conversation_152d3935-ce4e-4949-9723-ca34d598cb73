# RestChangesetToCommit


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**author** | [**RestChangesetToCommitAuthor**](RestChangesetToCommitAuthor.md) |  | [optional] 
**author_timestamp** | **int** |  | [optional] 
**committer** | [**RestChangesetToCommitAuthor**](RestChangesetToCommitAuthor.md) |  | [optional] 
**committer_timestamp** | **int** |  | [optional] 
**display_id** | **str** |  | [optional] 
**id** | **str** |  | [optional] 
**message** | **str** |  | [optional] 
**parents** | [**List[RestMinimalCommit]**](RestMinimalCommit.md) |  | [optional] 

## Example

```python
from .models.rest_changeset_to_commit import RestChangesetToCommit

# TODO update the JSON string below
json = "{}"
# create an instance of RestChangesetToCommit from a JSON string
rest_changeset_to_commit_instance = RestChangesetToCommit.from_json(json)
# print the JSON string representation of the object
print(RestChangesetToCommit.to_json())

# convert the object into a dict
rest_changeset_to_commit_dict = rest_changeset_to_commit_instance.to_dict()
# create an instance of RestChangesetToCommit from a dict
rest_changeset_to_commit_from_dict = RestChangesetToCommit.from_dict(rest_changeset_to_commit_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


