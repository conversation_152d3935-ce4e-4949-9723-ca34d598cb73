# RestConflictChange


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**path** | [**RestCommentAnchorPath**](RestCommentAnchorPath.md) |  | [optional] 
**src_path** | [**RestCommentAnchorPath**](RestCommentAnchorPath.md) |  | [optional] 
**type** | **str** |  | [optional] 

## Example

```python
from .models.rest_conflict_change import RestConflictChange

# TODO update the JSON string below
json = "{}"
# create an instance of RestConflictChange from a JSON string
rest_conflict_change_instance = RestConflictChange.from_json(json)
# print the JSON string representation of the object
print(RestConflictChange.to_json())

# convert the object into a dict
rest_conflict_change_dict = rest_conflict_change_instance.to_dict()
# create an instance of RestConflictChange from a dict
rest_conflict_change_from_dict = RestConflictChange.from_dict(rest_conflict_change_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


