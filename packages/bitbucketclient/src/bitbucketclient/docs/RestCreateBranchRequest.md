# RestCreateBranchRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**message** | **str** |  | [optional] 
**name** | **str** |  | [optional] 
**start_point** | **str** |  | [optional] 

## Example

```python
from .models.rest_create_branch_request import RestCreateBranchRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RestCreateBranchRequest from a JSON string
rest_create_branch_request_instance = RestCreateBranchRequest.from_json(json)
# print the JSON string representation of the object
print(RestCreateBranchRequest.to_json())

# convert the object into a dict
rest_create_branch_request_dict = rest_create_branch_request_instance.to_dict()
# create an instance of RestCreateBranchRequest from a dict
rest_create_branch_request_from_dict = RestCreateBranchRequest.from_dict(rest_create_branch_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


