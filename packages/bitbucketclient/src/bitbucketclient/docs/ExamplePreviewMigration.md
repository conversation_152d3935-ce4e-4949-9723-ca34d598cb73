# ExamplePreviewMigration


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**repositories** | [**List[RestRepository]**](RestRepository.md) |  | [optional] 

## Example

```python
from .models.example_preview_migration import ExamplePreviewMigration

# TODO update the JSON string below
json = "{}"
# create an instance of ExamplePreviewMigration from a JSON string
example_preview_migration_instance = ExamplePreviewMigration.from_json(json)
# print the JSON string representation of the object
print(ExamplePreviewMigration.to_json())

# convert the object into a dict
example_preview_migration_dict = example_preview_migration_instance.to_dict()
# create an instance of ExamplePreviewMigration from a dict
example_preview_migration_from_dict = ExamplePreviewMigration.from_dict(example_preview_migration_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


