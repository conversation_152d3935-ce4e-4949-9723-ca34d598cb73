# RestAutoDeclineSettingsRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**enabled** | **bool** |  | [optional] 
**inactivity_weeks** | **int** |  | [optional] 

## Example

```python
from .models.rest_auto_decline_settings_request import RestAutoDeclineSettingsRequest

# TODO update the JSON string below
json = "{}"
# create an instance of RestAutoDeclineSettingsRequest from a JSON string
rest_auto_decline_settings_request_instance = RestAutoDeclineSettingsRequest.from_json(json)
# print the JSON string representation of the object
print(RestAutoDeclineSettingsRequest.to_json())

# convert the object into a dict
rest_auto_decline_settings_request_dict = rest_auto_decline_settings_request_instance.to_dict()
# create an instance of RestAutoDeclineSettingsRequest from a dict
rest_auto_decline_settings_request_from_dict = RestAutoDeclineSettingsRequest.from_dict(rest_auto_decline_settings_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


