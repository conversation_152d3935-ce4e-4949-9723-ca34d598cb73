# RestMeshNode


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**availability_zone** | **str** |  | [optional] 
**id** | **str** |  | [optional] 
**last_seen_date** | **float** |  | [optional] 
**name** | **str** |  | [optional] 
**offline** | **bool** |  | [optional] 
**rpc_id** | **str** |  | [optional] 
**rpc_url** | **str** |  | [optional] 
**state** | **str** |  | [optional] 

## Example

```python
from .models.rest_mesh_node import RestMeshNode

# TODO update the JSON string below
json = "{}"
# create an instance of RestMeshNode from a JSON string
rest_mesh_node_instance = RestMeshNode.from_json(json)
# print the JSON string representation of the object
print(RestMeshNode.to_json())

# convert the object into a dict
rest_mesh_node_dict = rest_mesh_node_instance.to_dict()
# create an instance of RestMeshNode from a dict
rest_mesh_node_from_dict = RestMeshNode.from_dict(rest_mesh_node_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


