# RestPermittedGroup


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**group** | [**RestPermittedGroupGroup**](RestPermittedGroupGroup.md) |  | [optional] 
**permission** | **str** |  | [optional] 

## Example

```python
from .models.rest_permitted_group import RestPermittedGroup

# TODO update the JSON string below
json = "{}"
# create an instance of RestPermittedGroup from a JSON string
rest_permitted_group_instance = RestPermittedGroup.from_json(json)
# print the JSON string representation of the object
print(RestPermittedGroup.to_json())

# convert the object into a dict
rest_permitted_group_dict = rest_permitted_group_instance.to_dict()
# create an instance of RestPermittedGroup from a dict
rest_permitted_group_from_dict = RestPermittedGroup.from_dict(rest_permitted_group_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


