# RestPermitted


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**permitted** | **bool** |  | [optional] 

## Example

```python
from .models.rest_permitted import RestPermitted

# TODO update the JSON string below
json = "{}"
# create an instance of RestPermitted from a JSON string
rest_permitted_instance = RestPermitted.from_json(json)
# print the JSON string representation of the object
print(RestPermitted.to_json())

# convert the object into a dict
rest_permitted_dict = rest_permitted_instance.to_dict()
# create an instance of RestPermitted from a dict
rest_permitted_from_dict = RestPermitted.from_dict(rest_permitted_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


