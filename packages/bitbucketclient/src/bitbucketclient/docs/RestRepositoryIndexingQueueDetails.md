# RestRepositoryIndexingQueueDetails


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**captured_at** | **int** | The timestamp indicating when the current queue details were captured. | [optional] 
**node_id** | **str** | The ID of the node associated with the indexing queue. | [optional] 
**queued** | **bool** | Indicates whether the repository is currently queued for indexing. | [optional] 
**queued_at** | **int** | Gets the time at which the repository was added to the indexing queue. If the repository is not present in the queue, this will be empty. | [optional] 

## Example

```python
from .models.rest_repository_indexing_queue_details import RestRepositoryIndexingQueueDetails

# TODO update the JSON string below
json = "{}"
# create an instance of RestRepositoryIndexingQueueDetails from a JSON string
rest_repository_indexing_queue_details_instance = RestRepositoryIndexingQueueDetails.from_json(json)
# print the JSON string representation of the object
print(RestRepositoryIndexingQueueDetails.to_json())

# convert the object into a dict
rest_repository_indexing_queue_details_dict = rest_repository_indexing_queue_details_instance.to_dict()
# create an instance of RestRepositoryIndexingQueueDetails from a dict
rest_repository_indexing_queue_details_from_dict = RestRepositoryIndexingQueueDetails.from_dict(rest_repository_indexing_queue_details_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


