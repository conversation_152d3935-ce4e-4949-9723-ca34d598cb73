# GetPageOfRequiredBuildsMergeChecks200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**is_last_page** | **bool** |  | [optional] 
**limit** | **float** |  | [optional] 
**next_page_start** | **int** |  | [optional] 
**size** | **float** |  | [optional] 
**start** | **int** |  | [optional] 
**values** | [**List[RestRequiredBuildCondition]**](RestRequiredBuildCondition.md) |  | [optional] 

## Example

```python
from .models.get_page_of_required_builds_merge_checks200_response import GetPageOfRequiredBuildsMergeChecks200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GetPageOfRequiredBuildsMergeChecks200Response from a JSON string
get_page_of_required_builds_merge_checks200_response_instance = GetPageOfRequiredBuildsMergeChecks200Response.from_json(json)
# print the JSON string representation of the object
print(GetPageOfRequiredBuildsMergeChecks200Response.to_json())

# convert the object into a dict
get_page_of_required_builds_merge_checks200_response_dict = get_page_of_required_builds_merge_checks200_response_instance.to_dict()
# create an instance of GetPageOfRequiredBuildsMergeChecks200Response from a dict
get_page_of_required_builds_merge_checks200_response_from_dict = GetPageOfRequiredBuildsMergeChecks200Response.from_dict(get_page_of_required_builds_merge_checks200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


