# GroupAndUsers


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**group** | **str** |  | [optional] 
**users** | **List[str]** |  | 

## Example

```python
from .models.group_and_users import GroupAndUsers

# TODO update the JSON string below
json = "{}"
# create an instance of GroupAndUsers from a JSON string
group_and_users_instance = GroupAndUsers.from_json(json)
# print the JSON string representation of the object
print(GroupAndUsers.to_json())

# convert the object into a dict
group_and_users_dict = group_and_users_instance.to_dict()
# create an instance of GroupAndUsers from a dict
group_and_users_from_dict = GroupAndUsers.from_dict(group_and_users_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


