# RestScopesExample


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**links** | **object** |  | [optional] 
**scopes** | **List[object]** |  | [optional] 

## Example

```python
from .models.rest_scopes_example import RestScopesExample

# TODO update the JSON string below
json = "{}"
# create an instance of RestScopesExample from a JSON string
rest_scopes_example_instance = RestScopesExample.from_json(json)
# print the JSON string representation of the object
print(RestScopesExample.to_json())

# convert the object into a dict
rest_scopes_example_dict = rest_scopes_example_instance.to_dict()
# create an instance of RestScopesExample from a dict
rest_scopes_example_from_dict = RestScopesExample.from_dict(rest_scopes_example_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


