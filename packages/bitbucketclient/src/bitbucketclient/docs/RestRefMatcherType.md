# RestRefMatcherType


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** |  | [optional] 
**name** | **str** |  | [optional] 

## Example

```python
from .models.rest_ref_matcher_type import RestRefMatcherType

# TODO update the JSON string below
json = "{}"
# create an instance of RestRefMatcherType from a JSON string
rest_ref_matcher_type_instance = RestRefMatcherType.from_json(json)
# print the JSON string representation of the object
print(RestRefMatcherType.to_json())

# convert the object into a dict
rest_ref_matcher_type_dict = rest_ref_matcher_type_instance.to_dict()
# create an instance of RestRefMatcherType from a dict
rest_ref_matcher_type_from_dict = RestRefMatcherType.from_dict(rest_ref_matcher_type_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


