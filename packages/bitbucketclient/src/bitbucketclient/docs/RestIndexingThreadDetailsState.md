# RestIndexingThreadDetailsState

Represents the state of an indexing thread.

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**code** | **str** | The current code representing the state of the indexing thread | 
**description** | **str** | Additional detail about the current state, meant for informational purposes only. | [optional] 

## Example

```python
from .models.rest_indexing_thread_details_state import RestIndexingThreadDetailsState

# TODO update the JSON string below
json = "{}"
# create an instance of RestIndexingThreadDetailsState from a JSON string
rest_indexing_thread_details_state_instance = RestIndexingThreadDetailsState.from_json(json)
# print the JSON string representation of the object
print(RestIndexingThreadDetailsState.to_json())

# convert the object into a dict
rest_indexing_thread_details_state_dict = rest_indexing_thread_details_state_instance.to_dict()
# create an instance of RestIndexingThreadDetailsState from a dict
rest_indexing_thread_details_state_from_dict = RestIndexingThreadDetailsState.from_dict(rest_indexing_thread_details_state_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


