# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class JitConfigEntity(BaseModel):
    """
    JitConfigEntity
    """ # noqa: E501
    additional_openid_scopes: Optional[List[StrictStr]] = Field(default=None, alias="additional-openid-scopes")
    mapping_display_name: Optional[StrictStr] = Field(default=None, alias="mapping-display-name")
    mapping_email: Optional[StrictStr] = Field(default=None, alias="mapping-email")
    mapping_groups: Optional[StrictStr] = Field(default=None, alias="mapping-groups")
    user_provisioning_enabled: Optional[StrictBool] = Field(default=None, alias="user-provisioning-enabled")
    __properties: ClassVar[List[str]] = ["additional-openid-scopes", "mapping-display-name", "mapping-email", "mapping-groups", "user-provisioning-enabled"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of JitConfigEntity from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of JitConfigEntity from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "additional-openid-scopes": obj.get("additional-openid-scopes"),
            "mapping-display-name": obj.get("mapping-display-name"),
            "mapping-email": obj.get("mapping-email"),
            "mapping-groups": obj.get("mapping-groups"),
            "user-provisioning-enabled": obj.get("user-provisioning-enabled")
        })
        return _obj


