# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from .rest_ref_matcher import RestRefMatcher
from .update_pull_request_condition1_request_source_matcher import UpdatePullRequestCondition1RequestSourceMatcher
from typing import Optional, Set
from typing_extensions import Self

class RestRequiredBuildConditionSetRequest(BaseModel):
    """
    RestRequiredBuildConditionSetRequest
    """ # noqa: E501
    build_parent_keys: Annotated[List[StrictStr], Field(min_length=0, max_length=100)] = Field(description="A non-empty list of build parent keys that require green builds for this merge check to pass", alias="buildParentKeys")
    exempt_ref_matcher: Optional[UpdatePullRequestCondition1RequestSourceMatcher] = Field(default=None, alias="exemptRefMatcher")
    ref_matcher: RestRefMatcher = Field(alias="refMatcher")
    __properties: ClassVar[List[str]] = ["buildParentKeys", "exemptRefMatcher", "refMatcher"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestRequiredBuildConditionSetRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of exempt_ref_matcher
        if self.exempt_ref_matcher:
            _dict['exemptRefMatcher'] = self.exempt_ref_matcher.to_dict()
        # override the default output from pydantic by calling `to_dict()` of ref_matcher
        if self.ref_matcher:
            _dict['refMatcher'] = self.ref_matcher.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestRequiredBuildConditionSetRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "buildParentKeys": obj.get("buildParentKeys"),
            "exemptRefMatcher": UpdatePullRequestCondition1RequestSourceMatcher.from_dict(obj["exemptRefMatcher"]) if obj.get("exemptRefMatcher") is not None else None,
            "refMatcher": RestRefMatcher.from_dict(obj["refMatcher"]) if obj.get("refMatcher") is not None else None
        })
        return _obj


