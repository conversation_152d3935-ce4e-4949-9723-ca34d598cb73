# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from .rest_application_user import RestApplicationUser
from .rest_pull_request_condition_scope import RestPullRequestConditionScope
from .rest_ssh_access_key import RestSshAccessKey
from .update_pull_request_condition1_request_source_matcher import UpdatePullRequestCondition1RequestSourceMatcher
from typing import Optional, Set
from typing_extensions import Self

class RestRestrictionRequest(BaseModel):
    """
    RestRestrictionRequest
    """ # noqa: E501
    access_key_ids: Optional[List[StrictInt]] = Field(default=None, alias="accessKeyIds")
    access_keys: Optional[List[RestSshAccessKey]] = Field(default=None, alias="accessKeys")
    group_names: Optional[List[StrictStr]] = Field(default=None, alias="groupNames")
    groups: Optional[List[StrictStr]] = None
    id: Optional[StrictInt] = None
    matcher: Optional[UpdatePullRequestCondition1RequestSourceMatcher] = None
    scope: Optional[RestPullRequestConditionScope] = None
    type: Optional[StrictStr] = None
    user_slugs: Optional[List[StrictStr]] = Field(default=None, alias="userSlugs")
    users: Optional[List[RestApplicationUser]] = None
    __properties: ClassVar[List[str]] = ["accessKeyIds", "accessKeys", "groupNames", "groups", "id", "matcher", "scope", "type", "userSlugs", "users"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestRestrictionRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * OpenAPI `readOnly` fields are excluded.
        """
        excluded_fields: Set[str] = set([
            "id",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in access_keys (list)
        _items = []
        if self.access_keys:
            for _item_access_keys in self.access_keys:
                if _item_access_keys:
                    _items.append(_item_access_keys.to_dict())
            _dict['accessKeys'] = _items
        # override the default output from pydantic by calling `to_dict()` of matcher
        if self.matcher:
            _dict['matcher'] = self.matcher.to_dict()
        # override the default output from pydantic by calling `to_dict()` of scope
        if self.scope:
            _dict['scope'] = self.scope.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in users (list)
        _items = []
        if self.users:
            for _item_users in self.users:
                if _item_users:
                    _items.append(_item_users.to_dict())
            _dict['users'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestRestrictionRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "accessKeyIds": obj.get("accessKeyIds"),
            "accessKeys": [RestSshAccessKey.from_dict(_item) for _item in obj["accessKeys"]] if obj.get("accessKeys") is not None else None,
            "groupNames": obj.get("groupNames"),
            "groups": obj.get("groups"),
            "id": obj.get("id"),
            "matcher": UpdatePullRequestCondition1RequestSourceMatcher.from_dict(obj["matcher"]) if obj.get("matcher") is not None else None,
            "scope": RestPullRequestConditionScope.from_dict(obj["scope"]) if obj.get("scope") is not None else None,
            "type": obj.get("type"),
            "userSlugs": obj.get("userSlugs"),
            "users": [RestApplicationUser.from_dict(_item) for _item in obj["users"]] if obj.get("users") is not None else None
        })
        return _obj


