# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from .rest_changeset_to_commit_author import RestChangesetToCommitAuthor
from .rest_minimal_commit import RestMinimalCommit
from typing import Optional, Set
from typing_extensions import Self

class RestChangesetToCommit(BaseModel):
    """
    RestChangesetToCommit
    """ # noqa: E501
    author: Optional[RestChangesetToCommitAuthor] = None
    author_timestamp: Optional[StrictInt] = Field(default=None, alias="authorTimestamp")
    committer: Optional[RestChangesetToCommitAuthor] = None
    committer_timestamp: Optional[StrictInt] = Field(default=None, alias="committerTimestamp")
    display_id: Optional[StrictStr] = Field(default=None, alias="displayId")
    id: Optional[StrictStr] = None
    message: Optional[StrictStr] = None
    parents: Optional[List[RestMinimalCommit]] = None
    __properties: ClassVar[List[str]] = ["author", "authorTimestamp", "committer", "committerTimestamp", "displayId", "id", "message", "parents"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestChangesetToCommit from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of author
        if self.author:
            _dict['author'] = self.author.to_dict()
        # override the default output from pydantic by calling `to_dict()` of committer
        if self.committer:
            _dict['committer'] = self.committer.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in parents (list)
        _items = []
        if self.parents:
            for _item_parents in self.parents:
                if _item_parents:
                    _items.append(_item_parents.to_dict())
            _dict['parents'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestChangesetToCommit from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "author": RestChangesetToCommitAuthor.from_dict(obj["author"]) if obj.get("author") is not None else None,
            "authorTimestamp": obj.get("authorTimestamp"),
            "committer": RestChangesetToCommitAuthor.from_dict(obj["committer"]) if obj.get("committer") is not None else None,
            "committerTimestamp": obj.get("committerTimestamp"),
            "displayId": obj.get("displayId"),
            "id": obj.get("id"),
            "message": obj.get("message"),
            "parents": [RestMinimalCommit.from_dict(_item) for _item in obj["parents"]] if obj.get("parents") is not None else None
        })
        return _obj


