# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from .rest_webhook_credentials import RestWebhookCredentials
from typing import Optional, Set
from typing_extensions import Self

class RestWebhook(BaseModel):
    """
    RestWebhook
    """ # noqa: E501
    active: Optional[StrictBool] = None
    configuration: Optional[Dict[str, Any]] = None
    credentials: Optional[RestWebhookCredentials] = None
    events: Optional[List[StrictStr]] = None
    name: Optional[StrictStr] = None
    scope_type: Optional[StrictStr] = Field(default=None, alias="scopeType")
    ssl_verification_required: Optional[StrictBool] = Field(default=None, alias="sslVerificationRequired")
    statistics: Optional[Dict[str, Any]] = None
    url: Optional[StrictStr] = None
    __properties: ClassVar[List[str]] = ["active", "configuration", "credentials", "events", "name", "scopeType", "sslVerificationRequired", "statistics", "url"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestWebhook from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of credentials
        if self.credentials:
            _dict['credentials'] = self.credentials.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestWebhook from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "active": obj.get("active"),
            "configuration": obj.get("configuration"),
            "credentials": RestWebhookCredentials.from_dict(obj["credentials"]) if obj.get("credentials") is not None else None,
            "events": obj.get("events"),
            "name": obj.get("name"),
            "scopeType": obj.get("scopeType"),
            "sslVerificationRequired": obj.get("sslVerificationRequired"),
            "statistics": obj.get("statistics"),
            "url": obj.get("url")
        })
        return _obj


