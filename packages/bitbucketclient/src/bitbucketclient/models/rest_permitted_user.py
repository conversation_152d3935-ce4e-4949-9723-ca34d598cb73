# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from .rest_comment_anchor_pull_request_author_user import RestCommentAnchorPullRequestAuthorUser
from typing import Optional, Set
from typing_extensions import Self

class RestPermittedUser(BaseModel):
    """
    RestPermittedUser
    """ # noqa: E501
    permission: Optional[StrictStr] = None
    user: Optional[RestCommentAnchorPullRequestAuthorUser] = None
    __properties: ClassVar[List[str]] = ["permission", "user"]

    @field_validator('permission')
    def permission_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['USER_ADMIN', 'PROJECT_VIEW', 'REPO_READ', 'REPO_WRITE', 'REPO_ADMIN', 'PROJECT_READ', 'PROJECT_WRITE', 'REPO_CREATE', 'PROJECT_ADMIN', 'LICENSED_USER', 'PROJECT_CREATE', 'ADMIN', 'SYS_ADMIN']):
            raise ValueError("must be one of enum values ('USER_ADMIN', 'PROJECT_VIEW', 'REPO_READ', 'REPO_WRITE', 'REPO_ADMIN', 'PROJECT_READ', 'PROJECT_WRITE', 'REPO_CREATE', 'PROJECT_ADMIN', 'LICENSED_USER', 'PROJECT_CREATE', 'ADMIN', 'SYS_ADMIN')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestPermittedUser from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of user
        if self.user:
            _dict['user'] = self.user.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestPermittedUser from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "permission": obj.get("permission"),
            "user": RestCommentAnchorPullRequestAuthorUser.from_dict(obj["user"]) if obj.get("user") is not None else None
        })
        return _obj


