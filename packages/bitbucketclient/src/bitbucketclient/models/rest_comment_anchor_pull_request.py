# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from .rest_comment_anchor_pull_request_author import RestCommentAnchorPullRequestAuthor
from .rest_comment_anchor_pull_request_from_ref import RestCommentAnchorPullRequestFromRef
from .rest_pull_request_participant import RestPullRequestParticipant
from typing import Optional, Set
from typing_extensions import Self

class RestCommentAnchorPullRequest(BaseModel):
    """
    RestCommentAnchorPullRequest
    """ # noqa: E501
    author: Optional[RestCommentAnchorPullRequestAuthor] = None
    closed: Optional[StrictBool] = None
    closed_date: Optional[StrictInt] = Field(default=None, alias="closedDate")
    created_date: Optional[StrictInt] = Field(default=None, alias="createdDate")
    description: Optional[StrictStr] = None
    description_as_html: Optional[StrictStr] = Field(default=None, alias="descriptionAsHtml")
    draft: Optional[StrictBool] = None
    from_ref: Optional[RestCommentAnchorPullRequestFromRef] = Field(default=None, alias="fromRef")
    html_description: Optional[StrictStr] = Field(default=None, alias="htmlDescription")
    id: Optional[StrictInt] = None
    links: Optional[Dict[str, Any]] = None
    locked: Optional[StrictBool] = None
    open: Optional[StrictBool] = None
    participants: Optional[List[RestPullRequestParticipant]] = None
    reviewers: Optional[List[RestPullRequestParticipant]] = None
    state: Optional[StrictStr] = None
    title: Optional[StrictStr] = None
    to_ref: Optional[RestCommentAnchorPullRequestFromRef] = Field(default=None, alias="toRef")
    updated_date: Optional[StrictInt] = Field(default=None, alias="updatedDate")
    version: Optional[StrictInt] = None
    __properties: ClassVar[List[str]] = ["author", "closed", "closedDate", "createdDate", "description", "descriptionAsHtml", "draft", "fromRef", "htmlDescription", "id", "links", "locked", "open", "participants", "reviewers", "state", "title", "toRef", "updatedDate", "version"]

    @field_validator('state')
    def state_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['DECLINED', 'MERGED', 'OPEN']):
            raise ValueError("must be one of enum values ('DECLINED', 'MERGED', 'OPEN')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestCommentAnchorPullRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of author
        if self.author:
            _dict['author'] = self.author.to_dict()
        # override the default output from pydantic by calling `to_dict()` of from_ref
        if self.from_ref:
            _dict['fromRef'] = self.from_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in participants (list)
        _items = []
        if self.participants:
            for _item_participants in self.participants:
                if _item_participants:
                    _items.append(_item_participants.to_dict())
            _dict['participants'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in reviewers (list)
        _items = []
        if self.reviewers:
            for _item_reviewers in self.reviewers:
                if _item_reviewers:
                    _items.append(_item_reviewers.to_dict())
            _dict['reviewers'] = _items
        # override the default output from pydantic by calling `to_dict()` of to_ref
        if self.to_ref:
            _dict['toRef'] = self.to_ref.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestCommentAnchorPullRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "author": RestCommentAnchorPullRequestAuthor.from_dict(obj["author"]) if obj.get("author") is not None else None,
            "closed": obj.get("closed"),
            "closedDate": obj.get("closedDate"),
            "createdDate": obj.get("createdDate"),
            "description": obj.get("description"),
            "descriptionAsHtml": obj.get("descriptionAsHtml"),
            "draft": obj.get("draft"),
            "fromRef": RestCommentAnchorPullRequestFromRef.from_dict(obj["fromRef"]) if obj.get("fromRef") is not None else None,
            "htmlDescription": obj.get("htmlDescription"),
            "id": obj.get("id"),
            "links": obj.get("links"),
            "locked": obj.get("locked"),
            "open": obj.get("open"),
            "participants": [RestPullRequestParticipant.from_dict(_item) for _item in obj["participants"]] if obj.get("participants") is not None else None,
            "reviewers": [RestPullRequestParticipant.from_dict(_item) for _item in obj["reviewers"]] if obj.get("reviewers") is not None else None,
            "state": obj.get("state"),
            "title": obj.get("title"),
            "toRef": RestCommentAnchorPullRequestFromRef.from_dict(obj["toRef"]) if obj.get("toRef") is not None else None,
            "updatedDate": obj.get("updatedDate"),
            "version": obj.get("version")
        })
        return _obj


