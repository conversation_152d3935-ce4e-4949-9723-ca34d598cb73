# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from .rest_changeset_repository_origin import RestChangesetRepositoryOrigin
from .rest_changeset_repository_origin_project import RestChangesetRepositoryOriginProject
from typing import Optional, Set
from typing_extensions import Self

class RestBrokenIndexStatusRepositoryRepository(BaseModel):
    """
    The repository which has entered a broken status
    """ # noqa: E501
    archived: Optional[StrictBool] = None
    default_branch: Optional[StrictStr] = Field(default=None, alias="defaultBranch")
    description: Optional[StrictStr] = None
    forkable: Optional[StrictBool] = None
    hierarchy_id: Optional[StrictStr] = Field(default=None, alias="hierarchyId")
    id: Optional[StrictInt] = None
    links: Optional[Dict[str, Any]] = None
    name: Optional[StrictStr] = None
    origin: Optional[RestChangesetRepositoryOrigin] = None
    partition: Optional[StrictInt] = None
    project: Optional[RestChangesetRepositoryOriginProject] = None
    public: Optional[StrictBool] = None
    related_links: Optional[Dict[str, Any]] = Field(default=None, alias="relatedLinks")
    scm_id: Optional[StrictStr] = Field(default=None, alias="scmId")
    scope: Optional[StrictStr] = None
    slug: Optional[StrictStr] = None
    state: Optional[StrictStr] = None
    status_message: Optional[StrictStr] = Field(default=None, alias="statusMessage")
    __properties: ClassVar[List[str]] = ["archived", "defaultBranch", "description", "forkable", "hierarchyId", "id", "links", "name", "origin", "partition", "project", "public", "relatedLinks", "scmId", "scope", "slug", "state", "statusMessage"]

    @field_validator('state')
    def state_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['AVAILABLE', 'INITIALISATION_FAILED', 'INITIALISING', 'OFFLINE']):
            raise ValueError("must be one of enum values ('AVAILABLE', 'INITIALISATION_FAILED', 'INITIALISING', 'OFFLINE')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestBrokenIndexStatusRepositoryRepository from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        * OpenAPI `readOnly` fields are excluded.
        """
        excluded_fields: Set[str] = set([
            "archived",
            "description",
            "forkable",
            "hierarchy_id",
            "id",
            "partition",
            "public",
            "related_links",
            "scope",
            "state",
            "status_message",
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of origin
        if self.origin:
            _dict['origin'] = self.origin.to_dict()
        # override the default output from pydantic by calling `to_dict()` of project
        if self.project:
            _dict['project'] = self.project.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestBrokenIndexStatusRepositoryRepository from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "archived": obj.get("archived"),
            "defaultBranch": obj.get("defaultBranch"),
            "description": obj.get("description"),
            "forkable": obj.get("forkable"),
            "hierarchyId": obj.get("hierarchyId"),
            "id": obj.get("id"),
            "links": obj.get("links"),
            "name": obj.get("name"),
            "origin": RestChangesetRepositoryOrigin.from_dict(obj["origin"]) if obj.get("origin") is not None else None,
            "partition": obj.get("partition"),
            "project": RestChangesetRepositoryOriginProject.from_dict(obj["project"]) if obj.get("project") is not None else None,
            "public": obj.get("public"),
            "relatedLinks": obj.get("relatedLinks"),
            "scmId": obj.get("scmId"),
            "scope": obj.get("scope"),
            "slug": obj.get("slug"),
            "state": obj.get("state"),
            "statusMessage": obj.get("statusMessage")
        })
        return _obj


