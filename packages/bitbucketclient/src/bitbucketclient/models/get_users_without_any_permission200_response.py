# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictFloat, StrictInt
from typing import Any, ClassVar, Dict, List, Optional, Union
from .rest_application_user import RestApplicationUser
from typing import Optional, Set
from typing_extensions import Self

class GetUsersWithoutAnyPermission200Response(BaseModel):
    """
    GetUsersWithoutAnyPermission200Response
    """ # noqa: E501
    is_last_page: Optional[StrictBool] = Field(default=None, alias="isLastPage")
    limit: Optional[Union[StrictFloat, StrictInt]] = None
    next_page_start: Optional[StrictInt] = Field(default=None, alias="nextPageStart")
    size: Optional[Union[StrictFloat, StrictInt]] = None
    start: Optional[StrictInt] = None
    values: Optional[List[RestApplicationUser]] = None
    __properties: ClassVar[List[str]] = ["isLastPage", "limit", "nextPageStart", "size", "start", "values"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of GetUsersWithoutAnyPermission200Response from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in values (list)
        _items = []
        if self.values:
            for _item_values in self.values:
                if _item_values:
                    _items.append(_item_values.to_dict())
            _dict['values'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of GetUsersWithoutAnyPermission200Response from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "isLastPage": obj.get("isLastPage"),
            "limit": obj.get("limit"),
            "nextPageStart": obj.get("nextPageStart"),
            "size": obj.get("size"),
            "start": obj.get("start"),
            "values": [RestApplicationUser.from_dict(_item) for _item in obj["values"]] if obj.get("values") is not None else None
        })
        return _obj


