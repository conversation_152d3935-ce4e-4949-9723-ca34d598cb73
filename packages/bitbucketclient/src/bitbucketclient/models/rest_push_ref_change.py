# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from .rest_pull_request_rebase_result_ref_change_ref import RestPullRequestRebaseResultRefChangeRef
from typing import Optional, Set
from typing_extensions import Self

class RestPushRefChange(BaseModel):
    """
    RestPushRefChange
    """ # noqa: E501
    from_hash: Optional[StrictStr] = Field(default=None, alias="fromHash")
    ref: Optional[RestPullRequestRebaseResultRefChangeRef] = None
    ref_id: Optional[StrictStr] = Field(default=None, alias="refId")
    to_hash: Optional[StrictStr] = Field(default=None, alias="toHash")
    type: Optional[StrictStr] = None
    updated_type: Optional[StrictStr] = Field(default=None, alias="updatedType")
    __properties: ClassVar[List[str]] = ["fromHash", "ref", "refId", "toHash", "type", "updatedType"]

    @field_validator('type')
    def type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['ADD', 'DELETE', 'UPDATE']):
            raise ValueError("must be one of enum values ('ADD', 'DELETE', 'UPDATE')")
        return value

    @field_validator('updated_type')
    def updated_type_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['UNKNOWN', 'UNRESOLVED', 'NOT_FORCED', 'FORCED']):
            raise ValueError("must be one of enum values ('UNKNOWN', 'UNRESOLVED', 'NOT_FORCED', 'FORCED')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestPushRefChange from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of ref
        if self.ref:
            _dict['ref'] = self.ref.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestPushRefChange from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "fromHash": obj.get("fromHash"),
            "ref": RestPullRequestRebaseResultRefChangeRef.from_dict(obj["ref"]) if obj.get("ref") is not None else None,
            "refId": obj.get("refId"),
            "toHash": obj.get("toHash"),
            "type": obj.get("type"),
            "updatedType": obj.get("updatedType")
        })
        return _obj


