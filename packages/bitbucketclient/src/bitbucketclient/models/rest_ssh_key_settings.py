# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt
from typing import Any, ClassVar, Dict, List, Optional
from .rest_ssh_key_type_restriction import RestSshKeyTypeRestriction
from typing import Optional, Set
from typing_extensions import Self

class RestSshKeySettings(BaseModel):
    """
    RestSshKeySettings
    """ # noqa: E501
    key_type_restrictions: Optional[List[RestSshKeyTypeRestriction]] = Field(default=None, alias="keyTypeRestrictions")
    max_expiry_days: Optional[StrictInt] = Field(default=None, alias="maxExpiryDays")
    __properties: ClassVar[List[str]] = ["keyTypeRestrictions", "maxExpiryDays"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestSshKeySettings from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in key_type_restrictions (list)
        _items = []
        if self.key_type_restrictions:
            for _item_key_type_restrictions in self.key_type_restrictions:
                if _item_key_type_restrictions:
                    _items.append(_item_key_type_restrictions.to_dict())
            _dict['keyTypeRestrictions'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestSshKeySettings from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "keyTypeRestrictions": [RestSshKeyTypeRestriction.from_dict(_item) for _item in obj["keyTypeRestrictions"]] if obj.get("keyTypeRestrictions") is not None else None,
            "maxExpiryDays": obj.get("maxExpiryDays")
        })
        return _obj


