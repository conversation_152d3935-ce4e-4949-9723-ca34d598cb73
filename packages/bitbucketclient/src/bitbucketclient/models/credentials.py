# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import json
import pprint
from pydantic import BaseModel, ConfigDict, Field, StrictStr, ValidationError, field_validator
from typing import Any, List, Optional
from .rest_bearer_token_credentials import RestBearerTokenCredentials
from .rest_ssh_credentials import RestSshCredentials
from .rest_username_password_credentials import RestUsernamePasswordCredentials
from pydantic import StrictStr, <PERSON>
from typing import Union, List, Set, Optional, Dict
from typing_extensions import Literal, Self

CREDENTIALS_ONE_OF_SCHEMAS = ["RestBearerTokenCredentials", "RestSshCredentials", "RestUsernamePasswordCredentials"]

class Credentials(BaseModel):
    """
    Credentials
    """
    # data type: RestUsernamePasswordCredentials
    oneof_schema_1_validator: Optional[RestUsernamePasswordCredentials] = None
    # data type: RestBearerTokenCredentials
    oneof_schema_2_validator: Optional[RestBearerTokenCredentials] = None
    # data type: RestSshCredentials
    oneof_schema_3_validator: Optional[RestSshCredentials] = None
    actual_instance: Optional[Union[RestBearerTokenCredentials, RestSshCredentials, RestUsernamePasswordCredentials]] = None
    one_of_schemas: Set[str] = { "RestBearerTokenCredentials", "RestSshCredentials", "RestUsernamePasswordCredentials" }

    model_config = ConfigDict(
        validate_assignment=True,
        protected_namespaces=(),
    )


    def __init__(self, *args, **kwargs) -> None:
        if args:
            if len(args) > 1:
                raise ValueError("If a position argument is used, only 1 is allowed to set `actual_instance`")
            if kwargs:
                raise ValueError("If a position argument is used, keyword arguments cannot be used.")
            super().__init__(actual_instance=args[0])
        else:
            super().__init__(**kwargs)

    @field_validator('actual_instance')
    def actual_instance_must_validate_oneof(cls, v):
        instance = Credentials.model_construct()
        error_messages = []
        match = 0
        # validate data type: RestUsernamePasswordCredentials
        if not isinstance(v, RestUsernamePasswordCredentials):
            error_messages.append(f"Error! Input type `{type(v)}` is not `RestUsernamePasswordCredentials`")
        else:
            match += 1
        # validate data type: RestBearerTokenCredentials
        if not isinstance(v, RestBearerTokenCredentials):
            error_messages.append(f"Error! Input type `{type(v)}` is not `RestBearerTokenCredentials`")
        else:
            match += 1
        # validate data type: RestSshCredentials
        if not isinstance(v, RestSshCredentials):
            error_messages.append(f"Error! Input type `{type(v)}` is not `RestSshCredentials`")
        else:
            match += 1
        if match > 1:
            # more than 1 match
            raise ValueError("Multiple matches found when setting `actual_instance` in Credentials with oneOf schemas: RestBearerTokenCredentials, RestSshCredentials, RestUsernamePasswordCredentials. Details: " + ", ".join(error_messages))
        elif match == 0:
            # no match
            raise ValueError("No match found when setting `actual_instance` in Credentials with oneOf schemas: RestBearerTokenCredentials, RestSshCredentials, RestUsernamePasswordCredentials. Details: " + ", ".join(error_messages))
        else:
            return v

    @classmethod
    def from_dict(cls, obj: Union[str, Dict[str, Any]]) -> Self:
        return cls.from_json(json.dumps(obj))

    @classmethod
    def from_json(cls, json_str: str) -> Self:
        """Returns the object represented by the json string"""
        instance = cls.model_construct()
        error_messages = []
        match = 0

        # deserialize data into RestUsernamePasswordCredentials
        try:
            instance.actual_instance = RestUsernamePasswordCredentials.from_json(json_str)
            match += 1
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))
        # deserialize data into RestBearerTokenCredentials
        try:
            instance.actual_instance = RestBearerTokenCredentials.from_json(json_str)
            match += 1
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))
        # deserialize data into RestSshCredentials
        try:
            instance.actual_instance = RestSshCredentials.from_json(json_str)
            match += 1
        except (ValidationError, ValueError) as e:
            error_messages.append(str(e))

        if match > 1:
            # more than 1 match
            raise ValueError("Multiple matches found when deserializing the JSON string into Credentials with oneOf schemas: RestBearerTokenCredentials, RestSshCredentials, RestUsernamePasswordCredentials. Details: " + ", ".join(error_messages))
        elif match == 0:
            # no match
            raise ValueError("No match found when deserializing the JSON string into Credentials with oneOf schemas: RestBearerTokenCredentials, RestSshCredentials, RestUsernamePasswordCredentials. Details: " + ", ".join(error_messages))
        else:
            return instance

    def to_json(self) -> str:
        """Returns the JSON representation of the actual instance"""
        if self.actual_instance is None:
            return "null"

        if hasattr(self.actual_instance, "to_json") and callable(self.actual_instance.to_json):
            return self.actual_instance.to_json()
        else:
            return json.dumps(self.actual_instance)

    def to_dict(self) -> Optional[Union[Dict[str, Any], RestBearerTokenCredentials, RestSshCredentials, RestUsernamePasswordCredentials]]:
        """Returns the dict representation of the actual instance"""
        if self.actual_instance is None:
            return None

        if hasattr(self.actual_instance, "to_dict") and callable(self.actual_instance.to_dict):
            return self.actual_instance.to_dict()
        else:
            # primitive type
            return self.actual_instance

    def to_str(self) -> str:
        """Returns the string representation of the actual instance"""
        return pprint.pformat(self.model_dump())


