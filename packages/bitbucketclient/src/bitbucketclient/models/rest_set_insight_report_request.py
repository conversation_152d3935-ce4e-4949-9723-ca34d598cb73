# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing_extensions import Annotated
from .rest_insight_report_data import RestInsightReportData
from typing import Optional, Set
from typing_extensions import Self

class RestSetInsightReportRequest(BaseModel):
    """
    RestSetInsightReportRequest
    """ # noqa: E501
    coverage_provider_key: Optional[StrictStr] = Field(default=None, alias="coverageProviderKey")
    created_date: Optional[StrictInt] = Field(default=None, alias="createdDate")
    data: Annotated[List[RestInsightReportData], Field(min_length=0, max_length=6)]
    details: Optional[StrictStr] = None
    link: Optional[StrictStr] = None
    logo_url: Optional[StrictStr] = Field(default=None, alias="logoUrl")
    reporter: Optional[Annotated[str, Field(min_length=0, strict=True, max_length=450)]] = None
    result: Optional[Annotated[str, Field(strict=True)]] = None
    title: Annotated[str, Field(min_length=0, strict=True, max_length=450)]
    __properties: ClassVar[List[str]] = ["coverageProviderKey", "createdDate", "data", "details", "link", "logoUrl", "reporter", "result", "title"]

    @field_validator('result')
    def result_validate_regular_expression(cls, value):
        """Validates the regular expression"""
        if value is None:
            return value

        if not re.match(r"FAIL|PASS", value):
            raise ValueError(r"must validate the regular expression /FAIL|PASS/")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of RestSetInsightReportRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in data (list)
        _items = []
        if self.data:
            for _item_data in self.data:
                if _item_data:
                    _items.append(_item_data.to_dict())
            _dict['data'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of RestSetInsightReportRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "coverageProviderKey": obj.get("coverageProviderKey"),
            "createdDate": obj.get("createdDate"),
            "data": [RestInsightReportData.from_dict(_item) for _item in obj["data"]] if obj.get("data") is not None else None,
            "details": obj.get("details"),
            "link": obj.get("link"),
            "logoUrl": obj.get("logoUrl"),
            "reporter": obj.get("reporter"),
            "result": obj.get("result"),
            "title": obj.get("title")
        })
        return _obj


