# coding: utf-8

"""
    Bitbucket Data Center

    This is the reference document for the Atlassian Bitbucket REST API. The REST API is for developers who want to:    - integrate Bitbucket with other applications;   - create scripts that interact with Bitbucket; or   - develop plugins that enhance the Bitbucket UI, using REST to interact with the backend.    You can read more about developing Bitbucket plugins in the [Bitbucket Developer Documentation](https://developer.atlassian.com/bitbucket/server/docs/latest/).

    The version of the OpenAPI document: 9.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501

import warnings
from pydantic import validate_call, Field, StrictFloat, StrictStr, StrictInt
from typing import Any, Dict, List, Optional, Tuple, Union
from typing_extensions import Annotated

from pydantic import Field, StrictStr
from typing import Optional
from typing_extensions import Annotated
from ..models.rest_markup import RestMarkup

from ..api_client import ApiClient, RequestSerialized
from ..api_response import ApiResponse
from ..rest import RESTResponseType


class MarkupApi:
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    def __init__(self, api_client=None) -> None:
        if api_client is None:
            api_client = ApiClient.get_default()
        self.api_client = api_client


    @validate_call
    def preview(
        self,
        html_escape: Annotated[Optional[StrictStr], Field(description="(Optional) true if HTML should be escaped in the input markup, false otherwise.")] = None,
        url_mode: Annotated[Optional[StrictStr], Field(description="(Optional) The mode to use when building URLs. One of: ABSOLUTE, RELATIVE or, CONFIGURED. By default this is RELATIVE.")] = None,
        include_heading_id: Annotated[Optional[StrictStr], Field(description="(Optional) true if headers should contain an ID based on the heading content.")] = None,
        hardwrap: Annotated[Optional[StrictStr], Field(description="(Optional) Whether the markup implementation should convert newlines to breaks. By default this is false which reflects the standard markdown specification.")] = None,
        body: Optional[StrictStr] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RestMarkup:
        """Preview markdown render

        Preview generated HTML for the given markdown content.  Only authenticated users may call this resource.

        :param html_escape: (Optional) true if HTML should be escaped in the input markup, false otherwise.
        :type html_escape: str
        :param url_mode: (Optional) The mode to use when building URLs. One of: ABSOLUTE, RELATIVE or, CONFIGURED. By default this is RELATIVE.
        :type url_mode: str
        :param include_heading_id: (Optional) true if headers should contain an ID based on the heading content.
        :type include_heading_id: str
        :param hardwrap: (Optional) Whether the markup implementation should convert newlines to breaks. By default this is false which reflects the standard markdown specification.
        :type hardwrap: str
        :param body:
        :type body: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._preview_serialize(
            html_escape=html_escape,
            url_mode=url_mode,
            include_heading_id=include_heading_id,
            hardwrap=hardwrap,
            body=body,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "RestMarkup",
            '400': "GetAllAccessTokens401Response",
            '401': "GetAllAccessTokens401Response",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        ).data


    @validate_call
    def preview_with_http_info(
        self,
        html_escape: Annotated[Optional[StrictStr], Field(description="(Optional) true if HTML should be escaped in the input markup, false otherwise.")] = None,
        url_mode: Annotated[Optional[StrictStr], Field(description="(Optional) The mode to use when building URLs. One of: ABSOLUTE, RELATIVE or, CONFIGURED. By default this is RELATIVE.")] = None,
        include_heading_id: Annotated[Optional[StrictStr], Field(description="(Optional) true if headers should contain an ID based on the heading content.")] = None,
        hardwrap: Annotated[Optional[StrictStr], Field(description="(Optional) Whether the markup implementation should convert newlines to breaks. By default this is false which reflects the standard markdown specification.")] = None,
        body: Optional[StrictStr] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> ApiResponse[RestMarkup]:
        """Preview markdown render

        Preview generated HTML for the given markdown content.  Only authenticated users may call this resource.

        :param html_escape: (Optional) true if HTML should be escaped in the input markup, false otherwise.
        :type html_escape: str
        :param url_mode: (Optional) The mode to use when building URLs. One of: ABSOLUTE, RELATIVE or, CONFIGURED. By default this is RELATIVE.
        :type url_mode: str
        :param include_heading_id: (Optional) true if headers should contain an ID based on the heading content.
        :type include_heading_id: str
        :param hardwrap: (Optional) Whether the markup implementation should convert newlines to breaks. By default this is false which reflects the standard markdown specification.
        :type hardwrap: str
        :param body:
        :type body: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._preview_serialize(
            html_escape=html_escape,
            url_mode=url_mode,
            include_heading_id=include_heading_id,
            hardwrap=hardwrap,
            body=body,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "RestMarkup",
            '400': "GetAllAccessTokens401Response",
            '401': "GetAllAccessTokens401Response",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        response_data.read()
        return self.api_client.response_deserialize(
            response_data=response_data,
            response_types_map=_response_types_map,
        )


    @validate_call
    def preview_without_preload_content(
        self,
        html_escape: Annotated[Optional[StrictStr], Field(description="(Optional) true if HTML should be escaped in the input markup, false otherwise.")] = None,
        url_mode: Annotated[Optional[StrictStr], Field(description="(Optional) The mode to use when building URLs. One of: ABSOLUTE, RELATIVE or, CONFIGURED. By default this is RELATIVE.")] = None,
        include_heading_id: Annotated[Optional[StrictStr], Field(description="(Optional) true if headers should contain an ID based on the heading content.")] = None,
        hardwrap: Annotated[Optional[StrictStr], Field(description="(Optional) Whether the markup implementation should convert newlines to breaks. By default this is false which reflects the standard markdown specification.")] = None,
        body: Optional[StrictStr] = None,
        _request_timeout: Union[
            None,
            Annotated[StrictFloat, Field(gt=0)],
            Tuple[
                Annotated[StrictFloat, Field(gt=0)],
                Annotated[StrictFloat, Field(gt=0)]
            ]
        ] = None,
        _request_auth: Optional[Dict[StrictStr, Any]] = None,
        _content_type: Optional[StrictStr] = None,
        _headers: Optional[Dict[StrictStr, Any]] = None,
        _host_index: Annotated[StrictInt, Field(ge=0, le=0)] = 0,
    ) -> RESTResponseType:
        """Preview markdown render

        Preview generated HTML for the given markdown content.  Only authenticated users may call this resource.

        :param html_escape: (Optional) true if HTML should be escaped in the input markup, false otherwise.
        :type html_escape: str
        :param url_mode: (Optional) The mode to use when building URLs. One of: ABSOLUTE, RELATIVE or, CONFIGURED. By default this is RELATIVE.
        :type url_mode: str
        :param include_heading_id: (Optional) true if headers should contain an ID based on the heading content.
        :type include_heading_id: str
        :param hardwrap: (Optional) Whether the markup implementation should convert newlines to breaks. By default this is false which reflects the standard markdown specification.
        :type hardwrap: str
        :param body:
        :type body: str
        :param _request_timeout: timeout setting for this request. If one
                                 number provided, it will be total request
                                 timeout. It can also be a pair (tuple) of
                                 (connection, read) timeouts.
        :type _request_timeout: int, tuple(int, int), optional
        :param _request_auth: set to override the auth_settings for an a single
                              request; this effectively ignores the
                              authentication in the spec for a single request.
        :type _request_auth: dict, optional
        :param _content_type: force content-type for the request.
        :type _content_type: str, Optional
        :param _headers: set to override the headers for a single
                         request; this effectively ignores the headers
                         in the spec for a single request.
        :type _headers: dict, optional
        :param _host_index: set to override the host_index for a single
                            request; this effectively ignores the host_index
                            in the spec for a single request.
        :type _host_index: int, optional
        :return: Returns the result object.
        """ # noqa: E501

        _param = self._preview_serialize(
            html_escape=html_escape,
            url_mode=url_mode,
            include_heading_id=include_heading_id,
            hardwrap=hardwrap,
            body=body,
            _request_auth=_request_auth,
            _content_type=_content_type,
            _headers=_headers,
            _host_index=_host_index
        )

        _response_types_map: Dict[str, Optional[str]] = {
            '200': "RestMarkup",
            '400': "GetAllAccessTokens401Response",
            '401': "GetAllAccessTokens401Response",
        }
        response_data = self.api_client.call_api(
            *_param,
            _request_timeout=_request_timeout
        )
        return response_data.response


    def _preview_serialize(
        self,
        html_escape,
        url_mode,
        include_heading_id,
        hardwrap,
        body,
        _request_auth,
        _content_type,
        _headers,
        _host_index,
    ) -> RequestSerialized:

        _host = None

        _collection_formats: Dict[str, str] = {
        }

        _path_params: Dict[str, str] = {}
        _query_params: List[Tuple[str, str]] = []
        _header_params: Dict[str, Optional[str]] = _headers or {}
        _form_params: List[Tuple[str, str]] = []
        _files: Dict[
            str, Union[str, bytes, List[str], List[bytes], List[Tuple[str, bytes]]]
        ] = {}
        _body_params: Optional[bytes] = None

        # process the path parameters
        # process the query parameters
        if html_escape is not None:
            
            _query_params.append(('htmlEscape', html_escape))
            
        if url_mode is not None:
            
            _query_params.append(('urlMode', url_mode))
            
        if include_heading_id is not None:
            
            _query_params.append(('includeHeadingId', include_heading_id))
            
        if hardwrap is not None:
            
            _query_params.append(('hardwrap', hardwrap))
            
        # process the header parameters
        # process the form parameters
        # process the body parameter
        if body is not None:
            _body_params = body


        # set the HTTP header `Accept`
        if 'Accept' not in _header_params:
            _header_params['Accept'] = self.api_client.select_header_accept(
                [
                    'application/json;charset=UTF-8', 
                    'application/json'
                ]
            )


        # authentication setting
        _auth_settings: List[str] = [
        ]

        return self.api_client.param_serialize(
            method='POST',
            resource_path='/api/latest/markup/preview',
            path_params=_path_params,
            query_params=_query_params,
            header_params=_header_params,
            body=_body_params,
            post_params=_form_params,
            files=_files,
            auth_settings=_auth_settings,
            collection_formats=_collection_formats,
            _host=_host,
            _request_auth=_request_auth
        )


