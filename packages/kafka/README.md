# Cymulate Kafka Wrapper

A Python wrapper for Apache Kafka, designed specifically for Cymulate applications. This package provides a simple and consistent interface for interacting with Kafka, including producer and consumer functionality.

## Features

- **Singleton Pattern**: Both producer and consumer are implemented as singletons to ensure efficient resource usage
- **Credential Management**: Integrated with Cymulate's secret management system
- **Thread-Safe**: Designed for use in multi-threaded environments
- **Automatic Topic Creation**: Ability to automatically create topics if they don't exist
- **JSON Serialization**: Automatic serialization and deserialization of JSON messages
- **Error Handling**: Comprehensive error handling and logging
- **Configurable Consumer Settings**: Pre-configured with sensible defaults for consumer behavior

## Installation

```bash
pip install cymulate-kafka
```

## Dependencies

- confluent-kafka
- cymulate-secretmanager

## Usage

### Basic Usage

```python
from kafka.manager import KafkaManager
from secretmanager.models import KafkaConfig

# Create Kafka configuration
config = KafkaConfig(
    broker=["localhost:9092"],
    sasl={
        "username": "your-username",
        "password": "your-password"
    }
)

# Initialize Kafka manager
kafka_manager = KafkaManager(config)

# Send a message
kafka_manager.send_message("example-topic", {"key": "value"})

# Subscribe to a topic
def message_handler(data, msg):
    print(f"Received message: {data}")

kafka_manager.subscribe("example-topic", message_handler)
kafka_manager.start_consuming()

# When done
kafka_manager.close()
```

### Using with Secret Manager

```python
from secretmanager import SecretManager
from kafka.manager import KafkaManager

# Get Kafka configuration from secret manager
secret_manager = SecretManager()
secrets = secret_manager.get_secrets()
kafka_config = secrets.kafka

# Initialize Kafka manager with configuration from secret manager
kafka_manager = KafkaManager(kafka_config)

# Use the manager as before
kafka_manager.send_message("example-topic", {"key": "value"})
```

## Default Configuration

The Kafka consumer comes with the following default configuration:

```python
{
    'bootstrap.servers': ','.join(config.broker),
    'group.id': group_id,
    'auto.offset.reset': 'earliest',
    'enable.auto.commit': True,
    'retry.backoff.ms': 100,
    'max.in.flight.requests.per.connection': 5,
    'allow.auto.create.topics': True,
    'session.timeout.ms': 120000,
    'heartbeat.interval.ms': 30000,
}
```

These settings can be customized when initializing the consumer.

## API Reference

### KafkaManager

The main class for interacting with Kafka.

#### Methods

- `__init__(config: Optional[KafkaConfig] = None)`: Initialize the Kafka manager
- `initialize(config: KafkaConfig) -> None`: Initialize with a Kafka configuration
- `get_producer() -> KafkaProducer`: Get the Kafka producer instance
- `get_consumer() -> KafkaConsumer`: Get the Kafka consumer instance
- `send_message(topic: str, message: Union[str, Dict[str, Any], bytes], key: Optional[Union[str, bytes]] = None, partition: Optional[int] = None, ensure_topic: bool = True) -> bool`: Send a message to a topic
- `subscribe(topics: Union[str, List[str]], handler: Callable[[Dict[str, Any], Message], None], group_id: Optional[str] = None) -> None`: Subscribe to topics
- `start_consuming() -> None`: Start consuming messages
- `stop_consuming() -> None`: Stop consuming messages
- `ensure_topic_exists(topic: str, num_partitions: int = 1, replication_factor: int = 1) -> bool`: Ensure a topic exists
- `close() -> None`: Close all Kafka connections

### KafkaProducer

Singleton class for producing messages to Kafka.

#### Methods

- `connect(config: KafkaConfig) -> None`: Connect to Kafka
- `ensure_topic_exists(topic: str, num_partitions: int = 1, replication_factor: int = 1) -> bool`: Ensure a topic exists
- `send(topic: str, message: Union[str, Dict[str, Any], bytes], key: Optional[Union[str, bytes]] = None, partition: Optional[int] = None, ensure_topic: bool = True) -> bool`: Send a message
- `close() -> None`: Close the producer

### KafkaConsumer

Singleton class for consuming messages from Kafka.

#### Methods

- `connect(config: KafkaConfig, group_id: Optional[str] = None) -> None`: Connect to Kafka
- `subscribe(topics: Union[str, List[str]], handler: Callable[[Dict[str, Any], Message], None]) -> None`: Subscribe to topics
- `start() -> None`: Start consuming messages
- `stop() -> None`: Stop consuming messages
- `close() -> None`: Close the consumer

## License

This project is licensed under the MIT License - see the LICENSE file for details. 