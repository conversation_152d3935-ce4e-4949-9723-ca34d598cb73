"""
Kafka Consumer singleton for Cymulate
"""

import json
import os
import threading
import time
import asyncio
from typing import Any, Callable, Dict, List, Optional, Union, Awaitable


from confluent_kafka import KafkaError, Message
from confluent_kafka import Consumer as ConfluentConsumer

from cymulate.logger import logger

from cymulate.secretmanager.models import KafkaConfig


class KafkaConsumer:
    """
    Singleton Kafka Consumer for Cymulate.
    
    This class provides a thread-safe singleton implementation of a Kafka consumer.
    It handles connection management, message deserialization, and error handling.
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            if cls._lock is None:
                import threading
                cls._lock = threading.Lock()
            
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(KafkaConsumer, cls).__new__(cls)
                    cls._instance._initialized = False
        
        return cls._instance
    
    def __init__(self, config: Optional[KafkaConfig] = None, group_id: Optional[str] = None):
        """
        Initialize the Kafka consumer with the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
        """
        if self._initialized:
            return
            
        self._initialized = True
        self._consumer = None
        self._config = config
        self._connected = False
        self._running = False
        self._consumer_thread = None
        self._message_handlers = {}
        self._group_id = group_id 
        
        if config:
            self.connect(config)
    
    def connect(self, config: KafkaConfig, group_id: Optional[str] = None) -> None:
        """
        Connect to Kafka using the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
            group_id: Optional consumer group ID
        """
        if self._connected and self._consumer:
            logger.info("Already connected to Kafka")
            return
            
        self._config = config
        
        if not config.broker:
            raise ValueError("Kafka broker addresses are required")
            
        if group_id:
            self._group_id = group_id
            
        # Build consumer configuration with default settings
        consumer_config = {
            'bootstrap.servers': ','.join(config.broker),
            'group.id': self._group_id,
            'enable.auto.commit': True,
            # Default configuration parameters
            'retry.backoff.ms': 100,
            'max.in.flight.requests.per.connection': 5,
            'allow.auto.create.topics': True,
            'auto.offset.reset': 'latest',
            'session.timeout.ms': 120000,
            'heartbeat.interval.ms': 30000,
            'group.instance.id': os.environ.get('HOSTNAME', 'localhost'),
        }
        
        # Add SASL configuration if provided
        if config.sasl:
            consumer_config.update({
                'security.protocol': 'SASL_SSL',
                'sasl.mechanism': 'SCRAM-SHA-512',
                'sasl.username': config.sasl.get('username', ''),
                'sasl.password': config.sasl.get('password', ''),
            })
        
        try:
            self._consumer = ConfluentConsumer(consumer_config)
            self._connected = True
            logger.info(f"Connected to Kafka brokers: {config.broker}")
        except Exception as e:
            logger.error(f"Failed to connect to Kafka: {str(e)}")
            raise
    
    def subscribe(self, topics: Union[str, List[str]], 
                 handler: Union[Callable[[Dict[str, Any], Message], None], 
                              Callable[[Dict[str, Any], Message], Awaitable[None]]]) -> None:
        """
        Subscribe to one or more topics and register a message handler.
        
        Args:
            topics: Topic or list of topics to subscribe to
            handler: Callback function to handle messages. Can be either synchronous or asynchronous.
        """
        if not self._consumer or not self._connected:
            logger.error("Consumer not connected to Kafka")
            return
            
        if isinstance(topics, str):
            topics = [topics]
            
        try:            
            # Register handler for each topic
            for topic in topics:
                self._message_handlers[topic] = handler
                
            logger.info(f"Subscribed to topics: {topics}")
        except Exception as e:
            logger.error(f"Failed to subscribe to topics: {str(e)}")
            raise
    
    def start(self) -> None:
        """
        Start consuming messages in a background thread.
        """
        if not self._consumer or not self._connected:
            logger.error("Consumer not connected to Kafka")
            return
            
        if self._running:
            logger.info("Consumer is already running")
            return
        self._consumer.subscribe(list(self._message_handlers.keys()))
        self._running = True
        self._consumer_thread = threading.Thread(target=self._consume_messages)
        self._consumer_thread.daemon = True
        self._consumer_thread.start()
        logger.info("Kafka consumer started")
    
    def stop(self) -> None:
        """
        Stop consuming messages and close the consumer.
        """
        if not self._running:
            logger.info("Consumer is not running")
            return
            
        self._running = False
        
        if self._consumer_thread:
            self._consumer_thread.join(timeout=5.0)
            self._consumer_thread = None
            
        if self._consumer:
            self._consumer.close()
            self._consumer = None
            self._connected = False
            
        logger.info("Kafka consumer stopped")
    
    def _consume_messages(self) -> None:
        """
        Internal method to consume messages in a loop.
        """
        # Create an event loop for async handlers
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        while self._running:
            try:
                msg = self._consumer.poll(1.0)
                
                if msg is None:
                    continue
                    
                if msg.error():
                    if msg.error().code() == KafkaError._PARTITION_EOF:
                        # End of partition event
                        logger.debug(f"Reached end of partition {msg.partition()} for topic {msg.topic()} at offset {msg.offset()}")
                    else:
                        logger.error(f"Error consuming message: {msg.error()}")
                    continue
                    
                # Get the handler for this topic
                handler = self._message_handlers.get(msg.topic())
                
                if handler:
                    try:
                        # Deserialize message
                        value = msg.value()
                        if value:
                            try:
                                # Try to parse as JSON
                                data = json.loads(value.decode('utf-8'))
                            except (json.JSONDecodeError, UnicodeDecodeError):
                                # If not JSON, use raw value
                                data = {"raw": value.decode('utf-8', errors='replace')}
                        else:
                            data = {}
                            
                        # Call the handler - handle both sync and async handlers
                        if asyncio.iscoroutinefunction(handler):
                            # For async handlers, run in the event loop and wait for completion
                            loop.run_until_complete(handler(data, msg))
                        else:
                            # For sync handlers, call directly
                            handler(data, msg)
                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")
                else:
                    logger.warning(f"No handler registered for topic {msg.topic()}")
                    
            except Exception as e:
                logger.error(f"Error in consumer loop: {str(e)}")
                time.sleep(1)  # Avoid tight loop in case of persistent errors
        
        # Clean up the event loop
        loop.close()
    
    def close(self) -> None:
        """
        Close the Kafka consumer connection.
        """
        self.stop()
        logger.info("Kafka consumer connection closed") 