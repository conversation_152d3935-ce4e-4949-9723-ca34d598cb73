"""
Kafka Producer singleton for Cymulate
"""

import json
import logging
from typing import Any, Dict, Optional, Union, List, Tuple

from confluent_kafka import Producer as ConfluentProducer
from confluent_kafka.admin import AdminClient, NewTopic
from confluent_kafka.cimpl import KafkaError

from cymulate.secretmanager.models import KafkaConfig
from cymulate.logger import logger


class KafkaProducer:
    """
    Singleton Kafka Producer for Cymulate.
    
    This class provides a thread-safe singleton implementation of a Kafka producer.
    It handles connection management, message serialization, and error handling.
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            if cls._lock is None:
                import threading
                cls._lock = threading.Lock()
            
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(KafkaProducer, cls).__new__(cls)
                    cls._instance._initialized = False
        
        return cls._instance
    
    def __init__(self, config: Optional[KafkaConfig] = None):
        """
        Initialize the Kafka producer with the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
        """
        if self._initialized:
            return
            
        self._initialized = True
        self._producer = None
        self._admin_client = None
        self._config = config
        self._connected = False
        
        if config:
            self.connect(config)
    
    def connect(self, config: KafkaConfig) -> None:
        """
        Connect to Kafka using the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
        """
        if self._connected and self._producer:
            logger.info("Already connected to Kafka")
            return
            
        self._config = config
        
        if not config.broker:
            raise ValueError("Kafka broker addresses are required")
            
        # Build producer configuration
        producer_config = {
            'bootstrap.servers': ','.join(config.broker),
            'client.id': 'cymulate-producer',
        }
        
        # Add SASL configuration if provided
        if config.sasl:
            producer_config.update({
                'security.protocol': 'SASL_SSL',
                'sasl.mechanism': 'SCRAM-SHA-512',
                'sasl.username': config.sasl.get('username', ''),
                'sasl.password': config.sasl.get('password', ''),
            })
        
        try:
            self._producer = ConfluentProducer(producer_config)
            self._admin_client = AdminClient(producer_config)
            self._connected = True
            logger.info(f"Connected to Kafka brokers: {config.broker}")
        except Exception as e:
            logger.error(f"Failed to connect to Kafka: {str(e)}")
            raise
    
    def ensure_topic_exists(self, topic: str, num_partitions: int = 1, replication_factor: int = 1) -> bool:
        """
        Ensure that a topic exists, creating it if necessary.
        
        Args:
            topic: Name of the topic to ensure exists
            num_partitions: Number of partitions for the topic
            replication_factor: Replication factor for the topic
            
        Returns:
            bool: True if the topic exists or was created successfully
        """
        if not self._admin_client:
            return False
            
        try:
            # Check if topic exists
            topics = self._admin_client.list_topics()
            if topic in topics.topics:
                logger.info(f"Topic {topic} already exists")
                return True
                
            # Create topic
            new_topic = NewTopic(
                topic,
                num_partitions=num_partitions,
                replication_factor=replication_factor
            )
            
            futures = self._admin_client.create_topics([new_topic])
            
            # Wait for the topic to be created
            for topic_name, future in futures.items():
                try:
                    future.result()
                    logger.info(f"Topic {topic_name} created successfully")
                    return True
                except Exception as e:
                    logger.error(f"Failed to create topic {topic_name}: {str(e)}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error ensuring topic exists: {str(e)}")
            return False
    
    def send(self, topic: str, message: Union[str, Dict[str, Any], bytes], 
             key: Optional[Union[str, bytes]] = None, 
             partition: Optional[int] = None,
             headers: Optional[List[Tuple[str, bytes]]] = None,
             ensure_topic: bool = True) -> bool:
        """
        Send a message to a Kafka topic.
        
        Args:
            topic: Name of the topic to send the message to
            message: Message to send (string, dict, or bytes)
            key: Optional message key
            partition: Optional partition to send to
            ensure_topic: Whether to ensure the topic exists before sending
            
        Returns:
            bool: True if the message was sent successfully
        """
        if not self._producer or not self._connected:
            logger.error("Producer not connected to Kafka")
            return False
            
        if ensure_topic:
            self.ensure_topic_exists(topic)
            
        # Serialize message if it's a dict
        if isinstance(message, dict):
            message = json.dumps(message)
        elif not isinstance(message, (str, bytes)):
            message = str(message)
            
        # Convert string to bytes if necessary
        if isinstance(message, str):
            message = message.encode('utf-8')
            
        # Convert key to bytes if necessary
        if key and isinstance(key, str):
            key = key.encode('utf-8')
            
        try:
            # Prepare delivery report callback
            def delivery_report(err, msg):
                if err is not None:
                    logger.error(f"Message delivery failed: {err}")
                else:
                    logger.debug(f"Message delivered to {msg.topic()} [{msg.partition()}]")
            
            # Send message
            self._producer.produce(
                topic=topic,
                value=message,
                headers=headers,
                callback=delivery_report
            )
            
            # Flush to ensure message is sent
            self._producer.flush()
            return True
            
        except Exception as e:
            logger.error(f"Failed to send message to topic {topic}: {str(e)}")
            return False
    
    def close(self) -> None:
        """
        Close the Kafka producer connection.
        """
        if self._producer:
            self._producer.flush()
            self._producer.close()
            self._producer = None
            self._admin_client = None
            self._connected = False
            logger.info("Kafka producer connection closed") 