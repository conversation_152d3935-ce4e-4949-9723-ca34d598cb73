"""
File Secret Manager service implementation.
"""

import json
import os
import pathlib
from typing import Op<PERSON>

from ..interfaces import SecretManagerService
from ..models import SecretConfig


class FileSecretManagerService(SecretManagerService):
    """
    File Secret Manager service implementation.

    This class provides access to secrets stored in local JSON files.
    """

    def __init__(self):
        """
        Initialize the File Secret Manager service.
        """
        super().__init__()

        # Default environment directory
        self.env_dir = os.environ.get(
            "ENV_CONFIG_DIR",
            str(pathlib.Path(__file__).parent.parent / "envFile"),
        )

    def get_env_file_path(self, env: str) -> str:
        """
        Get the path to the environment file.

        Args:
            env: The environment name

        Returns:
            The path to the environment file
        """
        # Check if a custom path is provided
        if custom_path := os.environ.get("ENV_CONFIG_PATH"):
            # If the custom path is a file, use it directly
            if custom_path.endswith(".json"):
                return os.path.abspath(custom_path)
            # Otherwise, append the environment name
            return os.path.abspath(os.path.join(custom_path, f"{env}.json"))

        # Use the default path
        return os.path.abspath(os.path.join(self.env_dir, f"{env}.json"))

    def get_secret(self) -> SecretConfig:
        """
        Get the secret configuration from a local file.

        Returns:
            The secret configuration

        Raises:
            FileNotFoundError: If the environment file does not exist
            json.JSONDecodeError: If the environment file is not valid JSON
        """
        # Return cached config if available
        if self._config is not None:
            return self._config

        # Get the environment
        env = os.environ.get("ENV", "local")

        # Get the path to the environment file
        file_path = self.get_env_file_path(env)

        try:
            # Read the environment file
            with open(file_path, "r") as f:
                secret_data = json.load(f)

            # Parse the secret data
            self._config = SecretConfig.parse_json(secret_data)
            return self._config
        except FileNotFoundError:
            error_msg = f"Environment file not found: {file_path}"
            self.logger.error(error_msg)
            raise
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON in environment file: {file_path} - {str(e)}"
            self.logger.error(error_msg)
            raise
        except Exception as e:
            error_msg = f"Unexpected error reading environment file: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise
