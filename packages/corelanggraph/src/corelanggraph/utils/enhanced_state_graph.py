import inspect
import async<PERSON>
from typing import Any, Callable, Dict, List, Optional, Type, Union

from langgraph.graph import StateGraph
from .run_with_state import get_global_event_loop
from langgraph.graph.state import CompiledStateGraph

class EnhancedStateGraph(StateGraph):
    """
    Enhanced StateGraph that ensures proper propagation of checkpoint configuration
    and simplifies working with stateful nodes.
    """
    
    def add_subgraph(
        self, 
        name: str, 
        subgraph: StateGraph,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add a subgraph, ensuring that checkpointing configuration is properly passed.
        
        Args:
            name: Name of the subgraph node
            subgraph: The StateGraph instance to add as a subgraph
            metadata: Optional metadata for the node
        """
        if metadata is None:
            metadata = {}
        
        # Add the wrapper as a node
        super().add_node(name, subgraph, metadata=metadata)
    
    def add_node(
        self, 
        name: str, 
        node: Union[Type, Callable],
        metadata: Optional[Dict[str, Any]] = None,
        auto_wrap: bool = True  # Whether to auto-wrap with run_with_state
    ) -> None:
        """
        Add a node to the graph, with optional automatic state handling.
        
        Args:
            name: Name of the node
            node: The node class or function
            metadata: Optional metadata for the node
            auto_wrap: Whether to automatically wrap the node with run_with_state (default: True)
        """
        if metadata is None:
            metadata = {}
            
        # Handle different node types
        if auto_wrap and inspect.isclass(node):
            # For class nodes, implement run_with_state functionality directly
            
            async def class_wrapper(state: Dict[str, Any]) -> Dict[str, Any]:
                # Check if the class constructor accepts a state argument
                init_signature = inspect.signature(node.__init__)
                init_params = list(init_signature.parameters.keys())
                
                # Create instance based on __init__ signature
                instance = node(state) if len(init_params) > 1 else node()
                
                # Ensure execute method exists
                if not hasattr(instance, "execute"):
                    raise NotImplementedError(f"Class {node.__name__} does not have a 'execute' method")
                
                # Get execute method signature
                execute_method = instance.execute
                execute_signature = inspect.signature(execute_method)
                execute_params = list(execute_signature.parameters.keys())
                
                if len(execute_params) >= 1:
                    if inspect.iscoroutinefunction(execute_method):
                        result = await execute_method(state)
                    else:
                        result = execute_method(state)
                else:
                    if inspect.iscoroutinefunction(execute_method):
                        result = await execute_method()
                    else:
                        result = execute_method()
                
                # Handle nested coroutines
                if inspect.iscoroutine(result):
                    result = await result
                
                return result if result is not None else state
            
            # Add the wrapped class
            super().add_node(name, class_wrapper, metadata=metadata)
            
        elif auto_wrap and inspect.isfunction(node) and not inspect.iscoroutinefunction(node):
            # For regular functions, wrap them in a coroutine
            async def func_wrapper(state: Dict[str, Any]) -> Dict[str, Any]:
                result = node(state)
                return result if result is not None else state
                
            super().add_node(name, func_wrapper, metadata=metadata)
            
        else:
            # For coroutines, asyncio.Future, or any other node type, add directly
            super().add_node(name, node, metadata=metadata)
    
    def compile(self, checkpointer=None, **kwargs):
        """
        Compile the graph with the given checkpointer.
        
        This method saves the checkpointer for potential use by subgraphs.
        If the graph is already compiled, returns it as is.
        """
        # Check if this graph is already compiled
        if isinstance(self, CompiledStateGraph):
            return self
            
        # Save the checkpointer for future reference
        self._checkpointer = checkpointer
        
        # Call the parent class's compile method
        return super().compile(checkpointer=checkpointer, **kwargs) 