[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "storage"
version = "0.1.6"
description = "A unified storage library that provides an abstraction over multiple cloud storage providers"
readme = "README.md"
authors = [
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
license = {text = "Proprietary"}
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    "azure-storage-blob>=12.16.0",
    "boto3>=1.28.0",
    "build>=1.2.2.post1",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "twine>=6.1.0",
    "typing-extensions>=4.7.0",
]

[project.optional-dependencies]
# Add optional dependencies here if needed
# example = [
#     "package>=1.0.0",
# ]

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages]
find = {where = ["src"], include = ["cymulate*"], namespaces = true}

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true 
