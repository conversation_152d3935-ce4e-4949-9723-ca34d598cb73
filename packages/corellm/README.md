# cymulate.corellm

Core LLM utilities and abstractions for Cymulate.

## Requirements

- Python 3.12 or higher
- uv package manager

## Installation

```bash
# Basic installation
uv pip install cymulate.corellm

# With OpenAI support
uv pip install "cymulate.corellm[openai]"

# With Anthropic support
uv pip install "cymulate.corellm[anthropic]"

# With Azure OpenAI support
uv pip install "cymulate.corellm[azure]"

# With all providers
uv pip install "cymulate.corellm[all]"
```

## Usage

### Basic Client Usage

```python
from corellm import LLMClient

# Initialize the client
client = LLMClient(api_key="your-api-key")

# Generate text
response = client.generate("Tell me about cybersecurity")
print(response)
```

### Using Model Providers

```python
from corellm import ModelProvider, OpenAIModel

# Configure model providers
configs = [
    {
        "type": "openai",
        "name": "gpt4",
        "model_name": "gpt-4o",
        "api_key": "your-openai-api-key",
        "default": True
    },
    {
        "type": "anthropic",
        "name": "claude",
        "model_name": "claude-3-opus-20240229",
        "api_key": "your-anthropic-api-key",
        "openai_api_key": "your-openai-api-key"  # For embeddings
    }
]

# Initialize the model provider
provider = ModelProvider(configs)

# Get the default model
llm = provider.get_llm()

# Use a specific model
claude_llm = provider.get_llm("claude")

# Get embeddings
embeddings = provider.get_embeddings()
```

## Features

- Unified interface for multiple LLM providers:
  - OpenAI
  - Anthropic
  - Azure OpenAI
- Streaming support
- Prompt templating
- Response parsing utilities
- Caching and rate limiting
- Type-safe interfaces

## Development

### Setup

```bash
# Clone the repository
git clone https://bitbucket.org/cymulate/pythonlibs.git
cd pythonlibs

# Install development dependencies
uv pip install invoke
invoke install-dev
```

### Testing

```bash
invoke test
```

## License

Proprietary - Cymulate 