from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from langchain_core.embeddings import Embeddings
from langchain.schema import Document


class VectorStoreBase(ABC):
    """Base class for vector store implementations."""

    def __init__(self, client: Any, db_name: str, embedding_model: Embeddings):
        self.client = client
        self.db_name = db_name
        self.embedding_model = embedding_model
    

    @abstractmethod
    def create(self, files_content: List[Dict[str, str]], collection_name: str, namespace: Optional[str] = None):
        """Create a vector store from file contents."""
        pass
    
    @abstractmethod
    def query(self, query: str, collection_name: str, namespace: Optional[str] = None, top_k: int = 5) -> List[Dict]:
        """Query the vector store for relevant contexts."""
        pass

    def is_ready(self):
        """Check if the vector store is ready to use."""
        return self.client is not None and self.db_name is not None and self.embedding_model is not None

    def create_documents_objects(self, files_content: List[Dict[str, str]], namespace: Optional[str] = None):
        """Create documents objects from file contents."""
        documents = []
        for file in files_content:
            doc = Document(
                page_content=f"{file['name']}\n{file['content']}",
                metadata={
                    "source": file['name'],
                    "namespace": namespace or "default"
                }
            )
            documents.append(doc)
        return documents
