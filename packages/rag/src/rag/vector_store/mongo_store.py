from typing import Any, Dict, List, Optional

from langchain_mongodb import MongoDBAtlasVectorSearch
from .base_store import VectorStoreBase
from langchain_core.embeddings import Embeddings
from langchain.schema import Document

from pymongo import MongoClient

class MongoDBVectorStore(VectorStoreBase):
    """MongoDB Atlas Vector Search implementation."""

    def __init__(self, client: MongoClient, db_name: str, embedding_model: Embeddings):
        super().__init__(client, db_name, embedding_model)
    
    def create(self, files_content: List[Dict[str, str]], collection_name: str, namespace: Optional[str] = None):
        """
        Create a MongoDB vector store from the file contents.

        Args:
            files_content: List of dictionaries containing file names and contents
            collection_name: Name of the MongoDB collection
            namespace: Namespace for the collection (used as a field in the documents)

        Returns:
            MongoDBAtlasVectorSearch instance
        """
        if not self.is_ready():
            raise ValueError("MongoDB client or database name or embedding model not initialized. Please set MONGODB_CONNECTION_STRING and MONGODB_DB_NAME.")

        # Create document objects
        documents = self.create_documents_objects(files_content, namespace)

        
        # Get database
        db = self.client[self.db_name]
        
        # Drop existing collection if exists
        if collection_name in db.list_collection_names():
            db[collection_name].drop()
        
        # Create vector store
        vector_store = MongoDBAtlasVectorSearch.from_documents(
            documents=documents,
            embedding=self.embedding_model,
            collection=db[collection_name],
            index_name=f"{collection_name}_vector_index"
        )
        
        return vector_store
    
    def query(self, query: str, collection_name: str, namespace: Optional[str] = None, top_k: int = 5) -> List[Dict]:
        """
        Query the MongoDB vector store for relevant contexts.

        Args:
            query: The query string
            collection_name: Name of the MongoDB collection
            namespace: Namespace to filter by
            top_k: Number of relevant contexts to retrieve

        Returns:
            List of relevant contexts
        """
        if not self.is_ready():
            raise ValueError("MongoDB client or database name or embedding model not initialized. Please set MONGODB_CONNECTION_STRING and MONGODB_DB_NAME.")
        
        # Get database and collection
        db = self.client[self.db_name]
        
        # Check if collection exists
        collection_list = db.list_collection_names()
        if collection_name not in collection_list:
            raise ValueError(f"Collection '{collection_name}' does not exist.")
        
        # Connect to the vector store
        # Create filter for namespace if specified
        metadata_filter = {}
        if namespace:
            metadata_filter = {"namespace": namespace}
        
        # Create vector store connection
        vector_store = MongoDBAtlasVectorSearch(
            collection=db[collection_name],
            embedding=self.embedding_model,
            index_name=f"{collection_name}_vector_index"
        )
        
        # Query the index
        results = vector_store.similarity_search_with_score(
            query, 
            k=top_k,
            pre_filter=metadata_filter
        )
        
        contexts = []
        for doc, score in results:
            contexts.append({
                "source": doc.metadata.get("source", "Unknown"),
                "content": doc.page_content,
                "score": float(score)
            })
        
        return contexts
