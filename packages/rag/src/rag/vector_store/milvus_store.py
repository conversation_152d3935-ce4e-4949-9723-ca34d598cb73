from typing import Any, Dict, List, Optional

from rag.database.milvus import MilvusClientArgs
from .base_store import VectorStoreBase
from langchain_core.embeddings import Embeddings

from langchain.schema import Document
from langchain_milvus import Milvus

class MilvusVectorStore(VectorStoreBase):
    """Milvus Vector Store implementation."""
    
    def __init__(self, client: Any, db_name: str, embedding_model: Embeddings):
        """
        Initialize Milvus Vector Store.
        
        Args:
            client: Milvus client
            db_name: Name of the Milvus database
        """
        super().__init__(client, db_name, embedding_model)
    
    def create(self, files_content: List[Dict[str, str]], collection_name: str, namespace: Optional[str] = None):
        """
        Create a Milvus vector store from the file contents.

        Args:
            files_content: List of dictionaries containing file names and contents
            collection_name: Name of the Milvus collection
            namespace: Namespace for the collection (used as a field in the documents)

        Returns:
            Milvus instance
        """
        # Create document objects
        documents = self.create_documents_objects(files_content, namespace)

        client:MilvusClientArgs = self.client

        # Create vector store
        vector_store = Milvus.from_documents(
            documents=documents,
            embedding=self.embedding_model,
            collection_name=collection_name,
            connection_args=client.model_dump(),
            drop_old=True,
            index_params={
                "metric_type": "COSINE",
                "index_type": "HNSW",
                "params": {"M": 8, "efConstruction": 64}
            }
        )
        
        return vector_store

    def create_hybrid(self, 
                      files_content: List[Dict[str, str]], 
                      collection_name: str, 
                      vector_field: list[str]=["dense", "sparse"],
                      namespace: Optional[str] = None):
        """
        Create a Milvus vector store with hybrid search capabilities.

        Args:
            files_content: List of dictionaries containing file names and contents
            collection_name: Name of the Milvus collection
            namespace: Namespace for the collection (used as a field in the documents)

        Returns:
            Milvus instance with hybrid search capabilities
        """
        try:
            from langchain_milvus import BM25BuiltInFunction
        except ImportError:
            raise ImportError("BM25BuiltInFunction requires langchain-milvus>=0.0.1")
            
        documents = self.create_documents_objects(files_content, namespace)
        # Create embeddings and add to vector store
        
        # Create BM25 function for full-text search
        bm25_function = BM25BuiltInFunction()

        client:MilvusClientArgs = self.client
        
        # Create vector store with hybrid search
        vector_store = Milvus.from_documents(
            documents=documents,
            embedding=self.embedding_model,
            collection_name=collection_name,
            connection_args=client.model_dump(),
            builtin_function=bm25_function,
            vector_field=vector_field,
            drop_old=True,
            index_params={
                "metric_type": "COSINE",
                "index_type": "HNSW",
                "params": {"M": 8, "efConstruction": 64}
            }
        )
        
        return vector_store
    
    def query(self, query: str, collection_name: str, namespace: Optional[str] = None, top_k: int = 5) -> List[Dict]:
        """
        Query the Milvus vector store for relevant contexts.

        Args:
            query: The query string
            collection_name: Name of the Milvus collection
            namespace: Namespace to filter by
            top_k: Number of relevant contexts to retrieve

        Returns:
            List of relevant contexts
        """
        # Create filter for namespace if specified
        search_kwargs = {}
        if namespace:
            search_kwargs["expr"] = f"namespace == \"{namespace}\""
        
        client:MilvusClientArgs = self.client
        # Create vector store connection
        vector_store = Milvus(
            embedding_function=self.embedding_model,
            collection_name=collection_name,
            connection_args=client.model_dump(),
            search_params={"params": {"ef": 100}}
        )
        
        # Query the index
        results = vector_store.similarity_search_with_score(
            query, 
            k=top_k,
            **search_kwargs
        )
        
        return results
        
    def query_hybrid(self, query: str, collection_name: str, namespace: Optional[str] = None, 
                    top_k: int = 5, weights: List[float] = None) -> List[Dict]:
        """
        Query the Milvus vector store using hybrid search (dense + sparse).

        Args:
            query: The query string
            collection_name: Name of the Milvus collection
            namespace: Namespace to filter by
            top_k: Number of relevant contexts to retrieve
            weights: Weights for dense and sparse vectors, e.g. [0.6, 0.4]

        Returns:
            List of relevant contexts
        """
        # Create filter for namespace if specified
        search_kwargs = {}
        if namespace:
            search_kwargs["expr"] = f"namespace == \"{namespace}\""
            
        # Add weights for hybrid search if specified
        if weights:
            search_kwargs["ranker_type"] = "weighted"
            search_kwargs["ranker_params"] = {"weights": weights}

        client:MilvusClientArgs = self.client
        
        # Create vector store connection
        vector_store = Milvus(
            embedding_function=self.embedding_model,
            collection_name=collection_name,
            connection_args=client.model_dump(),
            search_params={"params": {"ef": max(40, top_k * 2)}}
        )
        
        # Query the index
        results = vector_store.similarity_search_with_score(
            query, 
            k=top_k,
            **search_kwargs
        )
        
        return results
