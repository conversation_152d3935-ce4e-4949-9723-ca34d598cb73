# MAS API Endpoints and Data Models

This document provides a visual representation of the API endpoints and data models in the MAS (Modular Agent System) framework.

## API Endpoints Overview

```mermaid
graph LR
    Client[Client] --> API[API Server]
    
    API --> AgentEndpoint[POST /agent]
    API --> ProcessEndpoint[POST /process]
    API --> ListWorkflows[GET /workflows]
    API --> GetWorkflow[GET /workflows/{id}]
    API --> DeleteWorkflow[DELETE /workflows/{id}]
    API --> CreateUser[POST /users]
    API --> ListAgents[GET /agents]
    API --> HealthCheck[GET /health]
    
    AgentEndpoint --> WB[WorkflowBuilder]
    ProcessEndpoint --> PA[ProcessingAgent]
    ListWorkflows --> SM[StateManager]
    GetWorkflow --> SM
    DeleteWorkflow --> SM
    CreateUser --> Auth[AuthManager]
    ListAgents --> WB
    
    %% Style
    classDef client fill:#ddd,stroke:#333,stroke-width:2px;
    classDef api fill:#f9f,stroke:#333,stroke-width:2px;
    classDef endpoint fill:#9af,stroke:#333,stroke-width:2px;
    classDef component fill:#bfb,stroke:#333,stroke-width:2px;
    
    class Client client;
    class API api;
    class AgentEndpoint,ProcessEndpoint,ListWorkflows,GetWorkflow,DeleteWorkflow,CreateUser,ListAgents,HealthCheck endpoint;
    class WB,PA,SM,Auth component;
```

## API Data Models

```mermaid
classDiagram
    class AgentRequest {
        +string message
        +Dict context
        +string thread_id
        +string workflow_id
    }
    
    class AgentResponse {
        +string thread_id
        +string workflow_id
        +Dict result
        +string status
        +string error
    }
    
    class ProcessingRequest {
        +Any data
        +string operation
        +Dict parameters
        +string workflow_id
    }
    
    class ProcessingResponse {
        +string workflow_id
        +Any result
        +string status
        +string error
    }
    
    class UserRequest {
        +string username
        +string password
        +List~string~ roles
    }
    
    class UserResponse {
        +string username
        +List~string~ roles
        +string api_key
    }
    
    class ErrorStrategy {
        +string strategy_type
        +string agent_name
        +int max_retries
        +int delay_seconds
        +string message
        +Dict fallback_action
    }
    
    class ProcessingOperation {
        +string operation_type
        +Any input_data
        +Dict parameters
        +string output_format
    }
    
    AgentRequest --> AgentResponse
    ProcessingRequest --> ProcessingResponse
    UserRequest --> UserResponse
    ProcessingRequest --> ProcessingOperation
    AgentResponse --> ErrorStrategy
```

## Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Auth as AuthManager
    participant Endpoint as API Endpoint
    
    Client->>API: Request with API Key
    API->>Auth: Validate API Key
    
    alt Valid API Key
        Auth-->>API: Authentication Success
        API->>Endpoint: Forward Request
        Endpoint-->>API: Process Request
        API-->>Client: Response
    else Invalid API Key
        Auth-->>API: Authentication Failure
        API-->>Client: 401 Unauthorized
    end
```

## API Request-Response Flow

```mermaid
flowchart TD
    Start([Start]) --> ClientRequest[Client Sends Request]
    ClientRequest --> AuthCheck{Authenticate}
    
    AuthCheck -->|Fail| Return401[Return 401 Unauthorized]
    Return401 --> End([End])
    
    AuthCheck -->|Success| RequestType{Request Type}
    
    RequestType -->|POST /agent| AgentRequest[Process Agent Request]
    RequestType -->|POST /process| ProcessRequest[Process Data Request]
    RequestType -->|GET /workflows| ListWorkflows[List Workflows]
    RequestType -->|GET /workflows/{id}| GetWorkflow[Get Workflow]
    RequestType -->|DELETE /workflows/{id}| DeleteWorkflow[Delete Workflow]
    RequestType -->|POST /users| CreateUser[Create User]
    RequestType -->|GET /agents| ListAgents[List Agents]
    RequestType -->|GET /health| HealthCheck[Health Check]
    
    AgentRequest --> BuildWorkflow[Build Workflow]
    BuildWorkflow --> RunWorkflow[Run Workflow]
    RunWorkflow --> FormatResponse[Format Response]
    
    ProcessRequest --> ProcessData[Process Data]
    ProcessData --> FormatResponse
    
    ListWorkflows --> QueryState[Query State Manager]
    QueryState --> FormatResponse
    
    GetWorkflow --> QueryState
    
    DeleteWorkflow --> RemoveState[Remove State]
    RemoveState --> FormatResponse
    
    CreateUser --> AddUser[Add User to Auth Manager]
    AddUser --> FormatResponse
    
    ListAgents --> GetAgents[Get Available Agents]
    GetAgents --> FormatResponse
    
    HealthCheck --> CheckHealth[Check System Health]
    CheckHealth --> FormatResponse
    
    FormatResponse --> ReturnResponse[Return Response to Client]
    ReturnResponse --> End
    
    %% Style
    classDef start fill:#7f7,stroke:#333,stroke-width:2px;
    classDef end fill:#f77,stroke:#333,stroke-width:2px;
    classDef process fill:#7cf,stroke:#333,stroke-width:2px;
    classDef decision fill:#fcf,stroke:#333,stroke-width:2px;
    classDef error fill:#f9c,stroke:#333,stroke-width:2px;
    
    class Start,End start;
    class ClientRequest,AgentRequest,ProcessRequest,ListWorkflows,GetWorkflow,DeleteWorkflow,CreateUser,ListAgents,HealthCheck,BuildWorkflow,RunWorkflow,ProcessData,QueryState,RemoveState,AddUser,GetAgents,CheckHealth,FormatResponse,ReturnResponse process;
    class AuthCheck,RequestType decision;
    class Return401 error;
``` 