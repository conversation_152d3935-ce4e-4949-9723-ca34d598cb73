{"name": "@cymulate-innovation/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "devDependencies": {"@nx-tools/nx-container": "^6.7.1", "@nx/jest": "21.1.2", "@nx/js": "21.1.2", "@nx/plugin": "^21.1.2", "@nxlv/python": "^21.0.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/lodash": "^4.17.17", "@types/node": "18.16.9", "nx": "21.1.2", "tslib": "^2.3.0", "typescript": "~5.7.2"}, "workspaces": ["packages/*", "tools/*"], "dependencies": {"@nx/devkit": "21.1.2", "lodash": "^4.17.21"}}