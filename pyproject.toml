[project]
name = "cymulate_platform"
version = "0.0.1"
description = "Modular Multi-Agent System with LangGraph"

readme = "README.md"
license = "MIT"
requires-python = ">=3.12"
dependencies = [
    "kafka",
    "langchain-community>=0.3.19",
    "neo4j>=5.28.1",
    "prometheus-client>=0.22.0",
    "py2neo>=2021.2.4",
    "sentence-transformers>=4.1.0",
    "tree-sitter-python==0.23.6",
]


[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"



[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.setuptools.packages.find]
where = ["."]
include = ["apps*", "libs*"]
exclude = ["static*"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    # We actually do want to import from typing_extensions
    "UP035",
    # Relax the convention by _not_ requiring documentation for every function parameter.
    "D417",
    "E501",
]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.uv.workspace]
members = [
    "packages/bitbucketclient",
    "packages/corellm",
    "packages/langraph-redis-checkpointer",
    "packages/mongo",
    "packages/secretmanager",
    "packages/corelanggraph",
    "packages/kafka",
    "packages/logger",
    "packages/rag",
    "packages/storage",
    "apps/product/siem_rule",
    "apps/innovations/code_review",
    "arik-test/arik-test1",
    "apps/innovations/doc_agent",
]

[tool.uv.sources]
kafka = { workspace = true }


[dependency-groups]
dev = [
    "anyio>=4.7.0",
    "langgraph-cli[inmem]>=0.2.8",
    "mypy>=1.13.0",
    "pytest>=8.3.5",
    "ruff>=0.8.2",
]
