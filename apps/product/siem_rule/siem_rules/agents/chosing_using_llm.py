from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers import ModelProvider
import os
from langgraph.types import Command
from logger import logger
from siem_rules.metrics import LLM_RETRIEVED_SCENARIOS
from siem_rules.models.siem_rule_extractor import MatchingScenarios
from siem_rules.workflow.siem_rules.state import SiemRulesState

class ChosingUsingLLMAgent(BaseAgentLLM):
    """
    This agent is used to choose the best rule from the list of rules.
    """
    def __init__(self):
        super().__init__(
            description="Generate improved queries for RAG based on pull request diffs",
            name="SiemRules_ChosingUsingLLMAgent",
            llm=ModelProvider().get_llm(model_name=os.getenv('MODEL_NAME','azure_model_2'), temperature=0.1)
        )

    
    async def execute(self, state: SiemRulesState) -> SiemRulesState:
        # Check if we have RAG results
        if not hasattr(state, 'relevant_scenarios') or not state.relevant_scenarios:
            logger.warning("No RAG results found in state. Proceeding without reranked results.")
            raise ValueError("No RAG results found in state. Proceeding without reranked results.")
         
    
        logger.info(f"Using {len(state.relevant_scenarios)} reranked results for rule selection")
        
        # Format the reranked results for the prompt
        reranked_results_text = "\n".join([f"id: {result.id}: text: '{result.text}" for result in state.relevant_scenarios])        
        # Create a prompt that includes the reranked results
        prompt = self.system_prompt.format_messages(
            rule=f"Name: {state.name}\nQuery: {state.query}\n",
            scenario_text=reranked_results_text
        )
            
        # Create a tool extractor for the RAGFile objects
        extractor = self.create_extractor(
            tools=[MatchingScenarios],
            tool_choice='MatchingScenarios'
        )

        result = await extractor.ainvoke(prompt)
        matching_scenarios:MatchingScenarios = result["responses"][0]
        LLM_RETRIEVED_SCENARIOS.observe(len(matching_scenarios.scenarios))
        # Store the selected rules
        state.relevant_scenarios = matching_scenarios.scenarios
        state.desicion = matching_scenarios.desicion
        
        return Command(
                goto="save_result_to_mongo",
                update=state
            )