"""
API Server

This module provides a FastAPI application for interacting with
the multi-agent system via RESTful API.
"""
from prometheus_client import generate_latest
from prometheus_client import Counter, generate_latest, CONTENT_TYPE_LATEST
from ..infra import init
from logger import logger
import os
from fastapi import Fast<PERSON><PERSON>, Response
from fastapi.middleware.cors import CORSMiddleware

from siem_rules.api.indexer import router as indexer_router
from siem_rules.api.batch_handler import router as batch_router

# Create FastAPI app
app = FastAPI(
    title="SIEM Rules API",
    description="API for interacting with the SIEM Rules service",
    version="0.1.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update with actual origins in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(indexer_router)
app.include_router(batch_router)

@app.get("/metrics")
async def metrics():
    data = generate_latest()
    return Response(content=data, media_type=CONTENT_TYPE_LATEST)


@app.get("/liveness")
async def liveness():
    return {"status": "healthy"}

@app.get("/health")
async def health_check():
    """Check the health of the API."""
    logger.debug("Health check endpoint called")
    
    
    # Check Milvus connectivity if URI is set
    milvus_ok = False
    MILVUS_URI = os.getenv("MILVUS_URI", None)
    if MILVUS_URI:
        try:
            # Import pymilvus here to avoid dependency issues if not installed
            from pymilvus import connections
            # Check if we can establish a connection
            connections.connect(alias="health_check", uri=MILVUS_URI)
            milvus_ok = connections.has_connection("health_check")
            # Close the connection
            if milvus_ok:
                connections.disconnect("health_check")
        except Exception as e:
            logger.error(f"Milvus health check failed: {str(e)}")
            milvus_ok = False
    
    return {
        "status": "healthy", 
        "milvus": milvus_ok,
    } 
