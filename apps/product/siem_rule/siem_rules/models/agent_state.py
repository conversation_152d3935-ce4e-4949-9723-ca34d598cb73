from typing import Dict, List, Any, Optional, Callable
from pydantic import BaseModel, Field


class AgentState(BaseModel):
    """State model for the agent workflow"""
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    next_steps: List[str] = Field(default_factory=list)
    tasks: Optional[list[Dict[str, Any]]] = Field(default_factory=list)