#!/usr/bin/env python3
"""
Enhanced Query Examples for Two-Phase Graph

Demonstrates queries that work with both fast-generated data
and LLM-enhanced data.
"""

import os
from neo4j import GraphDatabase
from dotenv import load_dotenv

load_dotenv()

NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

def run_query(query, description):
    """Run query and display results"""
    print(f"\n🔍 {description}")
    print("=" * 60)
    print(f"Query: {query}")
    print("-" * 60)
    
    try:
        with driver.session() as session:
            result = session.run(query)
            records = list(result)
            
            if not records:
                print("❌ No results found")
                return
            
            for i, record in enumerate(records[:10], 1):
                print(f"{i}. {dict(record)}")
            
            if len(records) > 10:
                print(f"... and {len(records) - 10} more results")
                
            print(f"\n📊 Total results: {len(records)}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run enhanced query examples"""
    print("🔮 Enhanced Code Graph Queries")
    print("=" * 50)
    
    # 1. Enhancement status overview
    run_query("""
        MATCH (f:LogicalFunction)
        RETURN 
            count(f) as total_functions,
            count(CASE WHEN f.llm_analyzed = true THEN 1 END) as enhanced_functions,
            count(CASE WHEN f.llm_analyzed = false THEN 1 END) as pending_functions,
            round(100.0 * count(CASE WHEN f.llm_analyzed = true THEN 1 END) / count(f), 2) as enhancement_percentage
    """, "Enhancement Status Overview")
    
    # 2. Most complex functions (fast data)
    run_query("""
        MATCH (f:LogicalFunction)
        RETURN f.name, f.complexity, f.loc, f.language, f.file_path
        ORDER BY f.complexity DESC
        LIMIT 10
    """, "Most Complex Functions (Fast Analysis)")
    
    # 3. Enhanced functions with business concepts
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = true AND size(f.llm_business_concepts) > 0
        RETURN f.name, f.llm_purpose, f.llm_business_concepts, f.complexity
        ORDER BY size(f.llm_business_concepts) DESC
        LIMIT 10
    """, "Functions with Business Concepts (LLM Enhanced)")
    
    # 4. Functions needing improvement (LLM suggestions)
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = true AND size(f.llm_improvement_suggestions) > 0
        RETURN f.name, f.complexity, f.llm_improvement_suggestions, f.llm_confidence
        ORDER BY f.complexity DESC
        LIMIT 10
    """, "Functions with Improvement Suggestions")
    
    # 5. Similarity search using embeddings
    run_query("""
        MATCH (f1:LogicalFunction), (f2:LogicalFunction)
        WHERE f1.name CONTAINS 'process' AND f2.name CONTAINS 'handle' 
        AND f1 <> f2 AND f1.embedding IS NOT NULL AND f2.embedding IS NOT NULL
        RETURN f1.name, f2.name, f1.complexity, f2.complexity
        LIMIT 5
    """, "Similar Functions by Name Pattern")
    
    # 6. Functions by confidence level
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_confidence IS NOT NULL
        RETURN 
            CASE 
                WHEN f.llm_confidence >= 0.9 THEN 'High (0.9+)'
                WHEN f.llm_confidence >= 0.7 THEN 'Medium (0.7-0.9)'
                ELSE 'Low (<0.7)'
            END as confidence_level,
            count(f) as function_count,
            avg(f.complexity) as avg_complexity
        ORDER BY confidence_level
    """, "Functions by LLM Confidence Level")
    
    # 7. Business domain analysis
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = true
        UNWIND f.llm_business_concepts as concept
        RETURN concept, count(f) as function_count, collect(f.name)[0..3] as example_functions
        ORDER BY function_count DESC
        LIMIT 10
    """, "Business Domain Concepts Distribution")
    
    # 8. Code quality insights
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = true
        RETURN 
            f.language,
            count(f) as total_functions,
            avg(f.complexity) as avg_complexity,
            avg(f.loc) as avg_lines_of_code,
            avg(f.llm_confidence) as avg_llm_confidence,
            count(CASE WHEN size(f.llm_improvement_suggestions) > 0 THEN 1 END) as functions_needing_improvement
        ORDER BY avg_complexity DESC
    """, "Code Quality by Language")
    
    # 9. Function call patterns
    run_query("""
        MATCH (f1:LogicalFunction)-[:CALLS]->(f2:LogicalFunction)
        WHERE f1.llm_analyzed = true AND f2.llm_analyzed = true
        RETURN 
            f1.name as caller,
            f1.llm_purpose as caller_purpose,
            f2.name as called,
            f2.llm_purpose as called_purpose,
            f1.complexity + f2.complexity as total_complexity
        ORDER BY total_complexity DESC
        LIMIT 10
    """, "Enhanced Function Call Relationships")
    
    # 10. Architectural patterns usage
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = true AND size(f.llm_related_patterns) > 0
        UNWIND f.llm_related_patterns as pattern
        RETURN pattern, count(f) as usage_count, collect(f.name)[0..3] as example_functions
        ORDER BY usage_count DESC
        LIMIT 10
    """, "Architectural Patterns Usage")
    
    # 11. Functions ready for documentation
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = true 
        AND f.docstring IS NULL 
        AND f.llm_description IS NOT NULL
        RETURN 
            f.name,
            f.file_path,
            f.llm_description,
            f.llm_purpose,
            f.complexity
        ORDER BY f.complexity DESC
        LIMIT 10
    """, "Functions Ready for Documentation")
    
    # 12. Enhancement candidates (not yet analyzed)
    run_query("""
        MATCH (f:LogicalFunction)
        WHERE f.llm_analyzed = false
        AND (f.complexity >= 5 OR f.docstring IS NULL OR size(f.parameters) > 3)
        RETURN 
            f.name,
            f.complexity,
            f.loc,
            f.file_path,
            CASE WHEN f.docstring IS NULL THEN 'Missing docs' ELSE 'Has docs' END as doc_status
        ORDER BY f.complexity DESC
        LIMIT 15
    """, "Top Candidates for LLM Enhancement")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        driver.close()
