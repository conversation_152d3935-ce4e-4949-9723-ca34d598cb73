#!/usr/bin/env python3
"""
Fast Neo4j Graph Generator

Combines the fast parser with Neo4j graph creation.
No heavy dependencies, just fast parsing + graph storage.
"""

import os
import json
import asyncio
from datetime import datetime
from typing import List
from minimal_graph_generator import MinimalParser, MinimalFunction

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded .env file")
except ImportError:
    print("⚠️  python-dotenv not available. Install with: uv pip install python-dotenv")

# Try to import Neo4j, make it optional for testing
try:
    from neo4j import GraphDatabase
    NEO4J_AVAILABLE = True
except ImportError:
    print("⚠️  Neo4j not available. Install with: uv pip install neo4j")
    NEO4J_AVAILABLE = False

# Configuration
REPO_PATH = os.getenv("REPO_PATH", "/Users/<USER>/IdeaProjects/cymulate-bas-platform")
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

print(f"🔧 Neo4j Configuration:")
print(f"   URI: {NEO4J_URI}")
print(f"   User: {NEO4J_USER}")
print(f"   Password: {'*' * len(NEO4J_PASSWORD) if NEO4J_PASSWORD else 'None'}")

class FastGraphBuilder:
    """Fast graph builder for Neo4j"""
    
    def __init__(self):
        if NEO4J_AVAILABLE:
            self.driver = GraphDatabase.driver(
                NEO4J_URI, 
                auth=(NEO4J_USER, NEO4J_PASSWORD)
            )
        else:
            self.driver = None
    
    async def create_graph(self, functions: List[MinimalFunction]) -> None:
        """Create graph from functions"""
        if not self.driver:
            print("❌ Neo4j not available, saving to JSON instead")
            await self._save_to_json(functions)
            return
        
        print("🏗️ Creating graph schema...")
        await self._create_schema()
        
        print(f"📊 Adding {len(functions)} functions to graph...")
        await self._add_functions_batch(functions)
        
        print("🔗 Creating relationships...")
        await self._create_relationships(functions)
        
        print("✅ Graph creation completed")
    
    async def _create_schema(self) -> None:
        """Create graph schema"""
        with self.driver.session() as session:
            # Clear existing data
            session.run("MATCH (n) DETACH DELETE n")
            
            # Skip constraints for now to avoid conflicts
            print("   Skipping constraints to avoid conflicts")
    
    async def _add_functions_batch(self, functions: List[MinimalFunction]) -> None:
        """Add functions to graph in batches"""
        batch_size = 100
        
        for i in range(0, len(functions), batch_size):
            batch = functions[i:i + batch_size]
            
            with self.driver.session() as session:
                for func in batch:
                    # Infer service from file path
                    service_name = self._infer_service(func.file_path)
                    
                    session.run("""
                        MERGE (f:LogicalFunction {id: $id})
                        SET f.name = $name,
                            f.signature = $signature,
                            f.complexity = $complexity,
                            f.loc = $loc,
                            f.language = $language,
                            f.file_path = $file_path,
                            f.start_line = $start_line,
                            f.end_line = $end_line,
                            f.parameters = $parameters,
                            f.is_async = $is_async,
                            f.created_at = datetime(),
                            f.llm_analyzed = false,
                            f.llm_description = null,
                            f.llm_purpose = null,
                            f.llm_business_concepts = [],
                            f.llm_improvement_suggestions = [],
                            f.llm_confidence = null
                    """, {
                        'id': func.id,
                        'name': func.name,
                        'signature': func.signature,
                        'complexity': func.complexity,
                        'loc': func.loc,
                        'language': func.language,
                        'file_path': func.file_path,
                        'start_line': func.start_line,
                        'end_line': func.end_line,
                        'parameters': func.parameters,
                        'is_async': func.is_async
                    })
                    
                    # Create service node and relationship
                    if service_name:
                        session.run("""
                            MERGE (s:Service {name: $service_name})
                            SET s.type = 'microservice',
                                s.updated_at = datetime()
                            WITH s
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:BELONGS_TO]->(s)
                        """, {
                            'service_name': service_name,
                            'function_id': func.id
                        })
            
            print(f"📈 Progress: {min(i + batch_size, len(functions))}/{len(functions)} functions added")
    
    async def _create_relationships(self, functions: List[MinimalFunction]) -> None:
        """Create relationships between functions"""
        with self.driver.session() as session:
            for func in functions:
                for called_func in func.calls:
                    # Only create relationships for functions that exist in our graph
                    session.run("""
                        MATCH (f1:LogicalFunction {name: $caller})
                        MATCH (f2:LogicalFunction)
                        WHERE f2.name ENDS WITH $called OR f2.name = $called
                        MERGE (f1)-[:CALLS]->(f2)
                    """, {
                        'caller': func.name,
                        'called': called_func
                    })
    
    def _infer_service(self, file_path: str) -> str:
        """Infer service name from file path"""
        path_parts = file_path.split('/')
        
        # Look for common service indicators
        for part in path_parts:
            if any(keyword in part.lower() for keyword in ['service', 'api', 'controller', 'handler']):
                return part
        
        # Use directory structure
        if len(path_parts) > 2:
            return path_parts[-3]  # Third from last (usually service directory)
        
        return 'unknown'
    
    async def _save_to_json(self, functions: List[MinimalFunction]) -> None:
        """Save functions to JSON file as fallback"""
        output_file = "functions_output.json"
        
        functions_data = []
        for func in functions:
            functions_data.append({
                'id': func.id,
                'name': func.name,
                'signature': func.signature,
                'file_path': func.file_path,
                'start_line': func.start_line,
                'end_line': func.end_line,
                'language': func.language,
                'complexity': func.complexity,
                'loc': func.loc,
                'parameters': func.parameters,
                'calls': func.calls,
                'is_async': func.is_async
            })
        
        with open(output_file, 'w') as f:
            json.dump(functions_data, f, indent=2)
        
        print(f"💾 Saved {len(functions)} functions to {output_file}")
    
    def close(self):
        """Close database connection"""
        if self.driver:
            self.driver.close()

async def main():
    """Main function"""
    print("🚀 Fast Neo4j Graph Generator")
    print("=" * 40)
    
    if not os.path.exists(REPO_PATH):
        print(f"❌ Repository path not found: {REPO_PATH}")
        return
    
    # Phase 1: Parse code (fast)
    parser = MinimalParser()
    graph_builder = FastGraphBuilder()
    
    start_time = datetime.now()
    
    try:
        print("📁 Phase 1: Fast Code Parsing")
        functions = await parser.parse_repository(REPO_PATH)
        
        parse_time = datetime.now()
        parse_duration = (parse_time - start_time).total_seconds()
        
        print(f"✅ Parsing completed in {parse_duration:.2f} seconds")
        print(f"📊 Found {len(functions)} functions")
        
        if not functions:
            print("❌ No functions found")
            return
        
        # Phase 2: Create graph
        print(f"\n🏗️ Phase 2: Graph Creation")
        await graph_builder.create_graph(functions)
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        graph_duration = (end_time - parse_time).total_seconds()
        
        print(f"\n🎉 Complete! Total time: {total_duration:.2f} seconds")
        print(f"   • Parsing: {parse_duration:.2f}s")
        print(f"   • Graph creation: {graph_duration:.2f}s")
        
        if NEO4J_AVAILABLE:
            print(f"🌐 View graph in Neo4j Browser: http://localhost:7474")
            print(f"💡 Try these queries:")
            print(f"   • MATCH (n) RETURN n LIMIT 25")
            print(f"   • MATCH (f:LogicalFunction) RETURN f.language, count(f) ORDER BY count(f) DESC")
            print(f"   • MATCH (f:LogicalFunction) WHERE f.complexity > 10 RETURN f.name, f.complexity ORDER BY f.complexity DESC")
        
        print(f"\n🔮 Ready for LLM enhancement with: python llm_enhancer.py")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        graph_builder.close()

if __name__ == "__main__":
    asyncio.run(main())
