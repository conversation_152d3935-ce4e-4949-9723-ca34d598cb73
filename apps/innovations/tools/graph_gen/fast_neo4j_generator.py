#!/usr/bin/env python3
"""
Fast Neo4j Graph Generator

Combines the fast parser with Neo4j graph creation.
No heavy dependencies, just fast parsing + graph storage.
"""

import os
import json
import asyncio
from datetime import datetime
from typing import List
from minimal_graph_generator import MinimalParser, MinimalFunction

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded .env file")
except ImportError:
    print("⚠️  python-dotenv not available. Install with: uv pip install python-dotenv")

# Try to import Neo4j, make it optional for testing
try:
    from neo4j import GraphDatabase
    NEO4J_AVAILABLE = True
except ImportError:
    print("⚠️  Neo4j not available. Install with: uv pip install neo4j")
    NEO4J_AVAILABLE = False

# Configuration
REPO_PATH = os.getenv("REPO_PATH", "/Users/<USER>/IdeaProjects/cymulate-bas-platform")
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

print(f"🔧 Neo4j Configuration:")
print(f"   URI: {NEO4J_URI}")
print(f"   User: {NEO4J_USER}")
print(f"   Password: {'*' * len(NEO4J_PASSWORD) if NEO4J_PASSWORD else 'None'}")

class FastGraphBuilder:
    """Fast graph builder for Neo4j"""
    
    def __init__(self):
        if NEO4J_AVAILABLE:
            self.driver = GraphDatabase.driver(
                NEO4J_URI, 
                auth=(NEO4J_USER, NEO4J_PASSWORD)
            )
        else:
            self.driver = None
    
    async def create_graph(self, functions: List[MinimalFunction]) -> None:
        """Create graph from functions"""
        if not self.driver:
            print("❌ Neo4j not available, saving to JSON instead")
            await self._save_to_json(functions)
            return
        
        print("🏗️ Creating graph schema...")
        await self._create_schema()
        
        print(f"📊 Adding {len(functions)} functions to graph...")
        await self._add_functions_batch(functions)
        
        print("🔗 Creating relationships...")
        await self._create_relationships(functions)
        
        print("✅ Graph creation completed")
    
    async def _create_schema(self) -> None:
        """Create graph schema"""
        with self.driver.session() as session:
            # Clear existing data
            session.run("MATCH (n) DETACH DELETE n")
            
            # Skip constraints for now to avoid conflicts
            print("   Skipping constraints to avoid conflicts")
    
    async def _add_functions_batch(self, functions: List[MinimalFunction]) -> None:
        """Add functions to graph with rich schema"""
        batch_size = 50  # Smaller batches for complex operations

        for i in range(0, len(functions), batch_size):
            batch = functions[i:i + batch_size]

            with self.driver.session() as session:
                for func in batch:
                    # Create LogicalFunction node
                    session.run("""
                        CREATE (f:LogicalFunction {
                            id: $id,
                            name: $name,
                            signature: $signature,
                            complexity: $complexity,
                            loc: $loc,
                            language: $language,
                            file_path: $file_path,
                            start_line: $start_line,
                            end_line: $end_line,
                            parameters: $parameters,
                            is_async: $is_async,
                            function_type: $function_type,
                            created_at: datetime(),
                            llm_analyzed: false,
                            llm_description: null,
                            llm_purpose: null,
                            llm_business_concepts: [],
                            llm_improvement_suggestions: [],
                            llm_confidence: null
                        })
                    """, {
                        'id': func.id,
                        'name': func.name,
                        'signature': func.signature,
                        'complexity': func.complexity,
                        'loc': func.loc,
                        'language': func.language,
                        'file_path': func.file_path,
                        'start_line': func.start_line,
                        'end_line': func.end_line,
                        'parameters': func.parameters,
                        'is_async': func.is_async,
                        'function_type': self._classify_function_type(func)
                    })

                    # Create and link Service
                    service_info = self._extract_service_info(func.file_path)
                    if service_info:
                        session.run("""
                            MERGE (s:Service {name: $service_name})
                            SET s.type = $service_type,
                                s.path = $service_path,
                                s.updated_at = datetime()
                            WITH s
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:BELONGS_TO]->(s)
                        """, {
                            'service_name': service_info['name'],
                            'service_type': service_info['type'],
                            'service_path': service_info['path'],
                            'function_id': func.id
                        })

                    # Create and link Module
                    module_info = self._extract_module_info(func.file_path)
                    if module_info:
                        session.run("""
                            MERGE (m:Module {name: $module_name})
                            SET m.path = $module_path,
                                m.type = $module_type,
                                m.updated_at = datetime()
                            WITH m
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:PART_OF]->(m)
                        """, {
                            'module_name': module_info['name'],
                            'module_path': module_info['path'],
                            'module_type': module_info['type'],
                            'function_id': func.id
                        })

                    # Create Entities from parameters and return types
                    entities = self._extract_entities(func)
                    for entity in entities:
                        session.run("""
                            MERGE (e:Entity {name: $entity_name})
                            SET e.type = $entity_type,
                                e.source = $source,
                                e.updated_at = datetime()
                            WITH e
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:USES_ENTITY]->(e)
                        """, {
                            'entity_name': entity['name'],
                            'entity_type': entity['type'],
                            'source': entity['source'],
                            'function_id': func.id
                        })

                    # Create API Endpoints
                    endpoints = self._extract_endpoints(func)
                    for endpoint in endpoints:
                        session.run("""
                            MERGE (ep:Endpoint {path: $path})
                            SET ep.method = $method,
                                ep.type = $endpoint_type,
                                ep.updated_at = datetime()
                            WITH ep
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:EXPOSES_API]->(ep)
                        """, {
                            'path': endpoint['path'],
                            'method': endpoint['method'],
                            'endpoint_type': endpoint['type'],
                            'function_id': func.id
                        })

                    # Create Business Concepts
                    concepts = self._extract_business_concepts(func)
                    for concept in concepts:
                        session.run("""
                            MERGE (bc:BusinessConcept {name: $concept_name})
                            SET bc.domain = $domain,
                                bc.updated_at = datetime()
                            WITH bc
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:RELATES_TO]->(bc)
                        """, {
                            'concept_name': concept['name'],
                            'domain': concept['domain'],
                            'function_id': func.id
                        })

                    # Create Dependencies
                    dependencies = self._extract_dependencies(func)
                    for dep in dependencies:
                        session.run("""
                            MERGE (d:Dependency {name: $dep_name})
                            SET d.type = $dep_type,
                                d.version = $version,
                                d.updated_at = datetime()
                            WITH d
                            MATCH (f:LogicalFunction {id: $function_id})
                            MERGE (f)-[:DEPENDS_ON]->(d)
                        """, {
                            'dep_name': dep['name'],
                            'dep_type': dep['type'],
                            'version': dep.get('version', 'unknown'),
                            'function_id': func.id
                        })

            print(f"📈 Progress: {min(i + batch_size, len(functions))}/{len(functions)} functions added")
    
    async def _create_relationships(self, functions: List[MinimalFunction]) -> None:
        """Create relationships between functions"""
        with self.driver.session() as session:
            for func in functions:
                for called_func in func.calls:
                    # Only create relationships for functions that exist in our graph
                    session.run("""
                        MATCH (f1:LogicalFunction {name: $caller})
                        MATCH (f2:LogicalFunction)
                        WHERE f2.name ENDS WITH $called OR f2.name = $called
                        MERGE (f1)-[:CALLS]->(f2)
                    """, {
                        'caller': func.name,
                        'called': called_func
                    })
    
    def _classify_function_type(self, func: MinimalFunction) -> str:
        """Classify function type based on name and context"""
        name_lower = func.name.lower()

        # API Controllers
        if any(keyword in name_lower for keyword in ['controller', 'handler', 'route', 'endpoint']):
            return 'Controller'

        # Services
        if any(keyword in name_lower for keyword in ['service', 'process', 'execute', 'handle']):
            return 'Service'

        # Repository/Data Access
        if any(keyword in name_lower for keyword in ['repository', 'dao', 'find', 'save', 'delete', 'update', 'get', 'fetch']):
            return 'Repository'

        # Utilities
        if any(keyword in name_lower for keyword in ['util', 'helper', 'format', 'parse', 'validate']):
            return 'Utility'

        # Models/DTOs
        if any(keyword in name_lower for keyword in ['model', 'dto', 'entity', 'schema']):
            return 'Model'

        # Event Handlers
        if any(keyword in name_lower for keyword in ['event', 'listener', 'subscriber', 'observer']):
            return 'EventHandler'

        return 'Function'

    def _extract_service_info(self, file_path: str) -> dict:
        """Extract service information from file path"""
        path_parts = file_path.split('/')

        # Look for service indicators
        service_name = 'unknown'
        service_type = 'module'

        for part in path_parts:
            part_lower = part.lower()
            if any(keyword in part_lower for keyword in ['service', 'api', 'controller']):
                service_name = part
                service_type = 'microservice'
                break
            elif any(keyword in part_lower for keyword in ['component', 'module', 'lib', 'utils']):
                service_name = part
                service_type = 'library'
                break

        # Use directory structure if no specific service found
        if service_name == 'unknown' and len(path_parts) > 2:
            service_name = path_parts[-3]

        return {
            'name': service_name,
            'type': service_type,
            'path': '/'.join(path_parts[:-1])
        }

    def _extract_module_info(self, file_path: str) -> dict:
        """Extract module information from file path"""
        path_parts = file_path.split('/')
        file_name = path_parts[-1].replace('.ts', '').replace('.js', '').replace('.tsx', '').replace('.jsx', '')

        module_type = 'module'
        if any(keyword in file_name.lower() for keyword in ['component', 'page', 'view']):
            module_type = 'component'
        elif any(keyword in file_name.lower() for keyword in ['service', 'api']):
            module_type = 'service'
        elif any(keyword in file_name.lower() for keyword in ['util', 'helper']):
            module_type = 'utility'

        return {
            'name': file_name,
            'path': file_path,
            'type': module_type
        }

    def _extract_entities(self, func: MinimalFunction) -> list:
        """Extract entities from function parameters and signature"""
        entities = []

        # Extract from parameters
        for param in func.parameters:
            # Look for type annotations
            if ':' in param:
                param_name, param_type = param.split(':', 1)
                param_type = param_type.strip()

                # Skip primitive types
                if param_type.lower() not in ['string', 'number', 'boolean', 'void', 'any', 'object']:
                    entities.append({
                        'name': param_type,
                        'type': 'ParameterType',
                        'source': f"parameter_{param_name.strip()}"
                    })

        # Extract from function name patterns
        name_lower = func.name.lower()
        if any(keyword in name_lower for keyword in ['user', 'account', 'profile']):
            entities.append({
                'name': 'User',
                'type': 'DomainEntity',
                'source': 'function_name'
            })
        elif any(keyword in name_lower for keyword in ['order', 'purchase', 'payment']):
            entities.append({
                'name': 'Order',
                'type': 'DomainEntity',
                'source': 'function_name'
            })
        elif any(keyword in name_lower for keyword in ['product', 'item', 'catalog']):
            entities.append({
                'name': 'Product',
                'type': 'DomainEntity',
                'source': 'function_name'
            })

        return entities

    def _extract_endpoints(self, func: MinimalFunction) -> list:
        """Extract API endpoints from function"""
        endpoints = []

        name_lower = func.name.lower()
        signature_lower = func.signature.lower()

        # Look for HTTP method patterns
        http_methods = ['get', 'post', 'put', 'delete', 'patch']
        method = 'GET'  # default

        for http_method in http_methods:
            if http_method in name_lower or http_method in signature_lower:
                method = http_method.upper()
                break

        # Generate endpoint path from function name
        if any(keyword in name_lower for keyword in ['api', 'endpoint', 'route', 'controller']):
            # Extract potential path from function name
            path_parts = []

            if 'user' in name_lower:
                path_parts.append('users')
            elif 'order' in name_lower:
                path_parts.append('orders')
            elif 'product' in name_lower:
                path_parts.append('products')
            elif 'auth' in name_lower:
                path_parts.append('auth')

            if 'get' in name_lower:
                if 'all' in name_lower or 'list' in name_lower:
                    pass  # /users
                else:
                    path_parts.append('{id}')  # /users/{id}
            elif 'create' in name_lower or 'post' in name_lower:
                pass  # /users
            elif 'update' in name_lower or 'put' in name_lower:
                path_parts.append('{id}')  # /users/{id}
            elif 'delete' in name_lower:
                path_parts.append('{id}')  # /users/{id}

            if path_parts:
                path = '/' + '/'.join(path_parts)
                endpoints.append({
                    'path': path,
                    'method': method,
                    'type': 'REST_API'
                })

        return endpoints

    def _extract_business_concepts(self, func: MinimalFunction) -> list:
        """Extract business concepts from function"""
        concepts = []

        name_lower = func.name.lower()
        file_path_lower = func.file_path.lower()

        # Domain concepts based on function names
        domain_mapping = {
            'user': ['User Management', 'Authentication'],
            'auth': ['Authentication', 'Security'],
            'order': ['Order Management', 'E-commerce'],
            'payment': ['Payment Processing', 'Financial'],
            'product': ['Product Catalog', 'Inventory'],
            'inventory': ['Inventory Management', 'Stock'],
            'notification': ['Notification System', 'Communication'],
            'email': ['Email Service', 'Communication'],
            'report': ['Reporting', 'Analytics'],
            'analytics': ['Analytics', 'Business Intelligence'],
            'security': ['Security', 'Compliance'],
            'audit': ['Audit Trail', 'Compliance']
        }

        for keyword, business_concepts in domain_mapping.items():
            if keyword in name_lower or keyword in file_path_lower:
                for concept in business_concepts:
                    concepts.append({
                        'name': concept,
                        'domain': self._infer_domain(func.file_path)
                    })
                break

        return concepts

    def _extract_dependencies(self, func: MinimalFunction) -> list:
        """Extract dependencies from function calls"""
        dependencies = []

        # Common external libraries/frameworks
        external_deps = {
            'axios': {'type': 'HTTP_Client', 'category': 'networking'},
            'fetch': {'type': 'HTTP_Client', 'category': 'networking'},
            'express': {'type': 'Web_Framework', 'category': 'framework'},
            'react': {'type': 'UI_Framework', 'category': 'frontend'},
            'lodash': {'type': 'Utility_Library', 'category': 'utility'},
            'moment': {'type': 'Date_Library', 'category': 'utility'},
            'bcrypt': {'type': 'Crypto_Library', 'category': 'security'},
            'jwt': {'type': 'Auth_Library', 'category': 'security'},
            'mongoose': {'type': 'Database_ORM', 'category': 'database'},
            'sequelize': {'type': 'Database_ORM', 'category': 'database'},
            'redis': {'type': 'Cache_Database', 'category': 'database'},
            'winston': {'type': 'Logging_Library', 'category': 'logging'}
        }

        for call in func.calls:
            call_lower = call.lower()
            for dep_name, dep_info in external_deps.items():
                if dep_name in call_lower:
                    dependencies.append({
                        'name': dep_name,
                        'type': dep_info['type'],
                        'category': dep_info['category']
                    })

        return dependencies

    def _infer_domain(self, file_path: str) -> str:
        """Infer business domain from file path"""
        path_lower = file_path.lower()

        if any(keyword in path_lower for keyword in ['user', 'auth', 'account']):
            return 'User Management'
        elif any(keyword in path_lower for keyword in ['order', 'purchase', 'cart']):
            return 'E-commerce'
        elif any(keyword in path_lower for keyword in ['product', 'catalog', 'inventory']):
            return 'Product Management'
        elif any(keyword in path_lower for keyword in ['payment', 'billing', 'finance']):
            return 'Financial'
        elif any(keyword in path_lower for keyword in ['notification', 'email', 'sms']):
            return 'Communication'
        elif any(keyword in path_lower for keyword in ['report', 'analytics', 'dashboard']):
            return 'Analytics'
        else:
            return 'Core System'
    
    async def _save_to_json(self, functions: List[MinimalFunction]) -> None:
        """Save functions to JSON file as fallback"""
        output_file = "functions_output.json"
        
        functions_data = []
        for func in functions:
            functions_data.append({
                'id': func.id,
                'name': func.name,
                'signature': func.signature,
                'file_path': func.file_path,
                'start_line': func.start_line,
                'end_line': func.end_line,
                'language': func.language,
                'complexity': func.complexity,
                'loc': func.loc,
                'parameters': func.parameters,
                'calls': func.calls,
                'is_async': func.is_async
            })
        
        with open(output_file, 'w') as f:
            json.dump(functions_data, f, indent=2)
        
        print(f"💾 Saved {len(functions)} functions to {output_file}")
    
    def close(self):
        """Close database connection"""
        if self.driver:
            self.driver.close()

async def main():
    """Main function"""
    print("🚀 Fast Neo4j Graph Generator")
    print("=" * 40)
    
    if not os.path.exists(REPO_PATH):
        print(f"❌ Repository path not found: {REPO_PATH}")
        return
    
    # Phase 1: Parse code (fast)
    parser = MinimalParser()
    graph_builder = FastGraphBuilder()
    
    start_time = datetime.now()
    
    try:
        print("📁 Phase 1: Fast Code Parsing")
        functions = await parser.parse_repository(REPO_PATH)
        
        parse_time = datetime.now()
        parse_duration = (parse_time - start_time).total_seconds()
        
        print(f"✅ Parsing completed in {parse_duration:.2f} seconds")
        print(f"📊 Found {len(functions)} functions")
        
        if not functions:
            print("❌ No functions found")
            return
        
        # Phase 2: Create graph
        print(f"\n🏗️ Phase 2: Graph Creation")
        await graph_builder.create_graph(functions)
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        graph_duration = (end_time - parse_time).total_seconds()
        
        print(f"\n🎉 Complete! Total time: {total_duration:.2f} seconds")
        print(f"   • Parsing: {parse_duration:.2f}s")
        print(f"   • Graph creation: {graph_duration:.2f}s")
        
        if NEO4J_AVAILABLE:
            print(f"🌐 View graph in Neo4j Browser: http://localhost:7474")
            print(f"💡 Try these queries:")
            print(f"   • MATCH (n) RETURN n LIMIT 25")
            print(f"   • MATCH (f:LogicalFunction) RETURN f.language, count(f) ORDER BY count(f) DESC")
            print(f"   • MATCH (f:LogicalFunction) WHERE f.complexity > 10 RETURN f.name, f.complexity ORDER BY f.complexity DESC")
        
        print(f"\n🔮 Ready for LLM enhancement with: python llm_enhancer.py")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        graph_builder.close()

if __name__ == "__main__":
    asyncio.run(main())
