#!/usr/bin/env python3
"""
Fast Code Graph Generator - Phase 1

Generates code graph quickly using Tree-sitter and local analysis.
LLM enhancement can be added later as Phase 2.
"""

import os
import hashlib
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import logging
import ast
# Tree-sitter imports (will need to install)
# pip install tree-sitter tree-sitter-python tree-sitter-javascript tree-sitter-typescript
try:
    import tree_sitter
    from tree_sitter import Language, Parser
except ImportError:
    print("⚠️  Tree-sitter not installed. Run: pip install tree-sitter tree-sitter-python")

from sentence_transformers import SentenceTransformer
from neo4j import GraphDatabase
from dotenv import load_dotenv

load_dotenv()

# Configuration
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")
REPO_PATH = os.getenv("REPO_PATH", "/path/to/your/code")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class FastCodeFunction:
    """Represents a function with fast analysis data"""
    id: str
    name: str
    signature: str
    code: str
    code_hash: str
    file_path: str
    start_line: int
    end_line: int
    language: str
    parameters: List[str]
    return_type: Optional[str]
    docstring: Optional[str]
    complexity: int
    loc: int
    is_async: bool
    is_generator: bool
    decorators: List[str]
    calls: List[str]  # Functions this function calls
    imports: List[str]  # Modules this function imports
    embedding: List[float]

class TreeSitterParser:
    """Fast parser using Tree-sitter"""
    
    def __init__(self):
        self.parsers = {}
        self.languages = {}
        self._setup_languages()
    
    def _setup_languages(self):
        """Setup Tree-sitter languages"""
        try:
            # This would need proper Tree-sitter language setup
            # For now, we'll use a simplified approach
            self.supported_extensions = {
                '.py': 'python',
                '.js': 'javascript', 
                '.ts': 'typescript',
                '.jsx': 'javascript',
                '.tsx': 'typescript',
                '.java': 'java',
                '.go': 'go',
                '.rs': 'rust',
                '.cpp': 'cpp',
                '.c': 'c'
            }
        except Exception as e:
            logger.warning(f"Tree-sitter setup failed: {e}")
            self.supported_extensions = {}
    
    async def parse_repository(self, repo_path: str) -> List[FastCodeFunction]:
        """Parse entire repository quickly"""
        logger.info(f"🚀 Starting fast parsing of: {repo_path}")
        
        functions = []
        code_files = self._get_code_files(repo_path)
        
        # Process files in parallel
        tasks = []
        for file_path in code_files:
            task = self._parse_file(file_path)
            tasks.append(task)
        
        # Execute in batches to avoid overwhelming the system
        batch_size = 10
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            results = await asyncio.gather(*batch, return_exceptions=True)
            
            for result in results:
                if isinstance(result, list):
                    functions.extend(result)
                elif isinstance(result, Exception):
                    logger.error(f"Error parsing file: {result}")
        
        logger.info(f"✅ Parsed {len(functions)} functions from {len(code_files)} files")
        return functions
    
    def _get_code_files(self, repo_path: str) -> List[Path]:
        """Get all code files in repository"""
        repo_path = Path(repo_path)
        code_files = []
        
        ignore_dirs = {
            'node_modules', '.git', '__pycache__', 'dist', 'build', 
            '.venv', 'venv', '.env', 'target', 'bin', 'obj'
        }
        
        for ext in self.supported_extensions.keys():
            pattern = f"**/*{ext}"
            files = repo_path.glob(pattern)
            
            for file_path in files:
                # Skip ignored directories
                if not any(ignore_dir in file_path.parts for ignore_dir in ignore_dirs):
                    code_files.append(file_path)
        
        return code_files
    
    async def _parse_file(self, file_path: Path) -> List[FastCodeFunction]:
        """Parse a single file"""
        try:
            extension = file_path.suffix
            language = self.supported_extensions.get(extension, 'unknown')
            
            if language == 'unknown':
                return []
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # For now, use simplified parsing
            # In full implementation, this would use Tree-sitter
            if language == 'python':
                return await self._parse_python_file(file_path, content)
            elif language in ['javascript', 'typescript']:
                return await self._parse_js_ts_file(file_path, content)
            else:
                return await self._parse_generic_file(file_path, content, language)
                
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
            return []
    
    async def _parse_python_file(self, file_path: Path, content: str) -> List[FastCodeFunction]:
        """Parse Python file (simplified - would use Tree-sitter in full implementation)"""
        import ast
        
        functions = []
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    func = await self._extract_python_function(file_path, content, node)
                    if func:
                        functions.append(func)
                        
        except Exception as e:
            logger.error(f"Error parsing Python AST in {file_path}: {e}")
        
        return functions
    
    async def _extract_python_function(self, file_path: Path, content: str, node: ast.FunctionDef) -> Optional[FastCodeFunction]:
        """Extract Python function details"""
        try:
            lines = content.split('\n')
            start_line = node.lineno
            end_line = node.end_lineno or start_line + 10
            
            func_code = '\n'.join(lines[start_line-1:end_line])
            code_hash = hashlib.sha256(func_code.encode()).hexdigest()[:16]
            
            # Extract parameters
            parameters = []
            for arg in node.args.args:
                param_str = arg.arg
                if arg.annotation:
                    param_str += f": {ast.unparse(arg.annotation)}"
                parameters.append(param_str)
            
            # Extract return type
            return_type = None
            if node.returns:
                return_type = ast.unparse(node.returns)
            
            # Extract docstring
            docstring = None
            if (node.body and isinstance(node.body[0], ast.Expr) and 
                isinstance(node.body[0].value, ast.Constant) and 
                isinstance(node.body[0].value.value, str)):
                docstring = node.body[0].value.value
            
            # Calculate complexity (simplified)
            complexity = self._calculate_complexity(node)
            
            # Extract function calls (simplified)
            calls = []
            for child in ast.walk(node):
                if isinstance(child, ast.Call) and isinstance(child.func, ast.Name):
                    calls.append(child.func.id)
            
            # Extract decorators
            decorators = []
            for decorator in node.decorator_list:
                if isinstance(decorator, ast.Name):
                    decorators.append(f"@{decorator.id}")
            
            return FastCodeFunction(
                id=f"{file_path.stem}_{node.name}_{code_hash}",
                name=node.name,
                signature=f"def {node.name}({', '.join(parameters)})",
                code=func_code,
                code_hash=code_hash,
                file_path=str(file_path),
                start_line=start_line,
                end_line=end_line,
                language='python',
                parameters=parameters,
                return_type=return_type,
                docstring=docstring,
                complexity=complexity,
                loc=end_line - start_line + 1,
                is_async=isinstance(node, ast.AsyncFunctionDef),
                is_generator=any(isinstance(n, ast.Yield) for n in ast.walk(node)),
                decorators=decorators,
                calls=list(set(calls)),
                imports=[],  # Would extract from file level
                embedding=[]  # Will be generated later
            )
            
        except Exception as e:
            logger.error(f"Error extracting function {node.name}: {e}")
            return None
    
    def _calculate_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity (simplified)"""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    async def _parse_js_ts_file(self, file_path: Path, content: str) -> List[FastCodeFunction]:
        """Parse JavaScript/TypeScript file (placeholder)"""
        # Would implement Tree-sitter parsing here
        return []
    
    async def _parse_generic_file(self, file_path: Path, content: str, language: str) -> List[FastCodeFunction]:
        """Parse other language files (placeholder)"""
        # Would implement Tree-sitter parsing here
        return []

class VectorGenerator:
    """Generate embeddings for functions"""
    
    def __init__(self):
        logger.info("🔧 Loading embedding model...")
        self.model = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info("✅ Embedding model loaded")
    
    async def generate_embeddings(self, functions: List[FastCodeFunction]) -> None:
        """Generate embeddings for all functions"""
        logger.info(f"🧠 Generating embeddings for {len(functions)} functions...")
        
        # Prepare texts for embedding
        texts = []
        for func in functions:
            text_parts = [
                func.name,
                func.docstring or "",
                func.signature,
                " ".join(func.parameters),
                func.return_type or ""
            ]
            combined_text = " ".join(filter(None, text_parts))
            texts.append(combined_text)
        
        # Generate embeddings in batch
        embeddings = self.model.encode(texts, batch_size=32, show_progress_bar=True)
        
        # Assign embeddings back to functions
        for func, embedding in zip(functions, embeddings):
            func.embedding = embedding.tolist()
        
        logger.info("✅ Embeddings generated")

class FastGraphBuilder:
    """Build Neo4j graph quickly"""
    
    def __init__(self):
        self.driver = GraphDatabase.driver(
            NEO4J_URI, 
            auth=(NEO4J_USER, NEO4J_PASSWORD)
        )
    
    async def create_graph(self, functions: List[FastCodeFunction]) -> None:
        """Create graph from functions"""
        logger.info("🏗️ Creating graph schema...")
        await self._create_schema()
        
        logger.info(f"📊 Adding {len(functions)} functions to graph...")
        await self._add_functions_batch(functions)
        
        logger.info("🔗 Creating relationships...")
        await self._create_relationships(functions)
        
        logger.info("✅ Graph creation completed")
    
    async def _create_schema(self) -> None:
        """Create graph schema with LLM-ready properties"""
        with self.driver.session() as session:
            # Clear existing data
            session.run("MATCH (n) DETACH DELETE n")
            
            # Create constraints
            constraints = [
                "CREATE CONSTRAINT function_id IF NOT EXISTS FOR (f:LogicalFunction) REQUIRE f.id IS UNIQUE",
                "CREATE CONSTRAINT service_name IF NOT EXISTS FOR (s:Service) REQUIRE s.name IS UNIQUE"
            ]
            
            for constraint in constraints:
                try:
                    session.run(constraint)
                except Exception as e:
                    logger.warning(f"Constraint warning: {e}")
    
    async def _add_functions_batch(self, functions: List[FastCodeFunction]) -> None:
        """Add functions to graph in batches"""
        batch_size = 100
        
        for i in range(0, len(functions), batch_size):
            batch = functions[i:i + batch_size]
            
            with self.driver.session() as session:
                for func in batch:
                    session.run("""
                        CREATE (f:LogicalFunction {
                            id: $id,
                            name: $name,
                            signature: $signature,
                            code_hash: $code_hash,
                            embedding: $embedding,
                            complexity: $complexity,
                            loc: $loc,
                            language: $language,
                            file_path: $file_path,
                            start_line: $start_line,
                            end_line: $end_line,
                            parameters: $parameters,
                            return_type: $return_type,
                            docstring: $docstring,
                            is_async: $is_async,
                            is_generator: $is_generator,
                            decorators: $decorators,
                            created_at: datetime(),
                            
                            // LLM Enhancement fields (initially null)
                            llm_analyzed: false,
                            llm_description: null,
                            llm_purpose: null,
                            llm_business_concepts: [],
                            llm_complexity_explanation: null,
                            llm_improvement_suggestions: [],
                            llm_usage_examples: [],
                            llm_related_patterns: [],
                            llm_embedding: null,
                            llm_analyzed_at: null,
                            llm_model_used: null,
                            llm_confidence: null
                        })
                    """, {
                        'id': func.id,
                        'name': func.name,
                        'signature': func.signature,
                        'code_hash': func.code_hash,
                        'embedding': func.embedding,
                        'complexity': func.complexity,
                        'loc': func.loc,
                        'language': func.language,
                        'file_path': func.file_path,
                        'start_line': func.start_line,
                        'end_line': func.end_line,
                        'parameters': func.parameters,
                        'return_type': func.return_type,
                        'docstring': func.docstring,
                        'is_async': func.is_async,
                        'is_generator': func.is_generator,
                        'decorators': func.decorators
                    })
    
    async def _create_relationships(self, functions: List[FastCodeFunction]) -> None:
        """Create relationships between functions"""
        # Create CALLS relationships
        with self.driver.session() as session:
            for func in functions:
                for called_func in func.calls:
                    session.run("""
                        MATCH (f1:LogicalFunction {name: $caller})
                        MATCH (f2:LogicalFunction {name: $called})
                        MERGE (f1)-[:CALLS]->(f2)
                    """, {
                        'caller': func.name,
                        'called': called_func
                    })
    
    def close(self):
        """Close database connection"""
        self.driver.close()

class FastCodeGraphGenerator:
    """Main fast graph generator"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.parser = TreeSitterParser()
        self.vector_generator = VectorGenerator()
        self.graph_builder = FastGraphBuilder()
    
    async def generate_fast_graph(self) -> None:
        """Generate graph quickly without LLM"""
        start_time = datetime.now()
        
        logger.info("🚀 Starting FAST code graph generation...")
        
        # Phase 1: Parse code
        functions = await self.parser.parse_repository(self.repo_path)
        
        if not functions:
            logger.error("❌ No functions found")
            return
        
        # Phase 2: Generate embeddings
        await self.vector_generator.generate_embeddings(functions)
        
        # Phase 3: Create graph
        await self.graph_builder.create_graph(functions)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Fast graph generation completed in {duration:.2f} seconds!")
        logger.info(f"📊 Processed {len(functions)} functions")
        logger.info("🔮 Ready for LLM enhancement (Phase 2)")
    
    def close(self):
        """Close connections"""
        self.graph_builder.close()

async def main():
    """Main function"""
    print("🚀 Fast Code Graph Generator - Phase 1")
    print("=" * 50)
    
    if not Path(REPO_PATH).exists():
        print(f"❌ Repository path does not exist: {REPO_PATH}")
        return
    
    generator = FastCodeGraphGenerator(REPO_PATH)
    
    try:
        await generator.generate_fast_graph()
        
        print("\n🎉 Phase 1 completed successfully!")
        print("🌐 View graph in Neo4j Browser: http://localhost:7474")
        print("🔮 Run LLM enhancement later with: python llm_enhancer.py")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Full error details:")
    finally:
        generator.close()

if __name__ == "__main__":
    asyncio.run(main())
