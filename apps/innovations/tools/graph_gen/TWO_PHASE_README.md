# Two-Phase Code Graph RAG Generator 🚀⚡

## Overview 🎯

A **fast, cost-effective** approach to building code knowledge graphs:

- **Phase 1**: Fast graph generation (1-2 minutes) using Tree-sitter + local embeddings
- **Phase 2**: Optional LLM enhancement for rich descriptions and insights

## Why Two Phases? 💡

### **Phase 1 Benefits:**
- ⚡ **10x faster** than LLM-only approach
- 💰 **Zero API costs** for basic graph
- 🎯 **Immediate results** - explore graph right away
- 📊 **Complete structure** - all functions, relationships, vectors

### **Phase 2 Benefits:**
- 🧠 **Rich descriptions** from LLM analysis
- 💼 **Business insights** and domain concepts
- 🔧 **Improvement suggestions** for code quality
- 🎨 **Architectural patterns** identification
- 💰 **Cost control** - enhance only what you need

## Quick Start 🚀

### **Phase 1: Fast Graph (Required)**
```bash
# Install dependencies
pip install -r requirements.txt

# Generate fast graph (1-2 minutes)
python fast_graph_generator.py
```

### **Phase 2: LLM Enhancement (Optional)**
```bash
# Enhance with LLM insights (costs API credits)
python llm_enhancer.py
```

## Architecture 🏗️

### **Phase 1: Fast Generation**
```
Code Files → Tree-sitter Parser → Local Analysis → Vector Generation → Neo4j Graph
    ↓              ↓                    ↓              ↓              ↓
  All langs    Precise AST      Complexity calc   Local embeddings  Complete graph
  (1-2 min)    (10x faster)     Rule-based        (No API calls)    Ready to use
```

### **Phase 2: LLM Enhancement**
```
Neo4j Graph → Query Functions → LLM Analysis → Update Nodes → Enhanced Graph
     ↓             ↓               ↓             ↓            ↓
  Existing      Select smart    Rich insights   Add metadata  Premium features
  functions     candidates      (API costs)     to nodes      Business value
```

## Node Schema 📊

### **Phase 1 Properties (Fast)**
```json
{
  "id": "unique_hash",
  "name": "function_name",
  "signature": "def func(param: str) -> int",
  "embedding": [0.1, 0.2, ...],  // Local embedding
  "complexity": 15,
  "loc": 45,
  "language": "python",
  "file_path": "src/module.py",
  "parameters": ["param: str"],
  "return_type": "int",
  "calls": ["other_func"],
  "created_at": "2024-01-01T00:00:00Z"
}
```

### **Phase 2 Properties (LLM Enhanced)**
```json
{
  // ... all Phase 1 properties plus:
  "llm_analyzed": true,
  "llm_description": "Processes user data and validates input",
  "llm_purpose": "Data validation and transformation",
  "llm_business_concepts": ["user_management", "data_validation"],
  "llm_improvement_suggestions": ["Add error handling", "Extract validation logic"],
  "llm_usage_examples": ["Called during user registration"],
  "llm_related_patterns": ["Strategy Pattern", "Validator Pattern"],
  "llm_confidence": 0.95,
  "llm_analyzed_at": "2024-01-01T00:00:00Z"
}
```

## Usage Examples 💻

### **1. Fast Development Workflow**
```bash
# Day 1: Get immediate insights
python fast_graph_generator.py
python enhanced_queries.py  # Explore structure

# Day 2: Enhance critical functions
python llm_enhancer.py  # Choose "complex_only" strategy
```

### **2. Cost-Conscious Approach**
```bash
# Generate full graph for free
python fast_graph_generator.py

# Enhance only 20 most complex functions
python llm_enhancer.py  # Enter max: 20
```

### **3. Iterative Enhancement**
```bash
# Phase 1: Complete graph
python fast_graph_generator.py

# Phase 2a: Enhance complex functions
python llm_enhancer.py  # Strategy: complex_only

# Phase 2b: Later, enhance important functions  
python llm_enhancer.py  # Strategy: important_only
```

## Query Examples 🔍

### **Phase 1 Queries (Always Available)**
```cypher
// Most complex functions
MATCH (f:LogicalFunction)
RETURN f.name, f.complexity, f.loc
ORDER BY f.complexity DESC LIMIT 10

// Function call patterns
MATCH (f1)-[:CALLS]->(f2)
RETURN f1.name, f2.name, f1.complexity + f2.complexity as total_complexity
ORDER BY total_complexity DESC
```

### **Phase 2 Queries (After LLM Enhancement)**
```cypher
// Business domain analysis
MATCH (f:LogicalFunction)
WHERE f.llm_analyzed = true
UNWIND f.llm_business_concepts as concept
RETURN concept, count(f) as usage_count

// Functions needing improvement
MATCH (f:LogicalFunction)
WHERE size(f.llm_improvement_suggestions) > 0
RETURN f.name, f.llm_improvement_suggestions, f.complexity
```

## Performance Comparison 📈

| Approach | Time | Cost | Features |
|----------|------|------|----------|
| **Old (LLM-only)** | 30-60 min | $10-50 | Rich descriptions |
| **Phase 1 (Fast)** | 1-2 min | $0 | Structure + vectors |
| **Phase 1+2 (Hybrid)** | 3-10 min | $2-10 | Best of both |

## Enhancement Strategies 🎯

### **1. Selective (Recommended)**
- Complex functions (complexity > 5)
- Functions without documentation
- Functions with many parameters
- **Cost**: Low-Medium

### **2. Complex Only**
- Only high complexity functions
- Focus on code quality issues
- **Cost**: Low

### **3. Important Only**
- Functions with key names (main, process, handle)
- Entry points and core logic
- **Cost**: Low

### **4. All Functions**
- Complete LLM analysis
- Maximum insights
- **Cost**: High

## Best Practices 🌟

### **For Speed:**
1. **Always start with Phase 1** - get immediate value
2. **Use Tree-sitter** - 10x faster than regex parsing
3. **Local embeddings** - no API calls for vectors
4. **Batch processing** - efficient Neo4j operations

### **For Cost Control:**
1. **Start selective** - enhance only what you need
2. **Use complexity threshold** - focus on complex code
3. **Batch LLM calls** - reduce API overhead
4. **Monitor usage** - track enhancement costs

### **For Quality:**
1. **Combine both phases** - structure + insights
2. **Iterative enhancement** - improve over time
3. **Use confidence scores** - trust high-confidence results
4. **Regular updates** - re-enhance changed code

## Migration from Old System 🔄

```bash
# 1. Backup existing graph
python cleanup_graph.py  # Export if needed

# 2. Generate new fast graph
python fast_graph_generator.py

# 3. Selectively enhance
python llm_enhancer.py  # Choose strategy

# 4. Compare results
python enhanced_queries.py
```

## Next Steps 🎯

1. **Try Phase 1**: `python fast_graph_generator.py`
2. **Explore graph**: Use Neo4j Browser or `python enhanced_queries.py`
3. **Enhance selectively**: `python llm_enhancer.py`
4. **Iterate**: Add more enhancements over time

**Result: Fast, cost-effective, high-quality code knowledge graph! 🎉**
