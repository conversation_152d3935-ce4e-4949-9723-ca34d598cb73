#!/usr/bin/env python3
"""
Simple Parser Test - No Dependencies

Test the parsing logic without external dependencies.
"""

import os
import re
import hashlib
from pathlib import Path
from dataclasses import dataclass
from typing import List, Optional

# Simple data class for testing
@dataclass
class SimpleFunction:
    name: str
    signature: str
    file_path: str
    start_line: int
    end_line: int
    language: str
    complexity: int

def parse_js_ts_file(file_path: Path, content: str) -> List[SimpleFunction]:
    """Parse JavaScript/TypeScript file using regex patterns"""
    functions = []
    lines = content.split('\n')
    
    # Patterns for different function types
    patterns = [
        # Regular functions: function name() {}
        r'^\s*(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\([^)]*\)',
        # Arrow functions: const name = () => {}
        r'^\s*(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s+)?\([^)]*\)\s*=>\s*{',
        # Method definitions: methodName() {}
        r'^\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*{',
        # Class methods: public/private methodName() {}
        r'^\s*(?:public|private|protected)?\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*{',
    ]
    
    current_class = None
    brace_count = 0
    
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        
        # Track class context
        class_match = re.search(r'^\s*(?:export\s+)?(?:abstract\s+)?class\s+(\w+)', line)
        if class_match:
            current_class = class_match.group(1)
            brace_count = 0
        
        # Count braces to track when we exit class
        brace_count += line.count('{') - line.count('}')
        if current_class and brace_count <= 0 and i > 0:
            current_class = None
        
        # Try to match function patterns
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                func_name = match.group(1)
                
                # Skip common non-function matches
                if func_name in ['if', 'for', 'while', 'switch', 'catch', 'try']:
                    continue
                
                # Find end of function
                end_line = find_function_end(lines, i)
                
                # Create function name with class if applicable
                full_name = f"{current_class}.{func_name}" if current_class else func_name
                
                # Calculate simple complexity
                func_lines = lines[i:end_line + 1]
                complexity = calculate_complexity(func_lines)
                
                func = SimpleFunction(
                    name=full_name,
                    signature=line.strip(),
                    file_path=str(file_path),
                    start_line=i + 1,
                    end_line=end_line + 1,
                    language=file_path.suffix[1:],
                    complexity=complexity
                )
                
                functions.append(func)
                print(f"✅ Found function: {full_name} (lines {i+1}-{end_line+1})")
                break
    
    return functions

def find_function_end(lines: List[str], start_line: int) -> int:
    """Find the end line of a function by counting braces"""
    brace_count = 0
    started_counting = False
    
    for i in range(start_line, min(start_line + 100, len(lines))):
        line = lines[i]
        
        if '{' in line:
            started_counting = True
        
        if started_counting:
            brace_count += line.count('{') - line.count('}')
            
            if brace_count <= 0:
                return i
    
    return min(start_line + 20, len(lines) - 1)

def calculate_complexity(lines: List[str]) -> int:
    """Calculate simple cyclomatic complexity"""
    complexity = 1
    
    for line in lines:
        line = line.strip()
        complexity += line.count('if ')
        complexity += line.count('else if')
        complexity += line.count('while ')
        complexity += line.count('for ')
        complexity += line.count('case ')
        complexity += line.count('&&')
        complexity += line.count('||')
        complexity += line.count('?')
    
    return complexity

def test_parsing():
    """Test the parsing on actual files"""
    repo_path = Path("/Users/<USER>/IdeaProjects/cymulate-bas-platform")
    
    if not repo_path.exists():
        print(f"❌ Repository path not found: {repo_path}")
        return
    
    print(f"🔍 Testing parser on: {repo_path}")
    
    # Find some TypeScript files to test
    ts_files = list(repo_path.rglob("*.ts"))[:5]  # Test first 5 files
    
    total_functions = 0
    
    for file_path in ts_files:
        print(f"\n📄 Testing file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if not content.strip():
                print("   ⚠️  Empty file")
                continue
            
            functions = parse_js_ts_file(file_path, content)
            
            print(f"   📊 Found {len(functions)} functions")
            total_functions += len(functions)
            
            # Show first few functions
            for func in functions[:3]:
                print(f"      • {func.name} (complexity: {func.complexity})")
            
            if len(functions) > 3:
                print(f"      ... and {len(functions) - 3} more")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎯 Total functions found: {total_functions}")
    
    if total_functions > 0:
        print("✅ Parser is working! Functions are being detected.")
    else:
        print("❌ No functions found. Parser may need adjustment.")

def main():
    """Main test function"""
    print("🧪 Simple Parser Test")
    print("=" * 40)
    
    test_parsing()

if __name__ == "__main__":
    main()
