#!/usr/bin/env python3
"""
Test Neo4j Connection

Quick script to test and configure Neo4j connection.
"""

import os
from neo4j import GraphDatabase

def test_connection():
    """Test different Neo4j connection configurations"""
    
    # Common configurations to try
    configs = [
        {
            "uri": "bolt://localhost:7687",
            "user": "neo4j", 
            "password": "password"
        },
        {
            "uri": "bolt://localhost:7687",
            "user": "neo4j",
            "password": "neo4j"
        },
        {
            "uri": "bolt://localhost:7687", 
            "user": "neo4j",
            "password": ""
        },
        {
            "uri": "neo4j://localhost:7687",
            "user": "neo4j",
            "password": "password"
        }
    ]
    
    print("🔍 Testing Neo4j connections...")
    
    for i, config in enumerate(configs, 1):
        print(f"\n{i}. Testing: {config['uri']} with user '{config['user']}'")
        
        try:
            driver = GraphDatabase.driver(
                config["uri"],
                auth=(config["user"], config["password"])
            )
            
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                
            driver.close()
            
            print(f"   ✅ SUCCESS! Connection works")
            print(f"   📝 Use these settings:")
            print(f"      NEO4J_URI={config['uri']}")
            print(f"      NEO4J_USER={config['user']}")
            print(f"      NEO4J_PASSWORD={config['password']}")
            return config
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    print(f"\n❌ All connection attempts failed!")
    print(f"💡 Please check:")
    print(f"   1. Neo4j is running")
    print(f"   2. Check Neo4j Desktop or browser at http://localhost:7474")
    print(f"   3. Verify username/password")
    
    return None

def create_env_file(config):
    """Create .env file with working configuration"""
    env_content = f"""# Neo4j Configuration (Working)
NEO4J_URI={config['uri']}
NEO4J_USER={config['user']}
NEO4J_PASSWORD={config['password']}

# Repository Path
REPO_PATH=/Users/<USER>/IdeaProjects/cymulate-bas-platform
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"\n✅ Created .env file with working configuration")

def main():
    """Main function"""
    print("🔧 Neo4j Connection Tester")
    print("=" * 30)
    
    config = test_connection()
    
    if config:
        create_env_file(config)
        print(f"\n🚀 Ready to run: python fast_neo4j_generator.py")
    else:
        print(f"\n🛠️  Please start Neo4j and try again")
        print(f"   • Neo4j Desktop: Start your database")
        print(f"   • Neo4j Browser: http://localhost:7474")

if __name__ == "__main__":
    main()
