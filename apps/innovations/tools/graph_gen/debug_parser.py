#!/usr/bin/env python3
"""
Debug Parser - Test file discovery and parsing

Quick script to debug what files are being found and parsed.
"""

import os
import ast
from pathlib import Path

# Default path - change this to your actual repo path
REPO_PATH = "/Users/<USER>/IdeaProjects/cymulate-bas-platform"

def debug_file_discovery():
    """Debug file discovery"""
    print(f"🔍 Debugging file discovery in: {REPO_PATH}")
    
    repo_path = Path(REPO_PATH)
    
    if not repo_path.exists():
        print(f"❌ Path does not exist: {REPO_PATH}")
        return
    
    print(f"✅ Path exists: {repo_path}")
    print(f"📁 Is directory: {repo_path.is_dir()}")
    
    # Test basic directory listing
    try:
        items = list(repo_path.iterdir())
        print(f"📂 Directory contains {len(items)} items")
        
        # Show first few items
        for i, item in enumerate(items[:10]):
            print(f"   {i+1}. {'📁' if item.is_dir() else '📄'} {item.name}")
        
        if len(items) > 10:
            print(f"   ... and {len(items) - 10} more items")
            
    except Exception as e:
        print(f"❌ Error listing directory: {e}")
        return
    
    # Test Python file discovery
    print(f"\n🐍 Looking for Python files...")
    
    supported_extensions = {
        '.py': 'python',
        '.js': 'javascript', 
        '.ts': 'typescript',
        '.jsx': 'javascript',
        '.tsx': 'typescript',
        '.java': 'java',
        '.go': 'go',
        '.rs': 'rust'
    }
    
    ignore_dirs = {
        'node_modules', '.git', '__pycache__', 'dist', 'build', 
        '.venv', 'venv', '.env', 'target', 'bin', 'obj', '.idea'
    }
    
    for ext, lang in supported_extensions.items():
        print(f"\n🔍 Searching for *{ext} files...")
        
        try:
            # Use rglob for recursive search
            files = list(repo_path.rglob(f"*{ext}"))
            print(f"   Found {len(files)} {ext} files (before filtering)")
            
            # Filter out ignored directories
            filtered_files = []
            for file_path in files:
                if any(ignore_dir in file_path.parts for ignore_dir in ignore_dirs):
                    continue
                if file_path.name.startswith('.'):
                    continue
                if file_path.is_file() and file_path.stat().st_size > 0:
                    filtered_files.append(file_path)
            
            print(f"   ✅ {len(filtered_files)} {ext} files after filtering")
            
            # Show some examples
            for i, file_path in enumerate(filtered_files[:5]):
                print(f"      {i+1}. {file_path}")
            
            if len(filtered_files) > 5:
                print(f"      ... and {len(filtered_files) - 5} more")
                
            # Test parsing one Python file
            if ext == '.py' and filtered_files:
                test_file = filtered_files[0]
                print(f"\n🧪 Testing Python parsing on: {test_file}")
                test_python_parsing(test_file)
                
        except Exception as e:
            print(f"❌ Error searching for {ext} files: {e}")

def test_python_parsing(file_path: Path):
    """Test parsing a single Python file"""
    try:
        print(f"📄 Reading file: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        print(f"📊 File size: {len(content)} characters")
        print(f"📊 File lines: {len(content.splitlines())} lines")
        
        if not content.strip():
            print("⚠️  File is empty")
            return
        
        # Try to parse with AST
        print("🔧 Parsing with AST...")
        tree = ast.parse(content, filename=str(file_path))
        
        # Count functions
        functions = []
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                functions.append(node.name)
            elif isinstance(node, ast.ClassDef):
                classes.append(node.name)
                # Count methods in class
                for class_node in node.body:
                    if isinstance(class_node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        functions.append(f"{node.name}.{class_node.name}")
        
        print(f"✅ Found {len(functions)} functions and {len(classes)} classes")
        
        if functions:
            print("🔧 Functions found:")
            for i, func_name in enumerate(functions[:10]):
                print(f"   {i+1}. {func_name}")
            if len(functions) > 10:
                print(f"   ... and {len(functions) - 10} more")
        
        if classes:
            print("📦 Classes found:")
            for i, class_name in enumerate(classes[:5]):
                print(f"   {i+1}. {class_name}")
            if len(classes) > 5:
                print(f"   ... and {len(classes) - 5} more")
                
    except SyntaxError as e:
        print(f"⚠️  Syntax error in file: {e}")
    except Exception as e:
        print(f"❌ Error parsing file: {e}")

def main():
    """Main debug function"""
    print("🐛 Debug Parser - File Discovery Test")
    print("=" * 50)
    
    debug_file_discovery()
    
    print("\n" + "=" * 50)
    print("🎯 Debug completed!")
    print("\nIf you see Python files but 0 functions, the issue might be:")
    print("1. Files have syntax errors")
    print("2. Files are empty or only contain imports")
    print("3. Functions are not at the top level")
    print("4. Encoding issues")

if __name__ == "__main__":
    main()
