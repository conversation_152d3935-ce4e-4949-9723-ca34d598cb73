#!/usr/bin/env python3
"""
Quick Start Script for Code Graph RAG Generator

"""

import os
import sys
from pathlib import Path

def setup_environment():
    """בודק ומגדיר את הסביבה"""
    print("🔧 Setting up environment...")
    
    # Check if .env exists
    if not Path('.env').exists():
        if Path('.env.example').exists():
            print("📝 Creating .env file from example...")
            import shutil
            shutil.copy('.env.example', '.env')
            print("⚠️  Please edit .env file with your actual values!")
            return False
        else:
            print("❌ No .env.example file found")
            return False
    
    # Check dependencies
    try:
        import openai
        import neo4j
        import dotenv
        print("✅ All dependencies installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Run: pip install -r requirements.txt")
        return False

def check_connections():
    """בודק חיבורים ל-OpenAI ו-Neo4j"""
    from dotenv import load_dotenv
    load_dotenv()
    
    print("🔗 Checking connections...")
    
    # Check OpenAI
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key or openai_key == "your-openai-api-key-here":
        print("❌ OpenAI API key not configured")
        return False
    
    try:
        from openai import OpenAI
        client = OpenAI(api_key=openai_key)
        client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "test"}],
            max_tokens=1
        )
        print("✅ OpenAI connection successful")
    except Exception as e:
        print(f"❌ OpenAI connection failed: {e}")
        return False
    
    # Check Neo4j
    try:
        from neo4j import GraphDatabase
        neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        neo4j_user = os.getenv("NEO4J_USER", "neo4j")
        neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
        
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        with driver.session() as session:
            session.run("RETURN 1")
        driver.close()
        print("✅ Neo4j connection successful")
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        print("💡 Make sure Neo4j is running: docker run -p 7474:7474 -p 7687:7687 -e NEO4J_AUTH=neo4j/password neo4j:latest")
        return False
    
    return True

def get_repo_path():
    """מקבל נתיב לתיקיית הקוד"""
    from dotenv import load_dotenv
    load_dotenv()
    
    repo_path = os.getenv("REPO_PATH")
    
    if not repo_path or repo_path == "/path/to/your/code":
        print("\n📁 Repository path not configured in .env")
        repo_path = input("Enter path to your code repository: ").strip()
        
        if not repo_path:
            print("❌ No path provided")
            return None
    
    if not Path(repo_path).exists():
        print(f"❌ Path does not exist: {repo_path}")
        return None
    
    print(f"✅ Using repository: {repo_path}")
    return repo_path

def run_generator(repo_path):
    """מריץ את הגנרטור"""
    print("\n🚀 Starting Code Graph RAG Generator...")
    
    # Import and run the main generator
    sys.path.append('../../../../arik-test')
    from generate_graph import CodeGraphGenerator
    
    generator = CodeGraphGenerator(repo_path)
    
    try:
        # Ask for function limit
        print("\n🔢 How many functions would you like to process?")
        print("   • Enter a number (e.g., 50) for testing")
        print("   • Press Enter to process all functions")
        
        max_funcs = input("Max functions: ").strip()
        max_functions = int(max_funcs) if max_funcs.isdigit() else None
        
        if max_functions:
            print(f"📊 Processing first {max_functions} functions")
        else:
            print("📊 Processing all functions")
        
        generator.generate_graph(max_functions)
        
        print("\n🎉 Success! Your code graph is ready!")
        print("🌐 Open Neo4j Browser: http://localhost:7474")
        print("💡 Run example queries: python example_queries.py")
        
    except KeyboardInterrupt:
        print("\n⏹️ Process interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        generator.close()

def main():
    """הפונקציה הראשית"""
    print("🚀 Code Graph RAG - Quick Start")
    print("=" * 40)
    
    # Step 1: Setup environment
    if not setup_environment():
        return
    
    # Step 2: Check connections
    if not check_connections():
        return
    
    # Step 3: Get repository path
    repo_path = get_repo_path()
    if not repo_path:
        return
    
    # Step 4: Run generator
    run_generator(repo_path)

if __name__ == "__main__":
    main()
