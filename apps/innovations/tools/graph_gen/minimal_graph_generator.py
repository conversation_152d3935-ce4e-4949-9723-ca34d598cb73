#!/usr/bin/env python3
"""
Minimal Graph Generator - No Heavy Dependencies

Fast code graph generation without sentence-transformers.
Uses basic embeddings and focuses on structure.
"""

import os
import re
import ast
import hashlib
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
REPO_PATH = os.getenv("REPO_PATH", "/Users/<USER>/IdeaProjects/cymulate-bas-platform")

@dataclass
class MinimalFunction:
    """Minimal function representation"""
    id: str
    name: str
    signature: str
    file_path: str
    start_line: int
    end_line: int
    language: str
    complexity: int
    loc: int
    parameters: List[str]
    calls: List[str]
    is_async: bool

class MinimalParser:
    """Minimal parser for multiple languages"""
    
    def __init__(self):
        self.supported_extensions = {
            '.py': 'python',
            '.js': 'javascript', 
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.go': 'go'
        }
    
    async def parse_repository(self, repo_path: str) -> List[MinimalFunction]:
        """Parse repository and extract functions"""
        logger.info(f"🚀 Starting minimal parsing of: {repo_path}")
        
        functions = []
        code_files = self._get_code_files(repo_path)
        
        logger.info(f"📁 Found {len(code_files)} code files")
        
        # Process files in batches
        batch_size = 50
        for i in range(0, len(code_files), batch_size):
            batch = code_files[i:i + batch_size]
            
            logger.info(f"📊 Processing batch {i//batch_size + 1}/{(len(code_files)-1)//batch_size + 1}")
            
            for file_path in batch:
                try:
                    file_functions = await self._parse_file(file_path)
                    functions.extend(file_functions)
                except Exception as e:
                    logger.error(f"Error parsing {file_path}: {e}")
        
        logger.info(f"✅ Parsed {len(functions)} functions from {len(code_files)} files")
        return functions
    
    def _get_code_files(self, repo_path: str) -> List[Path]:
        """Get all code files recursively"""
        repo_path = Path(repo_path)
        code_files = []
        
        ignore_dirs = {
            'node_modules', '.git', '__pycache__', 'dist', 'build', 
            '.venv', 'venv', 'target', 'bin', 'obj', '.idea', 'coverage'
        }
        
        for ext in self.supported_extensions.keys():
            try:
                files = list(repo_path.rglob(f"*{ext}"))
                
                for file_path in files:
                    if any(ignore_dir in file_path.parts for ignore_dir in ignore_dirs):
                        continue
                    if file_path.name.startswith('.'):
                        continue
                    if file_path.is_file() and file_path.stat().st_size > 0:
                        code_files.append(file_path)
                        
            except Exception as e:
                logger.error(f"Error scanning for {ext} files: {e}")
        
        return code_files
    
    async def _parse_file(self, file_path: Path) -> List[MinimalFunction]:
        """Parse a single file"""
        extension = file_path.suffix.lower()
        language = self.supported_extensions.get(extension, 'unknown')
        
        if language == 'unknown':
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if not content.strip():
                return []
            
            if language == 'python':
                return await self._parse_python(file_path, content)
            elif language in ['javascript', 'typescript']:
                return await self._parse_js_ts(file_path, content)
            else:
                return await self._parse_generic(file_path, content, language)
                
        except Exception as e:
            logger.error(f"Error reading {file_path}: {e}")
            return []
    
    async def _parse_python(self, file_path: Path, content: str) -> List[MinimalFunction]:
        """Parse Python file"""
        import ast
        
        functions = []
        try:
            tree = ast.parse(content, filename=str(file_path))
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    func = self._extract_python_function(file_path, content, node)
                    if func:
                        functions.append(func)
                elif isinstance(node, ast.ClassDef):
                    for class_node in node.body:
                        if isinstance(class_node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                            func = self._extract_python_function(
                                file_path, content, class_node, class_name=node.name
                            )
                            if func:
                                functions.append(func)
        except:
            pass  # Skip files with syntax errors
        
        return functions
    
    def _extract_python_function(self, file_path: Path, content: str, 
                                node: ast.FunctionDef, class_name: Optional[str] = None) -> Optional[MinimalFunction]:
        """Extract Python function details"""
        try:
            lines = content.split('\n')
            start_line = node.lineno
            end_line = getattr(node, 'end_lineno', start_line + 10)
            end_line = min(end_line, len(lines))
            
            func_name = f"{class_name}.{node.name}" if class_name else node.name
            func_code = '\n'.join(lines[start_line-1:end_line])
            
            # Extract parameters
            parameters = []
            if hasattr(node.args, 'args'):
                parameters = [arg.arg for arg in node.args.args]
            
            # Extract calls
            calls = []
            for child in ast.walk(node):
                if isinstance(child, ast.Call):
                    if isinstance(child.func, ast.Name):
                        calls.append(child.func.id)
                    elif isinstance(child.func, ast.Attribute):
                        calls.append(child.func.attr)
            
            # Calculate complexity
            complexity = self._calculate_complexity(func_code)
            
            return MinimalFunction(
                id=f"{file_path.stem}_{func_name.replace('.', '_')}_{start_line}",
                name=func_name,
                signature=f"def {node.name}({', '.join(parameters)})",
                file_path=str(file_path),
                start_line=start_line,
                end_line=end_line,
                language='python',
                complexity=complexity,
                loc=end_line - start_line + 1,
                parameters=parameters,
                calls=list(set(calls)),
                is_async=isinstance(node, ast.AsyncFunctionDef)
            )
        except:
            return None
    
    async def _parse_js_ts(self, file_path: Path, content: str) -> List[MinimalFunction]:
        """Parse JavaScript/TypeScript file"""
        functions = []
        lines = content.split('\n')
        
        patterns = [
            r'^\s*(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\([^)]*\)',
            r'^\s*(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s+)?\([^)]*\)\s*=>\s*{',
            r'^\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*{',
            r'^\s*(?:public|private|protected)?\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*{',
        ]
        
        current_class = None
        
        for i, line in enumerate(lines):
            # Track class context
            class_match = re.search(r'^\s*(?:export\s+)?(?:abstract\s+)?class\s+(\w+)', line)
            if class_match:
                current_class = class_match.group(1)
            
            # Try to match function patterns
            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    func_name = match.group(1)
                    
                    if func_name in ['if', 'for', 'while', 'switch', 'catch', 'try']:
                        continue
                    
                    func = self._extract_js_function(file_path, lines, i, func_name, current_class, line)
                    if func:
                        functions.append(func)
                    break
        
        return functions
    
    def _extract_js_function(self, file_path: Path, lines: List[str], start_line: int,
                           func_name: str, class_name: Optional[str], signature_line: str) -> Optional[MinimalFunction]:
        """Extract JS/TS function details"""
        try:
            # Find function end
            end_line = self._find_function_end(lines, start_line)
            
            full_name = f"{class_name}.{func_name}" if class_name else func_name
            func_lines = lines[start_line:end_line + 1]
            func_code = '\n'.join(func_lines)
            
            # Extract parameters
            parameters = []
            param_match = re.search(r'\(([^)]*)\)', signature_line)
            if param_match:
                param_str = param_match.group(1).strip()
                if param_str:
                    parameters = [p.strip().split(':')[0].strip() for p in param_str.split(',')]
                    parameters = [p for p in parameters if p]
            
            # Extract calls
            calls = []
            for line in func_lines:
                call_matches = re.findall(r'(\w+)\s*\(', line)
                calls.extend(call_matches)
            
            complexity = self._calculate_complexity(func_code)
            
            return MinimalFunction(
                id=f"{file_path.stem}_{full_name.replace('.', '_')}_{start_line}",
                name=full_name,
                signature=signature_line.strip(),
                file_path=str(file_path),
                start_line=start_line + 1,
                end_line=end_line + 1,
                language=file_path.suffix[1:],
                complexity=complexity,
                loc=end_line - start_line + 1,
                parameters=parameters,
                calls=list(set(calls)),
                is_async='async' in signature_line
            )
        except:
            return None
    
    def _find_function_end(self, lines: List[str], start_line: int) -> int:
        """Find function end by counting braces"""
        brace_count = 0
        started = False
        
        for i in range(start_line, min(start_line + 100, len(lines))):
            line = lines[i]
            if '{' in line:
                started = True
            if started:
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0:
                    return i
        
        return min(start_line + 20, len(lines) - 1)
    
    async def _parse_generic(self, file_path: Path, content: str, language: str) -> List[MinimalFunction]:
        """Parse other languages (basic patterns)"""
        # Placeholder for other languages
        return []
    
    def _calculate_complexity(self, code: str) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1
        complexity += code.count('if ')
        complexity += code.count('else if')
        complexity += code.count('while ')
        complexity += code.count('for ')
        complexity += code.count('case ')
        complexity += code.count('&&')
        complexity += code.count('||')
        complexity += code.count('?')
        return complexity

async def main():
    """Main function"""
    print("🚀 Minimal Code Graph Generator")
    print("=" * 40)
    
    if not Path(REPO_PATH).exists():
        print(f"❌ Repository path not found: {REPO_PATH}")
        return
    
    parser = MinimalParser()
    
    start_time = datetime.now()
    
    try:
        functions = await parser.parse_repository(REPO_PATH)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n✅ Parsing completed in {duration:.2f} seconds!")
        print(f"📊 Found {len(functions)} functions")
        
        # Show statistics
        if functions:
            languages = {}
            complexities = []
            
            for func in functions:
                languages[func.language] = languages.get(func.language, 0) + 1
                complexities.append(func.complexity)
            
            print(f"\n📈 Statistics:")
            print(f"   • Languages: {dict(languages)}")
            print(f"   • Avg complexity: {sum(complexities) / len(complexities):.1f}")
            print(f"   • Max complexity: {max(complexities)}")
            
            print(f"\n🔝 Most complex functions:")
            sorted_funcs = sorted(functions, key=lambda f: f.complexity, reverse=True)
            for i, func in enumerate(sorted_funcs[:5], 1):
                print(f"   {i}. {func.name} (complexity: {func.complexity})")
        
        print(f"\n🎯 Ready for Neo4j integration!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Full error details:")

if __name__ == "__main__":
    asyncio.run(main())
