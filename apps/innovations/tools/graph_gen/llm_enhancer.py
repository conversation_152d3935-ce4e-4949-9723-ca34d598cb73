#!/usr/bin/env python3
"""
LLM Enhancer - Phase 2

Enhances existing graph with LLM-generated descriptions and insights.
Runs after fast graph generation is complete.
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from openai import OpenAI
from neo4j import GraphDatabase
from dotenv import load_dotenv

load_dotenv()

# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o")
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMEnhancer:
    """Enhances graph nodes with LLM analysis"""
    
    def __init__(self):
        self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
        self.driver = GraphDatabase.driver(
            NEO4J_URI, 
            auth=(NEO4J_USER, NEO4J_PASSWORD)
        )
    
    async def enhance_graph(self, 
                          strategy: str = "selective",
                          max_functions: Optional[int] = None,
                          complexity_threshold: int = 5) -> None:
        """
        Enhance graph with LLM analysis
        
        Args:
            strategy: "all", "selective", "complex_only", "important_only"
            max_functions: Maximum number of functions to enhance
            complexity_threshold: Minimum complexity for enhancement
        """
        logger.info(f"🔮 Starting LLM enhancement with strategy: {strategy}")
        
        # Get functions to enhance
        functions = await self._get_functions_for_enhancement(
            strategy, max_functions, complexity_threshold
        )
        
        if not functions:
            logger.warning("No functions found for enhancement")
            return
        
        logger.info(f"📊 Enhancing {len(functions)} functions")
        
        # Process in batches to manage API costs
        batch_size = 5  # Small batches for cost control
        enhanced_count = 0
        
        for i in range(0, len(functions), batch_size):
            batch = functions[i:i + batch_size]
            
            logger.info(f"🧠 Processing batch {i//batch_size + 1}/{(len(functions)-1)//batch_size + 1}")
            
            # Process batch with rate limiting
            enhanced_batch = await self._enhance_function_batch(batch)
            
            # Update graph
            await self._update_graph_batch(enhanced_batch)
            
            enhanced_count += len(enhanced_batch)
            
            # Rate limiting - wait between batches
            if i + batch_size < len(functions):
                await asyncio.sleep(2)  # 2 second delay between batches
        
        logger.info(f"✅ Enhanced {enhanced_count} functions with LLM analysis")
    
    async def _get_functions_for_enhancement(self, 
                                           strategy: str,
                                           max_functions: Optional[int],
                                           complexity_threshold: int) -> List[Dict]:
        """Get functions that need LLM enhancement"""
        
        with self.driver.session() as session:
            if strategy == "all":
                query = """
                MATCH (f:LogicalFunction)
                WHERE f.llm_analyzed = false
                RETURN f.id, f.name, f.signature, f.code_hash, f.complexity, 
                       f.file_path, f.start_line, f.end_line, f.docstring
                ORDER BY f.complexity DESC
                """
            elif strategy == "selective":
                query = f"""
                MATCH (f:LogicalFunction)
                WHERE f.llm_analyzed = false 
                AND (f.complexity >= {complexity_threshold} 
                     OR f.docstring IS NULL 
                     OR size(f.parameters) > 3)
                RETURN f.id, f.name, f.signature, f.code_hash, f.complexity,
                       f.file_path, f.start_line, f.end_line, f.docstring
                ORDER BY f.complexity DESC
                """
            elif strategy == "complex_only":
                query = f"""
                MATCH (f:LogicalFunction)
                WHERE f.llm_analyzed = false AND f.complexity >= {complexity_threshold * 2}
                RETURN f.id, f.name, f.signature, f.code_hash, f.complexity,
                       f.file_path, f.start_line, f.end_line, f.docstring
                ORDER BY f.complexity DESC
                """
            elif strategy == "important_only":
                query = """
                MATCH (f:LogicalFunction)
                WHERE f.llm_analyzed = false
                AND (f.name CONTAINS 'main' OR f.name CONTAINS 'process' 
                     OR f.name CONTAINS 'handle' OR f.name CONTAINS 'execute')
                RETURN f.id, f.name, f.signature, f.code_hash, f.complexity,
                       f.file_path, f.start_line, f.end_line, f.docstring
                ORDER BY f.complexity DESC
                """
            else:
                raise ValueError(f"Unknown strategy: {strategy}")
            
            if max_functions:
                query += f" LIMIT {max_functions}"
            
            result = session.run(query)
            return [dict(record) for record in result]
    
    async def _enhance_function_batch(self, functions: List[Dict]) -> List[Dict]:
        """Enhance a batch of functions with LLM"""
        enhanced_functions = []
        
        for func in functions:
            try:
                # Get the actual code for this function
                code = await self._get_function_code(func)
                
                if not code:
                    logger.warning(f"Could not get code for function: {func['f.name']}")
                    continue
                
                # Analyze with LLM
                analysis = await self._analyze_function_with_llm(func, code)
                
                if analysis:
                    enhanced_func = {**func, **analysis}
                    enhanced_functions.append(enhanced_func)
                    logger.info(f"✅ Enhanced: {func['f.name']}")
                else:
                    logger.warning(f"❌ Failed to enhance: {func['f.name']}")
                
                # Small delay between API calls
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error enhancing {func['f.name']}: {e}")
        
        return enhanced_functions
    
    async def _get_function_code(self, func: Dict) -> Optional[str]:
        """Get the actual code for a function"""
        try:
            file_path = func['f.file_path']
            start_line = func['f.start_line']
            end_line = func['f.end_line']
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                code = ''.join(lines[start_line-1:end_line])
                return code
                
        except Exception as e:
            logger.error(f"Error reading code from {file_path}: {e}")
            return None
    
    async def _analyze_function_with_llm(self, func: Dict, code: str) -> Optional[Dict]:
        """Analyze function with LLM"""
        try:
            prompt = self._create_enhancement_prompt(func, code)
            
            response = self.openai_client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": "You are a senior software architect analyzing code for documentation and insights."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=800
            )
            
            content = response.choices[0].message.content.strip()
            
            # Parse JSON response
            if content.startswith('```json'):
                content = content[7:]
            if content.endswith('```'):
                content = content[:-3]
            
            analysis = json.loads(content.strip())
            
            # Add metadata
            analysis['llm_analyzed_at'] = datetime.now().isoformat()
            analysis['llm_model_used'] = OPENAI_MODEL
            analysis['llm_analyzed'] = True
            
            return analysis
            
        except Exception as e:
            logger.error(f"LLM analysis failed for {func['f.name']}: {e}")
            return None
    
    def _create_enhancement_prompt(self, func: Dict, code: str) -> str:
        """Create prompt for LLM enhancement"""
        
        return f"""
Analyze this {func.get('f.language', 'unknown')} function and provide insights in JSON format.

Function: {func['f.name']}
Complexity: {func['f.complexity']}
File: {func['f.file_path']}

Code:
```
{code}
```

Return ONLY a JSON object with this structure:
{{
  "llm_description": "Clear, concise description of what this function does",
  "llm_purpose": "The main purpose/goal of this function",
  "llm_business_concepts": ["list", "of", "business", "domain", "concepts"],
  "llm_complexity_explanation": "Why this function is complex (if it is)",
  "llm_improvement_suggestions": ["suggestion1", "suggestion2"],
  "llm_usage_examples": ["example usage context"],
  "llm_related_patterns": ["design patterns or architectural patterns used"],
  "llm_confidence": 0.95
}}

Focus on:
1. Business value and purpose
2. Code quality and maintainability
3. Architectural patterns
4. Potential improvements
5. Usage context

Be concise but insightful.
"""
    
    async def _update_graph_batch(self, enhanced_functions: List[Dict]) -> None:
        """Update graph with enhanced data"""
        
        with self.driver.session() as session:
            for func in enhanced_functions:
                try:
                    session.run("""
                        MATCH (f:LogicalFunction {id: $id})
                        SET f.llm_analyzed = $llm_analyzed,
                            f.llm_description = $llm_description,
                            f.llm_purpose = $llm_purpose,
                            f.llm_business_concepts = $llm_business_concepts,
                            f.llm_complexity_explanation = $llm_complexity_explanation,
                            f.llm_improvement_suggestions = $llm_improvement_suggestions,
                            f.llm_usage_examples = $llm_usage_examples,
                            f.llm_related_patterns = $llm_related_patterns,
                            f.llm_analyzed_at = $llm_analyzed_at,
                            f.llm_model_used = $llm_model_used,
                            f.llm_confidence = $llm_confidence
                    """, {
                        'id': func['f.id'],
                        'llm_analyzed': func.get('llm_analyzed', True),
                        'llm_description': func.get('llm_description'),
                        'llm_purpose': func.get('llm_purpose'),
                        'llm_business_concepts': func.get('llm_business_concepts', []),
                        'llm_complexity_explanation': func.get('llm_complexity_explanation'),
                        'llm_improvement_suggestions': func.get('llm_improvement_suggestions', []),
                        'llm_usage_examples': func.get('llm_usage_examples', []),
                        'llm_related_patterns': func.get('llm_related_patterns', []),
                        'llm_analyzed_at': func.get('llm_analyzed_at'),
                        'llm_model_used': func.get('llm_model_used'),
                        'llm_confidence': func.get('llm_confidence')
                    })
                    
                except Exception as e:
                    logger.error(f"Error updating function {func['f.name']}: {e}")
    
    async def get_enhancement_stats(self) -> Dict[str, Any]:
        """Get statistics about LLM enhancement"""
        
        with self.driver.session() as session:
            result = session.run("""
                MATCH (f:LogicalFunction)
                RETURN 
                    count(f) as total_functions,
                    count(CASE WHEN f.llm_analyzed = true THEN 1 END) as enhanced_functions,
                    count(CASE WHEN f.llm_analyzed = false THEN 1 END) as pending_functions,
                    avg(CASE WHEN f.llm_confidence IS NOT NULL THEN f.llm_confidence END) as avg_confidence
            """)
            
            stats = dict(result.single())
            
            # Get enhancement by complexity
            result = session.run("""
                MATCH (f:LogicalFunction)
                WHERE f.llm_analyzed = true
                RETURN f.complexity as complexity, count(f) as count
                ORDER BY complexity
            """)
            
            complexity_dist = [dict(record) for record in result]
            stats['complexity_distribution'] = complexity_dist
            
            return stats
    
    def close(self):
        """Close connections"""
        self.driver.close()

async def main():
    """Main function"""
    print("🔮 LLM Enhancer - Phase 2")
    print("=" * 40)
    
    if not OPENAI_API_KEY or OPENAI_API_KEY == "your-openai-key":
        print("❌ Please set OPENAI_API_KEY in .env file")
        return
    
    enhancer = LLMEnhancer()
    
    try:
        # Show current stats
        stats = await enhancer.get_enhancement_stats()
        print(f"📊 Current Status:")
        print(f"   • Total functions: {stats['total_functions']}")
        print(f"   • Enhanced: {stats['enhanced_functions']}")
        print(f"   • Pending: {stats['pending_functions']}")
        
        if stats['pending_functions'] == 0:
            print("✅ All functions already enhanced!")
            return
        
        # Ask user for enhancement strategy
        print("\n🎯 Enhancement Strategies:")
        print("1. selective - Complex functions and those missing docs")
        print("2. all - All functions (expensive!)")
        print("3. complex_only - Only high complexity functions")
        print("4. important_only - Functions with important names")
        
        choice = input("\nChoose strategy (1-4) or press Enter for selective: ").strip()
        
        strategy_map = {
            '1': 'selective',
            '2': 'all', 
            '3': 'complex_only',
            '4': 'important_only'
        }
        
        strategy = strategy_map.get(choice, 'selective')
        
        # Ask for max functions
        max_funcs = input(f"\nMax functions to enhance (or Enter for all pending): ").strip()
        max_functions = int(max_funcs) if max_funcs.isdigit() else None
        
        print(f"\n🚀 Starting enhancement with strategy: {strategy}")
        if max_functions:
            print(f"📊 Limited to {max_functions} functions")
        
        # Run enhancement
        await enhancer.enhance_graph(
            strategy=strategy,
            max_functions=max_functions
        )
        
        # Show final stats
        final_stats = await enhancer.get_enhancement_stats()
        print(f"\n📈 Final Status:")
        print(f"   • Enhanced: {final_stats['enhanced_functions']}")
        print(f"   • Pending: {final_stats['pending_functions']}")
        if final_stats['avg_confidence']:
            print(f"   • Avg Confidence: {final_stats['avg_confidence']:.2f}")
        
        print("\n🎉 LLM enhancement completed!")
        print("🌐 View enhanced graph in Neo4j Browser: http://localhost:7474")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Full error details:")
    finally:
        enhancer.close()

if __name__ == "__main__":
    asyncio.run(main())
