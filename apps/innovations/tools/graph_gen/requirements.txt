# Code Graph RAG Generator Requirements - Two Phase

# Phase 1: Fast Graph Generation
# Tree-sitter for fast, accurate parsing
tree-sitter>=0.20.0
tree-sitter-python>=0.20.0
tree-sitter-javascript>=0.20.0
tree-sitter-typescript>=0.20.0

# Local embeddings for vectors
sentence-transformers>=2.2.0

# Neo4j driver for graph database
neo4j>=5.0.0

# Environment variables management
python-dotenv>=1.0.0

# Phase 2: LLM Enhancement (Optional)
# OpenAI for semantic analysis
openai>=1.0.0

# Async processing
asyncio-throttle>=1.0.0

# Additional utilities
numpy>=1.21.0
pathlib2>=2.3.0  # For older Python versions if needed
