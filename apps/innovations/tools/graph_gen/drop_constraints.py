#!/usr/bin/env python3
"""
Drop Neo4j Constraints

Drop existing constraints to avoid conflicts.
"""

import os
from neo4j import GraphDatabase

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Configuration
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

def drop_constraints():
    """Drop all constraints"""
    print("🗑️ Dropping Neo4j Constraints")
    print("=" * 30)
    
    try:
        driver = GraphDatabase.driver(
            NEO4J_URI,
            auth=(NEO4J_USER, NEO4J_PASSWORD)
        )
        
        with driver.session() as session:
            # List existing constraints
            result = session.run("SHOW CONSTRAINTS")
            constraints = list(result)
            
            print(f"📊 Found {len(constraints)} constraints")
            
            # Drop each constraint
            for constraint in constraints:
                constraint_name = constraint.get('name', 'unknown')
                try:
                    session.run(f"DROP CONSTRAINT {constraint_name}")
                    print(f"✅ Dropped constraint: {constraint_name}")
                except Exception as e:
                    print(f"⚠️  Could not drop {constraint_name}: {e}")
            
            print("✅ All constraints dropped!")
        
        driver.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    drop_constraints()

if __name__ == "__main__":
    main()
