#!/usr/bin/env python3
"""
Clean Neo4j Database

Simple script to clean the Neo4j database before running the graph generator.
"""

import os
from neo4j import GraphDatabase

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Configuration
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

def clean_database():
    """Clean the Neo4j database"""
    print("🧹 Cleaning Neo4j Database")
    print("=" * 30)
    
    print(f"🔧 Connecting to: {NEO4J_URI}")
    print(f"👤 User: {NEO4J_USER}")
    
    try:
        driver = GraphDatabase.driver(
            NEO4J_URI,
            auth=(NEO4J_USER, NEO4J_PASSWORD)
        )
        
        with driver.session() as session:
            # Get current stats
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()['node_count']
            
            result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            rel_count = result.single()['rel_count']
            
            print(f"📊 Current database:")
            print(f"   • Nodes: {node_count}")
            print(f"   • Relationships: {rel_count}")
            
            if node_count == 0 and rel_count == 0:
                print("✅ Database is already clean!")
                return
            
            # Auto-confirm deletion for automation
            print(f"\n🗑️  Auto-deleting all {node_count} nodes and {rel_count} relationships...")
            
            print("🗑️  Deleting all data...")
            
            # Delete all nodes and relationships
            session.run("MATCH (n) DETACH DELETE n")
            
            # Verify deletion
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            final_count = result.single()['node_count']
            
            if final_count == 0:
                print("✅ Database cleaned successfully!")
            else:
                print(f"⚠️  Warning: {final_count} nodes still remain")
        
        driver.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    clean_database()
    print(f"\n🚀 Ready to run: python fast_neo4j_generator.py")

if __name__ == "__main__":
    main()
