#!/usr/bin/env python3
"""
Complete Neo4j Cleanup

Thorough cleanup of Neo4j database including constraints, indexes, and all data.
"""

import os
from neo4j import GraphDatabase

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Configuration
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

def complete_cleanup():
    """Complete cleanup of Neo4j database"""
    print("🧹 Complete Neo4j Database Cleanup")
    print("=" * 40)
    
    try:
        driver = GraphDatabase.driver(
            NEO4J_URI,
            auth=(NEO4J_USER, NEO4J_PASSWORD)
        )
        
        with driver.session() as session:
            # Step 1: Check current state
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()['node_count']
            
            result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            rel_count = result.single()['rel_count']
            
            print(f"📊 Current state:")
            print(f"   • Nodes: {node_count}")
            print(f"   • Relationships: {rel_count}")
            
            # Step 2: Drop all constraints
            print(f"\n🗑️ Step 1: Dropping constraints...")
            try:
                result = session.run("SHOW CONSTRAINTS")
                constraints = list(result)
                print(f"   Found {len(constraints)} constraints")
                
                for constraint in constraints:
                    constraint_name = constraint.get('name', 'unknown')
                    try:
                        session.run(f"DROP CONSTRAINT {constraint_name}")
                        print(f"   ✅ Dropped: {constraint_name}")
                    except Exception as e:
                        print(f"   ⚠️  Could not drop {constraint_name}: {e}")
                        
            except Exception as e:
                print(f"   ⚠️  Error with constraints: {e}")
            
            # Step 3: Drop all indexes
            print(f"\n🗑️ Step 2: Dropping indexes...")
            try:
                result = session.run("SHOW INDEXES")
                indexes = list(result)
                print(f"   Found {len(indexes)} indexes")
                
                for index in indexes:
                    index_name = index.get('name', 'unknown')
                    try:
                        session.run(f"DROP INDEX {index_name}")
                        print(f"   ✅ Dropped: {index_name}")
                    except Exception as e:
                        print(f"   ⚠️  Could not drop {index_name}: {e}")
                        
            except Exception as e:
                print(f"   ⚠️  Error with indexes: {e}")
            
            # Step 4: Delete all data
            print(f"\n🗑️ Step 3: Deleting all data...")
            session.run("MATCH (n) DETACH DELETE n")
            
            # Step 5: Verify cleanup
            result = session.run("MATCH (n) RETURN count(n) as final_count")
            final_count = result.single()['final_count']
            
            result = session.run("MATCH ()-[r]->() RETURN count(r) as final_rel_count")
            final_rel_count = result.single()['final_rel_count']
            
            print(f"\n📊 Final state:")
            print(f"   • Nodes: {final_count}")
            print(f"   • Relationships: {final_rel_count}")
            
            if final_count == 0 and final_rel_count == 0:
                print("\n✅ Database completely cleaned!")
            else:
                print(f"\n⚠️  Warning: Still have {final_count} nodes and {final_rel_count} relationships")
        
        driver.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    complete_cleanup()
    print(f"\n🚀 Ready to run: python fast_neo4j_generator.py")

if __name__ == "__main__":
    main()
