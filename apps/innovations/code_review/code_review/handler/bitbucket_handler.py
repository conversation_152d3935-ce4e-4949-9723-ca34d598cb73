

import os
from typing import Any, Dict, Optional
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel
from code_review.workflow.bitbucket.bitbucket import base_workflow
from code_review.handler.handler import Hand<PERSON><PERSON>ithCheckpointer
from code_review.workflow.bitbucket.state import BitbucketState
from langfuse.decorators import observe,langfuse_context

class BitbucketInput(BaseModel):
    pull_request_id: str
    repository_slug: str
    workspace_slug: str
    comment: Optional[Dict[str,Any]] = None


class BitbucketHandler(HandlerWithCheckpointer[BitbucketState,BitbucketInput]):

    def __init__(self):
        super().__init__()
        self.bitbucket_username = os.getenv("BITBUCKET_USERNAME")
        self.bitbucket_app_password = os.getenv("BITBUCKET_PASSWORD")
    
    
    def parse_state(self,state:Dict)->BitbucketState:
        retState = BitbucketState(**state)
        retState.context=dict(state)
        return retState
    
    def get_workflow(self,checkpointer):
        return base_workflow(
            checkpointer=checkpointer
        )

    def init_state(self,input:BitbucketInput) -> BitbucketState:

        pr_id = input.pull_request_id
        repository = input.repository_slug
        workspace = input.workspace_slug

        review_description = f"Perform a code review on PR #{pr_id} in repository {repository} in workspace {workspace}"
        messages = [
                {
                    "role": "user",
                    "content": review_description
                }
            ]
    
        return BitbucketState(
            pull_request_id=pr_id,
            repository_slug=repository,
            workspace_slug=workspace,
            messages=messages,
            app_password=self.bitbucket_app_password,
            username=self.bitbucket_username,
            comment=input.comment
        )

    @observe
    def get_config(self,input:BitbucketInput) -> RunnableConfig:
        session_id = f"{input.workspace_slug}-{input.repository_slug}-{input.pull_request_id}".replace("-", "").replace("_", "")
        if input.comment:
            session_id = f"{session_id}-{input.comment.get('id',0)}"
        return {
            "run_id": langfuse_context.get_current_trace_id(),
            "callbacks": [self.get_langfuse_handler(session_id)],
            "configurable": {
                "thread_id": session_id,
            }
        }

    
    