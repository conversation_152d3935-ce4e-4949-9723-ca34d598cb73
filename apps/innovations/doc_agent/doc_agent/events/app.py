import os
from typing import Any, Dict
import uuid

from doc_agent.handlers.doc_agent_handler import DocAgentHandler,DocAgentInput
from ..infra import init
from kafka.manager import KafkaManager
from confluent_kafka.cimpl import Message
from logger import logger

env = os.environ.get("ENV", "local")
group_id = uuid.uuid4() if env == "local" else f"siem_rules"

kafka_manager = KafkaManager(config=init.secret.kafka, group_id=group_id)



@kafka_manager.kafka_event(topics="doc_agent.topic_name")
async def doc_agent_handler(data: Dict[str, Any], message: Message):
    # Add UUID to the message data
    handler = DocAgentHandler()
    await handler.run(DocAgentInput(**data))




if __name__ == "__main__":
    kafka_manager.start_consuming()
